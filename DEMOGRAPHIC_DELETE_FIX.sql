-- =============================================
-- FIX FOR DEMOGRAPHIC DATA DELETION ISSUE
-- =============================================
-- 
-- PROBLEM: Teachers cannot delete demographic data due to missing RLS policy
-- SOLUTION: Add DELETE policy for teachers on user_demographic_responses table
--
-- INSTRUCTIONS:
-- 1. Go to your Supabase Dashboard
-- 2. Navigate to SQL Editor
-- 3. Run the SQL below
-- 4. Test the delete functionality in the admin panel

-- Add policy for teachers to delete demographic responses
CREATE POLICY "Teachers can delete all responses"
  ON public.user_demographic_responses
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  );

-- Optional: Grant explicit DELETE permission (usually not needed with RLS)
-- GRANT DELETE ON public.user_demographic_responses TO authenticated;

-- Verify the policy was created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename = 'user_demographic_responses'
ORDER BY policyname;
