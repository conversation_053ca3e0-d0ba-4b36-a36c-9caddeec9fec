# IVCAN Course LMS

A modern Learning Management System built with React, Vite, and Supabase.

## How can I edit this code?

There are several ways of editing your application.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Set up environment variables
cp .env.example .env
# Edit .env with your Supabase credentials

# Step 4: Install the necessary dependencies.
npm i

# Step 5: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase (Auth, Database, Storage)
- Framer Motion
- React Query
- React Hook Form
- Zod

## Features

- User authentication with Supabase Auth
- Course management
- Module and lesson tracking
- Quiz functionality
- User progress tracking
- Teacher/admin dashboard
- Dark and light mode
- Responsive design

## Progress Tracking System

The LMS includes a robust progress tracking system that has been significantly improved:

- Consistent column naming with standardized `is_completed` fields
- Automatic module completion based on lesson progress
- Automatic course completion based on module progress
- Achievement/badge system integration
- PostgreSQL triggers for accurate progress calculation

For detailed information about the progress tracking system, including troubleshooting and maintenance, see the [Progress Tracking Documentation](docs/progress-tracking.md).

To fix user progress issues:

```bash
# Fix progress for the current authenticated user
npm run fix:user-progress

# Fix progress for all users (requires service role key)
npm run fix:all-progress
```

## Environment Configuration

This project uses environment variables for configuration. See [Environment Configuration Guide](docs/ENVIRONMENT_CONFIG.md) for details.

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=https://your-project-url.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

## Database Migrations

This project uses Supabase for the database and includes a standardized migration process. See [Database Migrations Guide](docs/DATABASE_MIGRATIONS.md) for details.

## Error Handling

The application uses a centralized error handling system to provide consistent error messages and logging. See [Error Handling Guide](docs/ERROR_HANDLING.md) for details.

## Code Organization

The project follows a feature-based organization structure. See [Code Organization Guide](docs/CODE_ORGANIZATION.md) for details.

```bash
# Check code organization
npm run organize:code

# Check for unused files
npm run organize:check-unused
```

1. Create a new migration:
   ```bash
   npm run migrate:create -- --name=your_migration_name
   ```

2. Apply migrations to your local development database:
   ```bash
   npm run migrate
   ```

3. Apply migrations to the remote production database:
   ```bash
   npm run migrate:remote
   ```

## Troubleshooting

### Module Completion Issues

If modules are incorrectly marked as completed for students, you can:

1. Reset module completion for a specific user:

```bash
npm run reset:module-completion <user_id>
```

2. Reset all module completions for all users:

```bash
npm run reset:all-module-completions
```

3. Fix course completion issues:

```bash
# Fix a specific course for a user
npm run fix:course-completion <user_id> <course_id>

# Fix all courses for a user
npm run fix:course-completion <user_id> --all
```

4. Disable auto-completion for all students:

```bash
npm run disable:auto-completion
```

5. Fix incorrect course completion records:

```bash
npm run fix:incorrect-completions
```

6. Fix lesson progress and update module completion status:

```bash
# Fix for a specific user
npm run fix:lesson-progress <user_id>

# Fix for all users
npm run fix:all-lesson-progress
```

7. Create the user_module_progress table in Supabase:

```bash
npm run create:module-progress-table
```

Alternatively, you can create the table directly in the Supabase SQL Editor by copying and pasting the SQL from the `scripts/create-module-progress-table.sql` file.

8. Fix all issues at once:

```bash
npm run fix:all-issues
```

9. Fix Supabase storage RLS policies:

```bash
npm run fix:storage-rls
```

10. Run SQL to create storage buckets and RLS policies directly in Supabase:

```bash
npm run run:storage-sql
```

For more details, see the [Module Completion Fix Guide](docs/MODULE_COMPLETION_FIX.md).

### Course Completion Issues

If courses are not being marked as completed correctly, you can:

1. Fix course completion for a specific user:

```bash
npm run fix:course-completion <user_id> <course_id>
```

For more details, see the [Course Completion Fix Guide](docs/COURSE_COMPLETION_FIX.md).

### Supabase Connection Issues

If you see the message "Using cached login due to Supabase connection issues", follow these steps:

1. Run the connection checker:

```bash
npm run fix:connection
```

2. Check your environment variables in `.env` or `.env.local`
3. Clear your browser cache and storage
4. Restart the development server

For detailed instructions, see [Fixing Supabase Connection Issues](docs/SUPABASE-CONNECTION.md).
