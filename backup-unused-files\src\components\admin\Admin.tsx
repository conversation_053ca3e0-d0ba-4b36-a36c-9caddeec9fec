import React from 'react';
import { Course } from './Course';
import { Module } from './Module';
import { Lesson } from './Lesson';

interface AdminProps {
  selectedCourseId: string | null;
  setSelectedCourseId: (id: string | null) => void;
  selectedModuleId: string | null;
  setSelectedModuleId: (id: string | null) => void;
  selectedLessonId: string | null;
  setSelectedLessonId: (id: string | null) => void;
  isAddingModule: boolean;
  setIsAddingModule: (isAdding: boolean) => void;
  isAddingLesson: boolean;
  setIsAddingLesson: (isAdding: boolean) => void;
  handleBackToCoursesList: () => void;
  handleBackToCourse: () => void;
}

const Admin: React.FC<AdminProps> = ({
  selectedCourseId,
  setSelectedCourseId,
  selectedModuleId,
  setSelectedModuleId,
  selectedLessonId,
  setSelectedLessonId,
  isAddingModule,
  setIsAddingModule,
  isAddingLesson,
  setIsAddingLesson,
  handleBackToCoursesList,
  handleBackToCourse,
}) => {
  return (
    <div>
      {/* Render components based on the selected IDs and states */}
      {selectedCourseId && (
        <Course
          courseId={selectedCourseId}
          setSelectedModuleId={setSelectedModuleId}
          setSelectedLessonId={setSelectedLessonId}
          handleBackToCoursesList={handleBackToCoursesList}
        />
      )}
      {selectedModuleId && (
        <Module
          moduleId={selectedModuleId}
          setSelectedLessonId={setSelectedLessonId}
          handleBackToCourse={handleBackToCourse}
        />
      )}
      {selectedLessonId && <Lesson lessonId={selectedLessonId} />}
      {isAddingModule && <div>Adding Module...</div>}
      {isAddingLesson && <div>Adding Lesson...</div>}
    </div>
  );
};

export default Admin;