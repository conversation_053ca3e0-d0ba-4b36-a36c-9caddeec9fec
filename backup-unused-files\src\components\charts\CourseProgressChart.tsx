import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  TooltipProps
} from 'recharts';
import { useTheme } from '@/components/theme/theme-provider';
import { cn } from '@/lib/utils';
import { useMotion } from '@/context/MotionContext';

interface CourseProgressData {
  name: string;
  progress: number;
  hoursSpent: number;
}

interface CourseProgressChartProps {
  data: CourseProgressData[];
  className?: string;
  height?: number;
}

export function CourseProgressChart({ data, className, height = 300 }: CourseProgressChartProps) {
  const { theme } = useTheme();
  const { shouldReduceMotion } = useMotion();
  const isDark = theme === 'dark';

  // Colors based on theme
  const colors = {
    primary: isDark ? '#60a5fa' : '#3b82f6',
    secondary: isDark ? '#4f46e5' : '#6366f1',
    accent: isDark ? '#f97316' : '#f59e0b',
    background: isDark ? '#1e293b' : '#f8fafc',
    border: isDark ? '#334155' : '#e2e8f0',
    text: isDark ? '#94a3b8' : '#64748b',
    grid: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card p-3 rounded-lg shadow-md border border-border/40 text-sm">
          <p className="font-medium text-foreground mb-1">{label}</p>
          <p className="text-primary">
            Progress: {payload[0].value}%
          </p>
          <p className="text-accent">
            Hours Spent: {payload[1]?.payload?.hoursSpent || 0}
          </p>
        </div>
      );
    }
    return null;
  };

  // Get color based on progress
  const getBarColor = (progress: number) => {
    if (progress < 25) return '#f87171'; // Red
    if (progress < 50) return '#fb923c'; // Orange
    if (progress < 75) return '#facc15'; // Yellow
    return '#4ade80'; // Green
  };

  return (
    <div className={cn("w-full bg-card p-4 rounded-xl border border-border/40 shadow-sm", className)}>
      <h3 className="text-lg font-medium mb-4 text-foreground">Course Progress</h3>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={data}
          margin={{ top: 10, right: 10, left: 0, bottom: 30 }}
          barGap={8}
          barSize={24}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} vertical={false} />
          <XAxis
            dataKey="name"
            axisLine={{ stroke: colors.border }}
            tick={{ fill: colors.text, fontSize: 12 }}
            tickLine={false}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            axisLine={{ stroke: colors.border }}
            tick={{ fill: colors.text, fontSize: 12 }}
            tickLine={false}
            domain={[0, 100]}
            tickCount={6}
            label={{ 
              value: 'Progress (%)', 
              angle: -90, 
              position: 'insideLeft',
              style: { fill: colors.text, fontSize: 12 }
            }}
          />
          <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }} />
          <Bar
            dataKey="progress"
            radius={[4, 4, 0, 0]}
            animationDuration={shouldReduceMotion ? 0 : 1500}
            animationBegin={shouldReduceMotion ? 0 : 300}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry.progress)} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
