/**
 * Basic image handling utility
 * This is a simplified approach that prioritizes reliability over features
 */

/**
 * Convert a file to a data URL
 * This is the most direct approach to handle images
 * 
 * @param file The file to convert
 * @returns A Promise that resolves to the data URL
 */
export function fileToDataUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to data URL'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * Resize an image to a maximum width and height
 * 
 * @param dataUrl The data URL of the image
 * @param maxWidth The maximum width
 * @param maxHeight The maximum height
 * @returns A Promise that resolves to the resized data URL
 */
export function resizeImage(
  dataUrl: string, 
  maxWidth: number = 600, 
  maxHeight: number = 400
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      const width = img.width;
      const height = img.height;
      
      if (width > maxWidth) {
        height = Math.round(height * (maxWidth / width));
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = Math.round(width * (maxHeight / height));
        height = maxHeight;
      }
      
      // Create canvas and draw image
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }
      
      // Fill with white background
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);
      
      // Draw image
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert to data URL
      const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.8);
      resolve(resizedDataUrl);
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    img.src = dataUrl;
  });
}

/**
 * Process an image file for storage
 * This function converts the file to a data URL and resizes it
 * 
 * @param file The image file to process
 * @returns A Promise that resolves to the processed data URL
 */
export async function processImageBasic(file: File): Promise<string> {
  try {
    // Convert file to data URL
    const dataUrl = await fileToDataUrl(file);
    
    // Resize the image
    const resizedDataUrl = await resizeImage(dataUrl);
    
    return resizedDataUrl;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
}
