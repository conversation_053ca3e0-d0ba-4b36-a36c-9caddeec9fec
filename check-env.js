// check-env.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the .env file
const envPath = path.join(__dirname, '.env');
const envContent = fs.readFileSync(envPath, 'utf8');

// Check if the keys are properly formatted
const lines = envContent.split('\n');
for (const line of lines) {
  if (line.trim() === '') continue;
  
  const parts = line.split('=');
  if (parts.length < 2) {
    // console.error(`Malformed line: ${line}`);
  } else {
    const key = parts[0];
    const value = parts.slice(1).join('=');
    // console.log(`Key: ${key}, Value length: ${value.length}`);
  }
}
