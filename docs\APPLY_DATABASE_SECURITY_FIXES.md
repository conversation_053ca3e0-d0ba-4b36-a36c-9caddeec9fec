# How to Apply Database Security Fixes

## 🔒 **Quick Start Guide**

This guide shows you how to apply the database security fixes to resolve the Supabase linter warnings.

---

## 📋 **What These Fixes Do**

The security fixes address **18 database functions** that have mutable search_path warnings:

- `update_timestamp`
- `handle_new_user_role` 
- `get_lesson_navigation`
- `health_check`
- `get_tables_with_rls`
- `update_module_completion`
- `update_module_progress_cache`
- `refresh_schema_cache`
- `set_completed_at`
- `complete_course`
- `exec_sql`
- `execute_sql`
- `mark_lesson_completed`
- `check_module_completion`
- `mark_notification_read`
- `mark_all_notifications_read`
- `has_role`
- `assign_role`
- `handle_new_user`

**Security Issue**: These functions use `SECURITY DEFINER` but don't have `SET search_path` parameter, making them vulnerable to search path manipulation attacks.

**Fix**: Add `SET search_path = public` to all functions to prevent attacks.

---

## 🚀 **Method 1: Supabase Dashboard (Recommended)**

### **Step 1: Open Supabase Dashboard**
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project
3. Navigate to **SQL Editor**

### **Step 2: Apply the Migration**
1. Copy the entire contents of `supabase/migrations/20250102001_fix_function_search_path_security.sql`
2. Paste it into the SQL Editor
3. Click **Run** to execute the migration

### **Step 3: Verify the Fixes**
1. Go to **Database > Linter** in the dashboard
2. Check that the "Function Search Path Mutable" warnings are resolved
3. Test that your application still works correctly

---

## 🛠️ **Method 2: Using Scripts (If you have service role key)**

### **Step 1: Set Environment Variable**
```bash
# Add your service role key to .env file
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### **Step 2: Run the Fix Script**
```bash
npm run fix:database-security
```

This script will:
- Apply the security migration automatically
- Test function security
- Verify function calls work correctly
- Provide detailed results

---

## 🔧 **Method 3: Manual SQL Execution**

If you prefer to apply the fixes manually:

### **Step 1: Connect to your database**
Use your preferred PostgreSQL client (pgAdmin, DBeaver, etc.)

### **Step 2: Execute the migration**
Run the SQL from `supabase/migrations/20250102001_fix_function_search_path_security.sql`

### **Step 3: Verify the changes**
```sql
-- Check that functions have proper search_path
SELECT 
  proname as function_name,
  prosecdef as is_security_definer,
  proconfig as config_settings
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  AND proname IN (
    'update_timestamp', 'handle_new_user_role', 'get_lesson_navigation',
    'health_check', 'get_tables_with_rls', 'update_module_completion'
    -- ... add other function names
  );
```

---

## ✅ **Verification Steps**

After applying the fixes:

### **1. Check Supabase Linter**
- Go to Supabase Dashboard > Database > Linter
- Verify "Function Search Path Mutable" warnings are gone

### **2. Test Application Functionality**
- Test course completion
- Test lesson progress tracking  
- Test user authentication
- Test role assignments
- Verify no errors in browser console

### **3. Test Basic Functions**
```bash
# If you have the verification script set up
npm run verify:rpc-functions
```

---

## 🔒 **Enable Leaked Password Protection**

To fix the remaining auth warning:

### **Step 1: Go to Auth Settings**
1. Open Supabase Dashboard
2. Navigate to **Authentication > Settings**

### **Step 2: Enable Password Protection**
1. Find "Password Security" section
2. Enable "Leaked Password Protection"
3. Save changes

This enables checking against HaveIBeenPwned.org database of compromised passwords.

---

## 🚨 **Important Notes**

### **Before Applying Fixes**:
- ✅ **Backup your database** (Supabase handles this automatically)
- ✅ **Test in development first** if possible
- ✅ **Notify users** of potential brief downtime

### **After Applying Fixes**:
- ✅ **Test all application features**
- ✅ **Check for any errors in logs**
- ✅ **Verify Supabase linter shows no warnings**
- ✅ **Monitor application performance**

### **If Something Goes Wrong**:
- Check Supabase Dashboard logs for errors
- Verify your service role key has proper permissions
- Test individual functions using SQL Editor
- Contact support if needed

---

## 📊 **Expected Results**

### **Before Fixes**:
- ❌ 18 function search_path warnings
- ❌ Leaked password protection disabled
- ❌ Potential security vulnerabilities

### **After Fixes**:
- ✅ All function search_path warnings resolved
- ✅ Functions protected against search path attacks
- ✅ Leaked password protection can be enabled
- ✅ Improved database security posture

---

## 🎯 **Next Steps**

After completing the database security fixes:

1. **Enable leaked password protection** (see instructions above)
2. **Run regular security audits** using Supabase linter
3. **Monitor application logs** for any issues
4. **Move on to next medium priority task** (Certificate Generation)

**Your database is now significantly more secure!** 🔒
