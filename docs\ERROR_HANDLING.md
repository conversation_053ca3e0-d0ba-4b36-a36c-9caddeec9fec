# Error Handling Guide

This document explains how to use the centralized error handling system in the IVCAN Course LMS project.

## Overview

The error handling system provides a standardized way to handle errors across the application. It includes:

- Error type detection
- Severity level determination
- User-friendly error messages
- Error logging
- Toast notifications

## Using the Error Service

### Basic Usage

The simplest way to handle an error is to use the `handleError` function:

```typescript
import { handleError } from '@/services/error/errorService';

try {
  // Your code that might throw an error
} catch (error) {
  handleError(error, {
    userId: user.id,
    action: 'fetchCourses',
    component: 'CourseList'
  });
}
```

### Using the Hook in React Components

For React components, use the `useErrorHandler` hook:

```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler';

function CourseList() {
  const { handleError, executeWithHandling } = useErrorHandler({
    componentName: 'CourseList'
  });
  
  const fetchCourses = async () => {
    try {
      // Your code that might throw an error
    } catch (error) {
      handleError(error, { action: 'fetchCourses' });
    }
  };
  
  // Or use the executeWithHandling helper
  const loadCourses = async () => {
    await executeWithHandling(
      async () => {
        // Your code that might throw an error
      },
      'loadCourses'
    );
  };
  
  return (
    // Your component JSX
  );
}
```

### Executing with Default Values

When you want to provide a default value in case of an error:

```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler';

function CourseList() {
  const { executeWithDefaultValue } = useErrorHandler({
    componentName: 'CourseList'
  });
  
  const [courses, setCourses] = useState([]);
  
  const loadCourses = async () => {
    const result = await executeWithDefaultValue(
      async () => {
        // Code that might throw an error
        const { data } = await supabase.from('courses').select('*');
        return data;
      },
      [], // Default empty array if an error occurs
      'loadCourses'
    );
    
    setCourses(result);
  };
  
  return (
    // Your component JSX
  );
}
```

## Error Types and Severity

The error service automatically categorizes errors into different types:

- `DATABASE`: Errors from Supabase or other database operations
- `NETWORK`: Network connectivity issues
- `AUTHENTICATION`: Authentication-related errors
- `VALIDATION`: Input validation errors
- `PERMISSION`: Permission or authorization errors
- `UNKNOWN`: Any other errors

Each error type is assigned a severity level:

- `INFO`: Informational messages
- `WARNING`: Warnings that don't prevent the application from functioning
- `ERROR`: Errors that prevent a specific operation from completing
- `CRITICAL`: Serious errors that might affect the entire application

## Creating Contextual Error Handlers

You can create error handlers with pre-defined context:

```typescript
import { createErrorHandler } from '@/services/error/errorService';

// Create a handler for a specific module
const handleCourseError = createErrorHandler({
  component: 'CourseModule',
  action: 'courseOperation'
});

// Use it later
try {
  // Your code
} catch (error) {
  handleCourseError(error, { 
    additionalData: { courseId: '123' }
  });
}
```

## Best Practices

1. **Always provide context**: Include as much context as possible when handling errors
2. **Use the hook in components**: Use `useErrorHandler` in React components
3. **Handle async errors**: Always handle errors in async functions
4. **Don't expose technical details**: The error service automatically creates user-friendly messages
5. **Log important errors**: Critical errors are automatically logged

## Error Boundaries

For React component errors, use the error boundary components:

```tsx
import { ErrorBoundary } from '@/components/error-boundaries/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <YourComponent />
    </ErrorBoundary>
  );
}
```

For Supabase-specific errors, use the Supabase error boundary:

```tsx
import { SupabaseErrorBoundary } from '@/components/error-boundaries/SupabaseErrorBoundary';

function DataComponent() {
  return (
    <SupabaseErrorBoundary>
      <ComponentThatUsesSupabase />
    </SupabaseErrorBoundary>
  );
}
```

## Troubleshooting

If you're seeing unhandled errors:

1. Make sure you're wrapping async code in try/catch blocks
2. Use error boundaries for component rendering errors
3. Check that you're using the error handling hooks correctly
