# Fixing the `completed_at` Column Issue

This document provides instructions on how to fix the issue with the `completed_at` column in the `user_course_enrollment` table.

## The Issue

The error message indicates that the system cannot find the `completed_at` column in the `user_course_enrollment` table in the schema cache. This can happen due to:

1. The column not being properly created in the database
2. The schema cache being out of sync with the actual database schema
3. Permissions issues with the column

## Solution

We've implemented a multi-layered approach to fix this issue:

### 1. Database Migrations

Two migration files have been created:

- `20250901010_fix_completed_at_column.sql`: Ensures the `completed_at` column exists and updates the schema cache
- `20250901011_create_execute_sql_function.sql`: Creates a function to execute SQL directly as a last resort

### 2. Code Changes

The `completionService.ts` file has been updated to:

- Add fallback mechanisms that try to update/insert without the `completed_at` column if the initial attempt fails
- Add a direct SQL approach as a last resort using the `execute_sql` function

### 3. Application Script

A script has been created to apply the migrations:

- `scripts/apply-fix-migrations.js`: Applies the fix migrations to the database

## How to Apply the Fix

### Option 1: Using the Script (Recommended)

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Set the DATABASE_URL environment variable:
   ```
   # For Windows PowerShell
   $env:DATABASE_URL="postgresql://postgres:your-password@localhost:54322/postgres"
   
   # For Windows Command Prompt
   set DATABASE_URL=postgresql://postgres:your-password@localhost:54322/postgres
   ```

3. Run the script:
   ```
   node scripts/apply-fix-migrations.js
   ```

### Option 2: Manual Application

1. Apply the migrations manually using the Supabase CLI:
   ```
   npx supabase db push --db-url your-database-url --migration-file supabase/migrations/20250901010_fix_completed_at_column.sql
   npx supabase db push --db-url your-database-url --migration-file supabase/migrations/20250901011_create_execute_sql_function.sql
   ```

2. Restart your application to ensure the changes take effect.

## Verifying the Fix

After applying the fix, you should be able to mark courses as completed without encountering the error. To verify:

1. Navigate to a course that you've completed
2. Click the "Mark as Completed" button
3. Check the console for any errors

If the course is marked as completed successfully, the fix has been applied correctly.

## Troubleshooting

If you continue to experience issues:

1. Check the browser console for specific error messages
2. Verify that the migrations were applied successfully by checking the database schema
3. Try clearing your browser cache and reloading the application
4. Ensure that the Supabase client is properly configured with the correct URL and API key
