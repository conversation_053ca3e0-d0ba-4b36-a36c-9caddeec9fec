# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for both local development and production environments.

## Prerequisites

- A Google Cloud Platform account
- Access to your Supabase project

## Step 1: Create OAuth Credentials in Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" and select "OAuth client ID"
5. Select "Web application" as the application type
6. Add a name for your OAuth client (e.g., "IVCAN Course LMS")

### For Local Development

7. Add authorized JavaScript origins:
   - `http://localhost:8080`

8. Add authorized redirect URIs:
   - `http://localhost:8080/auth/callback`
   - `https://jibspqwieubavucdtccv.supabase.co/auth/v1/callback`

### For Production

9. Add additional authorized JavaScript origins:
   - Your production domain (e.g., `https://ivcan-iv.netlify.app`)

10. Add additional authorized redirect URIs:
    - `https://ivcan-iv.netlify.app/auth/callback`
    - `https://jibspqwieubavucdtccv.supabase.co/auth/v1/callback`

11. Click "Create"
12. Note down the Client ID and Client Secret

### Important Notes for Google OAuth

- Make sure to add **both** the Supabase callback URL and your application callback URL
- The callback URLs must match exactly, including trailing slashes
- For local development, use `http://localhost:8080` not `localhost:8080`
- For production, use `https://ivcan-iv.netlify.app` not `ivcan-iv.netlify.app`

## Step 2: Configure Supabase Auth Settings

1. Go to your [Supabase Dashboard](https://app.supabase.io/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list of providers and click "Edit"
5. Enable the provider by toggling the switch
6. Enter the Client ID and Client Secret from Google Cloud Console
7. Save the changes

## Step 3: Configure Site URL and Redirect URLs in Supabase

1. In your Supabase dashboard, go to "Authentication" > "URL Configuration"
2. Set the Site URL:
   - For development: `http://localhost:8080`
   - For production: Your production domain (e.g., `https://ivcan-course-lms.netlify.app`)
3. Add Redirect URLs:
   - For development: `http://localhost:8080/auth/callback`
   - For production: `https://ivcan-course-lms.netlify.app/auth/callback`
4. Save the changes

## Step 4: Test the Integration

### Local Development

1. Run your application in development mode (`npm run dev`)
2. Navigate to the login page
3. Click the "Log in with Google" button
4. You should be redirected to Google's authentication page
5. After successful authentication, you should be redirected back to your application

### Production

1. Deploy your application to Netlify
2. Navigate to the login page on your production site
3. Click the "Log in with Google" button
4. You should be redirected to Google's authentication page
5. After successful authentication, you should be redirected back to your application

## Troubleshooting

If you encounter issues with Google OAuth, check the following:

1. **Redirect URI Mismatch**: Ensure that the redirect URIs in Google Cloud Console match the ones in your application and Supabase settings.
2. **JavaScript Origins**: Make sure your application's domain is listed in the authorized JavaScript origins in Google Cloud Console.
3. **Client ID and Secret**: Verify that the Client ID and Secret in Supabase match those from Google Cloud Console.
4. **Browser Console Errors**: Check the browser console for any error messages that might provide more information.
5. **Supabase Logs**: Check the Supabase logs for authentication-related errors.
6. **CORS Issues**: If you see CORS errors, make sure your domain is properly configured in both Google Cloud Console and Supabase.

## Common Errors and Solutions

### "redirect_uri_mismatch" Error

This error occurs when the redirect URI used in the OAuth request doesn't match any of the authorized redirect URIs in Google Cloud Console.

**Solution**: Add the exact redirect URI to the authorized redirect URIs list in Google Cloud Console.

### "invalid_client" Error

This error occurs when the client ID or client secret is incorrect.

**Solution**: Verify that the client ID and client secret in Supabase match those from Google Cloud Console.

### "access_denied" Error

This error occurs when the user denies access to their Google account.

**Solution**: This is a user choice. You can provide more information about why your application needs access to their Google account.

### "CORS Error" in Browser Console

This error occurs when the browser blocks requests due to Cross-Origin Resource Sharing (CORS) restrictions.

**Solution**: Make sure your domain is listed in the authorized JavaScript origins in Google Cloud Console.
