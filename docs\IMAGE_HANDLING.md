# Image Handling Best Practices

This document outlines the best practices for handling module images in the IVCAN Course LMS application.

## Overview

Our application uses a robust approach to image handling with the following key features:

1. Standardized storage strategy using Supabase Storage
2. Image validation and optimization
3. Enhanced database schema for image metadata
4. Efficient image loading with the OptimizedImage component
5. Automatic cleanup of orphaned images
6. Image transformation capabilities for responsive design

## Image Storage Architecture

### Directory Structure

Images are stored in Supabase Storage using the following structured approach:

```
module-images/
  ├── courses/
  │   ├── [course-id]/
  │   │   ├── modules/
  │   │   │   ├── [module-id]/
  │   │   │   │   ├── [timestamp].[extension]
```

This structure provides:
- Clear organization by course and module
- Easy management of images at various levels
- Simplified cleanup of related resources

### Database Schema

The `modules` table includes the following fields for image handling:

- `image_url`: URL to the stored image
- `image_width`: Width of the image in pixels
- `image_height`: Height of the image in pixels
- `image_alt_text`: Accessibility description for the image
- `image_size_bytes`: Size of the image in bytes

## Client-Side Implementation

### Image Uploads

All image uploads use the centralized `uploadModuleImage` function in `src/lib/image-utils.ts`:

```typescript
export async function uploadModuleImage(
  file: File, 
  courseId: string,
  moduleId?: string
): Promise<string | null>
```

Key features:
- Strict validation of image types and sizes
- Structured storage paths
- Retry logic with exponential backoff
- Comprehensive error handling

### Image Display

Use the `OptimizedImage` component (`src/components/ui/optimized-image.tsx`) to display images:

```tsx
<OptimizedImage
  src={imageUrl}
  alt="Module description"
  className="custom-class"
  fallbackSrc="/path/to/fallback.jpg" // Optional
  priority={false} // Set to true for above-the-fold images
/>
```

Benefits:
- Lazy loading for improved performance
- Loading skeleton while images are fetched
- Error handling with fallback images
- Consistent styling and accessibility

### Image Transformations

Use the `transformImageUrl` function to optimize images for different contexts:

```typescript
const optimizedUrl = transformImageUrl(originalUrl, {
  width: 800,
  height: 600,
  format: 'webp',
  quality: 80
});
```

## Maintenance and Cleanup

The application automatically cleans up orphaned images that are no longer referenced in the database:

1. The cleanup job runs once every 24 hours in production
2. Only users with admin privileges can trigger cleanup
3. Images are compared against database references before deletion
4. Detailed logs are kept of all cleanup operations

To manually trigger cleanup:

```typescript
import { CleanupTasks } from '@/utils/cleanup-tasks';

// Run all cleanup tasks
await CleanupTasks.runAllCleanupTasks();

// Or specifically clean up module images
import { cleanupOrphanedModuleImages } from '@/lib/image-utils';
await cleanupOrphanedModuleImages();
```

## Security Considerations

1. Image uploads are restricted to authenticated users only
2. File type validation prevents malicious file uploads
3. Size limits prevent DoS attacks via large file uploads
4. Bucket policies enforce appropriate access controls

## Performance Optimization

1. Images are served through Supabase's CDN network
2. WebP format is used when supported by the browser
3. Image dimensions are responsive to device size
4. Low-quality image placeholders show during loading

## Accessibility

1. All images require alt text for screen readers
2. The `OptimizedImage` component enforces alt text property
3. Fallback text is provided when images fail to load
4. Color contrast is maintained for image overlays

## Implementation Checklist

When implementing new image handling features:

- [ ] Use the `validateImage` function for all image validations
- [ ] Use `OptimizedImage` component for displaying images
- [ ] Store additional metadata for each image
- [ ] Implement proper error handling
- [ ] Use structured storage paths
- [ ] Provide fallback images
- [ ] Ensure appropriate access controls