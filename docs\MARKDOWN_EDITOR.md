# Advanced Markdown Editor System

A comprehensive, Notion-like markdown editor built with TipTap and React, featuring GitHub Flavored Markdown support, live preview, and advanced content creation features.

## 🚀 Features

### Core Editing Features
- **Rich Text Editing**: WYSIWYG editing with markdown output
- **Live Preview**: Real-time preview with split-pane and full-screen modes
- **GitHub Flavored Markdown**: Full GFM support including tables, task lists, strikethrough
- **Syntax Highlighting**: Code blocks with syntax highlighting for 20+ languages
- **Image Upload**: Drag & drop image upload with Supabase storage integration
- **Table Editing**: Visual table creation and editing with resizable columns

### Advanced Content Features
- **Task Lists**: Interactive checkboxes with nested support
- **Callouts**: Info, warning, success, error, and tip callouts
- **Collapsible Sections**: Details/summary elements for organizing content
- **YouTube Embeds**: Direct YouTube video embedding
- **Mathematical Expressions**: Support for inline and block math
- **Highlight Text**: ==highlighted text== syntax support

### User Experience
- **Dark Mode**: Full dark mode support with theme toggle
- **Responsive Design**: Mobile-optimized interface
- **Keyboard Shortcuts**: Common editing shortcuts (Ctrl+B, Ctrl+I, etc.)
- **Fullscreen Mode**: Distraction-free writing experience
- **Export Options**: Export to markdown with copy/download functionality
- **Image Zoom**: Click to zoom images in preview mode
- **Code Copy**: One-click code copying from code blocks

## 📦 Installation

The editor system is already integrated into your project. The main components are:

```
src/
├── components/ui/
│   ├── advanced-markdown-editor.tsx     # Main editor component
│   └── enhanced-markdown-preview.tsx    # Preview component
├── lib/
│   ├── tiptap-extensions/               # Custom TipTap extensions
│   │   ├── details.ts                   # Collapsible sections
│   │   └── callout.ts                   # Callout/admonition blocks
│   ├── advanced-markdown-serializer.ts  # Markdown conversion
│   └── markdown-utils.ts                # Utility functions
└── styles/
    └── obsidian-markdown.css            # Obsidian-inspired styles
```

## 🎯 Quick Start

### Basic Usage

```tsx
import { AdvancedMarkdownEditor } from '@/components/ui/advanced-markdown-editor';

function MyComponent() {
  const [content, setContent] = useState('');
  
  return (
    <AdvancedMarkdownEditor
      initialContent={content}
      onChange={setContent}
      placeholder="Start writing..."
      showToolbar={true}
      showPreview={true}
    />
  );
}
```

### With Image Upload Support

```tsx
<AdvancedMarkdownEditor
  initialContent={content}
  onChange={setContent}
  courseId="course-123"
  moduleId="module-456"
  placeholder="Start writing..."
  showToolbar={true}
  showPreview={true}
  minHeight={600}
/>
```

### Minimal Editor (No Preview)

```tsx
<AdvancedMarkdownEditor
  initialContent={content}
  onChange={setContent}
  showToolbar={true}
  showPreview={false}
  className="border rounded-lg"
/>
```

## 🔧 Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialContent` | `string` | `''` | Initial markdown content |
| `onChange` | `(markdown: string) => void` | - | Callback when content changes |
| `placeholder` | `string` | `'Start writing...'` | Placeholder text |
| `className` | `string` | `''` | Additional CSS classes |
| `minHeight` | `number` | `500` | Minimum editor height in pixels |
| `autoFocus` | `boolean` | `false` | Auto-focus the editor on mount |
| `courseId` | `string` | - | Course ID for image uploads |
| `moduleId` | `string` | - | Module ID for image uploads |
| `showToolbar` | `boolean` | `true` | Show/hide the formatting toolbar |
| `showPreview` | `boolean` | `true` | Show/hide preview tabs |
| `fullscreen` | `boolean` | `false` | Start in fullscreen mode |
| `onFullscreenChange` | `(fullscreen: boolean) => void` | - | Fullscreen state change callback |

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+B` | Bold text |
| `Ctrl+I` | Italic text |
| `Ctrl+U` | Underline text |
| `Ctrl+Z` | Undo |
| `Ctrl+Y` | Redo |
| `Ctrl+Shift+D` | Insert collapsible section |

## 🎨 Styling

The editor uses Obsidian-inspired styling with full dark mode support. Styles are defined in:

- `src/styles/obsidian-markdown.css` - Main editor and preview styles
- CSS custom properties for theming integration
- Responsive design for mobile devices

### Customizing Styles

You can override styles by targeting the CSS classes:

```css
.obsidian-editor .ProseMirror {
  /* Editor styles */
}

.obsidian-preview {
  /* Preview styles */
}
```

## 📝 Markdown Features

### GitHub Flavored Markdown

```markdown
# Headings
## Subheadings

**Bold** and *italic* text
~~Strikethrough~~ and ==highlighted== text
`inline code`

- Bullet lists
- [x] Task lists
- [ ] Unchecked tasks

| Tables | Are | Supported |
|--------|-----|-----------|
| With   | Full| GFM       |

```code blocks```
```

### Callouts

```markdown
> [!INFO]
> This is an info callout

> [!WARNING]
> This is a warning callout

> [!SUCCESS]
> This is a success callout

> [!ERROR]
> This is an error callout

> [!TIP]
> This is a tip callout
```

### Collapsible Sections

```markdown
<details>
<summary>Click to expand</summary>

Hidden content goes here.
Can include any markdown elements.

</details>
```

## 🔧 Utility Functions

The `markdown-utils.ts` file provides helpful utilities:

```tsx
import { 
  getMarkdownStats, 
  validateMarkdown, 
  cleanupMarkdown,
  extractHeadings,
  generateTableOfContents 
} from '@/lib/markdown-utils';

// Get content statistics
const stats = getMarkdownStats(content);
console.log(stats.wordCount, stats.readingTimeMinutes);

// Validate markdown
const validation = validateMarkdown(content);
if (!validation.valid) {
  console.log('Errors:', validation.errors);
}

// Clean up formatting
const cleaned = cleanupMarkdown(content);

// Extract headings for TOC
const headings = extractHeadings(content);
const toc = generateTableOfContents(content);
```

## 🌐 Demo

Visit `/markdown-editor-demo` in your application to see a full-featured demo with:

- Live content statistics
- Validation feedback
- Feature showcase
- Usage examples
- Integration guide

## 🔒 Security

- All HTML output is sanitized using DOMPurify
- Image uploads are validated for file type and size
- XSS protection through content sanitization
- Safe handling of user-generated content

## 📱 Mobile Support

The editor is fully responsive and includes:

- Touch-friendly interface elements
- Optimized toolbar for mobile screens
- Swipe gestures for tab navigation
- Responsive typography and spacing

## 🎯 Integration Examples

Check out these integration examples:

- `src/components/examples/MarkdownEditorExample.tsx` - Basic integration
- `src/pages/MarkdownEditorDemo.tsx` - Full-featured demo
- Existing lesson content editors in your LMS

## 🐛 Troubleshooting

### Common Issues

1. **Images not uploading**: Ensure `courseId` and `moduleId` props are provided
2. **Styles not loading**: Make sure `obsidian-markdown.css` is imported in `main.tsx`
3. **Extensions not working**: Check that all TipTap extensions are properly installed

### Performance

- The editor uses lazy loading for better performance
- Code highlighting is debounced to prevent excessive re-rendering
- Images are optimized and compressed during upload

## 🚀 Future Enhancements

Potential improvements for the editor system:

- [ ] Collaborative editing with real-time sync
- [ ] Plugin system for custom extensions
- [ ] Advanced table editing with formulas
- [ ] Diagram support (Mermaid, PlantUML)
- [ ] Version history and diff viewing
- [ ] Advanced search and replace
- [ ] Custom themes and styling options

## 📄 License

This markdown editor system is part of your LMS project and follows the same licensing terms.
