# RLS (Row Level Security) Setup Guide

This guide will help you manually set up Row Level Security policies and storage buckets for the i-can-iv e-learning platform.

## Overview

Row Level Security (RLS) is a PostgreSQL feature that allows you to control access to individual rows in database tables. For our application, we need to:

1. Create storage buckets for file uploads
2. Set up RLS policies for storage access
3. Ensure proper user permissions

## Prerequisites

- Access to your Supabase dashboard
- Admin privileges on your Supabase project
- Basic understanding of SQL

## Step 1: Access Supabase SQL Editor

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `jibspqwieubavucdtccv`
3. Navigate to **SQL Editor** in the left sidebar
4. Click **New Query**

## Step 2: Run the RLS Fix Script

Copy and paste the entire contents of `supabase/manual_rls_fix.sql` into the SQL Editor and click **Run**.

This script will:
- ✅ Create 5 storage buckets (course-images, avatars, app-uploads, uploads, default-bucket)
- ✅ Enable RLS on storage.objects table
- ✅ Create policies for public read access
- ✅ Create policies for authenticated user uploads
- ✅ Create policies for users to manage their own files
- ✅ Create service role bypass policies

## Step 3: Verify the Setup

After running the script, verify it worked by running these queries in the SQL Editor:

### Check Buckets
```sql
SELECT name, public, file_size_limit FROM storage.buckets ORDER BY name;
```

Expected result: 5 buckets listed (course-images, avatars, app-uploads, uploads, default-bucket)

### Check RLS Status
```sql
SELECT schemaname, tablename, rowsecurity FROM pg_tables 
WHERE tablename = 'objects' AND schemaname = 'storage';
```

Expected result: `rowsecurity` should be `true`

### Check Policies
```sql
SELECT policyname, tablename FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage' 
ORDER BY policyname;
```

Expected result: Multiple policies listed for different access types

## Step 4: Test the Setup

Run the automated test script to verify everything is working:

```bash
npm run test:rls
```

This will test:
- ✅ Storage bucket existence
- ✅ Anonymous access restrictions
- ✅ Database RLS policies
- ✅ Authentication system

## Step 5: Manual Bucket Creation (If Script Fails)

If the SQL script fails to create buckets, create them manually:

1. Go to **Storage** in your Supabase dashboard
2. Click **Create Bucket** for each of these:

| Bucket Name | Public | File Size Limit | Allowed MIME Types |
|-------------|--------|-----------------|-------------------|
| course-images | ✅ Yes | 50 MB | Any |
| avatars | ✅ Yes | 10 MB | image/jpeg, image/png, image/webp |
| app-uploads | ✅ Yes | 50 MB | Any |
| uploads | ✅ Yes | 50 MB | Any |
| default-bucket | ✅ Yes | 50 MB | Any |

## Troubleshooting

### Common Issues

**Issue**: "new row violates row-level security policy"
**Solution**: Run the RLS policies script first, then create buckets

**Issue**: "policy already exists"
**Solution**: The script includes `DROP POLICY IF EXISTS` statements to handle this

**Issue**: Buckets not appearing
**Solution**: Refresh the Storage page or check the browser console for errors

### Verification Commands

Check if RLS is working properly:

```sql
-- Test anonymous access (should work for public buckets)
SELECT * FROM storage.buckets WHERE public = true;

-- Test policy existence
SELECT COUNT(*) FROM pg_policies WHERE tablename = 'objects';
```

### Manual Policy Creation

If policies fail to create, you can create them individually. Here's an example for one bucket:

```sql
-- Public read access
CREATE POLICY "Allow public access to course-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'course-images');

-- Authenticated upload access
CREATE POLICY "Allow authenticated uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);
```

## Security Considerations

1. **Public Buckets**: All buckets are set to public for read access
2. **Upload Restrictions**: Only authenticated users can upload files
3. **File Management**: Users can only modify/delete their own files
4. **Service Role**: Service role can bypass RLS for admin operations

## Next Steps

After completing this setup:

1. ✅ Run `npm run test:rls` to verify everything works
2. ✅ Test file uploads in your application
3. ✅ Monitor storage usage in Supabase dashboard
4. ✅ Update TODO.md to mark task 7 as complete

## Support

If you encounter issues:

1. Check the Supabase logs in your dashboard
2. Verify environment variables are correct
3. Ensure your Supabase project is active
4. Contact Supabase support if needed

---

**Last Updated**: May 28, 2025
**Script Location**: `supabase/manual_rls_fix.sql`
**Test Script**: `scripts/test-rls-policies.js`
