# Static Module Images Guide

This guide explains how to set up static images for modules in the application.

## Overview

To simplify the image handling process, we've implemented a system that uses static images stored in the `public/images` folder. This approach eliminates the complexity of uploading, storing, and managing dynamic images.

## Required Files

You need to add the following files to your project:

- `/public/images/m1.jpg` - Image for Module 1
- `/public/images/m2.jpg` - Image for Module 2
- `/public/images/m3.jpg` - Image for Module 3
- `/public/images/m4.jpg` - Image for Module 4
- `/public/images/m5.jpg` - Image for Module 5
- `/public/images/m6.jpg` - Image for Module 6
- `/public/images/m7.jpg` - Image for Module 7
- `/public/images/module-placeholder.jpg` - Fallback image used when a module image is missing

## Adding the Images

1. Download your preferred images for modules
2. Rename them to match the pattern: `m1.jpg`, `m2.jpg`, etc.
3. Place them in the `/public/images` folder in your project

## Image Recommendations

- **Size**: 1280x720 pixels (16:9 aspect ratio) is recommended
- **Format**: JPG format is preferred for better compression
- **Quality**: Medium to high quality (70-85%)
- **File size**: Keep each image under 500KB for better performance

## Folder Structure

Your project structure should look like this:

```
your-project/
├── public/
│   ├── images/
│   │   ├── m1.jpg
│   │   ├── m2.jpg
│   │   ├── m3.jpg
│   │   ├── m4.jpg
│   │   ├── m5.jpg
│   │   ├── m6.jpg
│   │   ├── m7.jpg
│   │   └── module-placeholder.jpg
│   └── other-public-files...
└── src/
    └── your-source-code...
```

## How It Works

1. Each module is automatically assigned an image based on its `module_number` property
2. Module 1 gets `m1.jpg`, Module 2 gets `m2.jpg`, and so on
3. If a module's number is greater than 7, it will cycle back to using the first images
4. If any image is missing, the `module-placeholder.jpg` will be used as a fallback

## Troubleshooting

If you see error messages about missing module images:

1. Check that all required images exist in the `/public/images` folder
2. Verify that the file names exactly match the expected patterns (`m1.jpg`, etc.)
3. Make sure the images are directly in the `/public/images` folder, not in subfolders
4. Check if the images are accessible via your browser by visiting `http://localhost:your-port/images/m1.jpg`

## Benefits of This Approach

- Simplified implementation without complex upload handling
- Faster page loads since images are served directly from static files
- No storage costs or management overhead
- Consistent, predictable image naming and referencing 