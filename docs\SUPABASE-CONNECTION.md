# Fixing Supabase Connection Issues

If you're seeing the message "Using cached login due to Supabase connection issues", follow these steps to resolve the problem.

## Quick Fix

Run the connection checker script:

```bash
npm run fix:connection
```

This script will:
1. Check your Supabase connection
2. Verify your environment variables
3. Provide troubleshooting steps if issues are found

## Manual Troubleshooting Steps

If the script doesn't resolve your issue, try these manual steps:

### 1. Check Your Environment Variables

Make sure your `.env` or `.env.local` file has the correct Supabase URL and API key:

```
VITE_SUPABASE_URL=https://your-project-url.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 2. Clear Browser Cache and Storage

1. Open your browser's developer tools (F12 or right-click > Inspect)
2. Go to the "Application" tab
3. Select "Storage" > "Clear site data"
4. Refresh the page

### 3. Check Your Internet Connection

Make sure you have a stable internet connection. The application uses cached login when it can't reach the Supabase servers.

### 4. Restart the Development Server

Stop and restart your development server:

```bash
# Press Ctrl+C to stop the server
npm run dev
```

### 5. Check Supabase Status

Visit the [Supabase Status Page](https://status.supabase.com/) to check if there are any ongoing service disruptions.

## Understanding the "Cached Login" Message

When the application can't connect to Supabase, it falls back to using cached credentials stored in your browser's local storage. This allows you to continue using some features of the application even when offline or when there are connection issues.

However, some features that require real-time data or server interactions will be limited until the connection is restored.

## Advanced Troubleshooting

If you're still experiencing issues:

1. Check if your Supabase project is active and properly configured
2. Verify that your IP address isn't blocked in Supabase Auth settings
3. Try accessing the Supabase dashboard to confirm your account is working
4. Check for any firewall or network restrictions that might be blocking connections

For developers: You can modify the connection retry logic in `src/lib/health-check.ts` and the custom fetch implementation in `src/integrations/supabase/client.ts` to adjust timeouts and retry strategies.
