# Professional Typography System

## Overview

The LMS application now uses a unified, professional typography system designed for optimal readability and consistent spacing across all lesson content. This system follows modern design principles and accessibility guidelines.

## Key Features

### 1. Unified Typography Scale
- **Professional font size scale** optimized for readability
- **Consistent line heights** for different content types
- **Semantic letter spacing** for improved legibility
- **Responsive typography** that adapts to screen sizes

### 2. Optimized Spacing System
- **Vertical rhythm** for consistent content flow
- **Horizontal spacing** standards for margins and padding
- **Reading-optimized widths** (45-75 characters per line)
- **Mobile-first responsive design**

### 3. Professional Content Styling
- **Heading hierarchy** with proper size relationships
- **Enhanced readability** for long-form content
- **Consistent element spacing** throughout
- **Accessibility compliance** with WCAG guidelines

## Typography Variables

### Font Sizes
```css
--text-xs: 0.75rem;     /* 12px - captions, labels */
--text-sm: 0.875rem;    /* 14px - small text, metadata */
--text-base: 1rem;      /* 16px - body text baseline */
--text-lg: 1.125rem;    /* 18px - large body text */
--text-xl: 1.25rem;     /* 20px - small headings */
--text-2xl: 1.5rem;     /* 24px - medium headings */
--text-3xl: 1.875rem;   /* 30px - large headings */
--text-4xl: 2.25rem;    /* 36px - extra large headings */
--text-5xl: 3rem;       /* 48px - display headings */
```

### Line Heights
```css
--leading-tight: 1.25;    /* Headings */
--leading-snug: 1.375;    /* Subheadings */
--leading-normal: 1.5;    /* UI text */
--leading-relaxed: 1.625; /* Body text */
--leading-loose: 1.75;    /* Long-form content */
```

### Letter Spacing
```css
--tracking-tighter: -0.05em;  /* Large headings */
--tracking-tight: -0.025em;   /* Headings */
--tracking-normal: 0em;       /* Body text */
--tracking-wide: 0.025em;     /* Uppercase text */
--tracking-wider: 0.05em;     /* Spaced text */
```

### Spacing System
```css
--space-xs: 0.25rem;    /* 4px */
--space-sm: 0.5rem;     /* 8px */
--space-md: 0.75rem;    /* 12px */
--space-lg: 1rem;       /* 16px */
--space-xl: 1.5rem;     /* 24px */
--space-2xl: 2rem;      /* 32px */
--space-3xl: 3rem;      /* 48px */
--space-4xl: 4rem;      /* 64px */
```

## Content Width Standards

### Reading-Optimized Widths
```css
--content-width-narrow: 45ch;   /* Narrow reading column */
--content-width-optimal: 65ch;  /* Optimal reading width */
--content-width-wide: 75ch;     /* Wide reading column */
--content-width-full: 100%;     /* Full width */
```

## CSS Classes

### Professional Lesson Container
```css
.professional-lesson-container {
  /* Constrained width for optimal reading */
  max-width: var(--content-width-optimal);
  margin: 0 auto;
  padding: var(--space-2xl) var(--space-xl);
}

.professional-lesson-container.full-width {
  /* Full-width variant for immersive content */
  max-width: none;
  width: 100%;
}
```

### Professional Prose Styling
```css
.professional-prose {
  /* Optimized typography for lesson content */
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
}
```

## Responsive Behavior

### Mobile (≤640px)
- **Smaller font sizes** for better mobile readability
- **Tighter spacing** to maximize content visibility
- **Simplified layouts** with reduced padding

### Tablet (641px-1023px)
- **Medium font sizes** for comfortable reading
- **Balanced spacing** between elements
- **Optimized line lengths** for tablet screens

### Desktop (≥1024px)
- **Larger font sizes** for enhanced readability
- **Generous spacing** for professional appearance
- **Wide layouts** with optimal reading widths

## Implementation

### Using in Components
```tsx
import '@/styles/professional-lesson-content.css';

// Professional lesson container
<div className="professional-lesson-container full-width">
  <div className="professional-prose">
    {/* Your content here */}
  </div>
</div>
```

### Responsive Typography
The system automatically adjusts font sizes and spacing based on screen size:

```css
/* Mobile */
@media (max-width: 640px) {
  .professional-prose {
    font-size: var(--text-sm);
  }
}

/* Desktop */
@media (min-width: 768px) {
  .professional-prose {
    font-size: var(--text-lg);
  }
}
```

## Benefits

### 1. Improved Readability
- **Optimal line lengths** (45-75 characters)
- **Proper line spacing** for comfortable reading
- **Clear heading hierarchy** for content structure

### 2. Consistent Spacing
- **Unified spacing system** across all content
- **Vertical rhythm** for better visual flow
- **Responsive spacing** that adapts to screen sizes

### 3. Professional Appearance
- **Modern typography** following design best practices
- **Consistent styling** across all lesson content
- **Enhanced visual hierarchy** for better comprehension

### 4. Accessibility
- **WCAG compliant** contrast ratios
- **Readable font sizes** for all users
- **Proper semantic structure** for screen readers

## Migration Guide

### From Old System
1. Replace `lesson-content-container-full-width` with `professional-lesson-container full-width`
2. Replace `prose prose-red max-w-none` with `professional-prose`
3. Import the new CSS file: `@import './styles/professional-lesson-content.css'`

### Testing
- Test on multiple screen sizes
- Verify readability with different content types
- Check accessibility with screen readers
- Validate responsive behavior

## Maintenance

### Adding New Typography
1. Use existing CSS variables when possible
2. Follow the established spacing system
3. Test responsive behavior
4. Document any new patterns

### Performance
- CSS variables reduce bundle size
- Consistent spacing improves layout performance
- Responsive design reduces need for multiple stylesheets
