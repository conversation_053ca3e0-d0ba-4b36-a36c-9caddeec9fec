<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="icon" type="image/svg+xml" href="/icon.svg" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0, viewport-fit=cover, user-scalable=yes" />
    <title>i-can-iv e-learning - Medical Imaging IV Skills Platform</title>
    <meta name="description" content="An interactive e-learning platform for mastering intravenous cannulation and contrast media administration skills for medical imaging students." />
    <meta name="author" content="<PERSON>" />
    <meta name="theme-color" content="#E63946" />
    <meta name="color-scheme" content="light dark" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="https://jibspqwieubavucdtccv.supabase.co" />
    <link rel="preconnect" href="https://jibspqwieubavucdtccv.supabase.co" crossorigin />

    <!-- Preload critical assets -->
    <link rel="modulepreload" href="/src/main.tsx" as="script" type="module" crossorigin />

    <!-- Poppins Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Dark mode fix script - runs before page load -->
    <script src="/dark-mode-fix.js" defer></script>

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://cdn.gpteng.co" crossorigin />

    <!-- Social media tags -->
    <meta property="og:title" content="i-can-iv e-learning - Medical Imaging IV Skills Platform" />
    <meta property="og:description" content="An interactive e-learning platform for mastering intravenous cannulation and contrast media administration skills for medical imaging students." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#E63946" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#C1121F" media="(prefers-color-scheme: dark)" />

    <!-- Critical CSS inline for faster rendering -->
    <style>
      /* Initial loading state - optimized for performance */
      html{line-height:1.5;-webkit-text-size-adjust:100%;tab-size:4;font-family:Poppins,ui-sans-serif,system-ui;font-feature-settings:normal;font-variation-settings:normal;scroll-behavior:smooth;touch-action:manipulation;-webkit-tap-highlight-color:transparent}
      body{margin:0;padding:0;height:100%;font-family:'Poppins',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;line-height:inherit;overflow-x:hidden;text-size-adjust:100%;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
      *,:after,:before{box-sizing:border-box;border:0 solid #e5e7eb}
      :root{color-scheme:light dark}

      /* Improved mobile input handling */
      input,button,textarea,select{font:inherit;touch-action:manipulation}

      /* Better touch targets */
      button,a{min-height:44px;min-width:44px}

      /* Prevent content shifting */
      html,body{overflow-x:hidden;width:100%}

      /* Loading spinner - optimized */
      #initial-loader{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:#fff;z-index:9999;transition:opacity .3s ease}
      @media (prefers-color-scheme:dark){#initial-loader{background-color:#1F2937}}
      .spinner{width:40px;height:40px;border:3px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#E63946;animation:spin 1s ease-in-out infinite}
      @media (prefers-color-scheme:dark){.spinner{border:3px solid rgba(255,255,255,.1);border-top-color:#E63946}}
      @keyframes spin{to{transform:rotate(360deg)}}

      /* Content fade-in */
      #root{opacity:0;transition:opacity .3s ease}
      .loaded #root{opacity:1}

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        html {scroll-behavior:auto}
        *,*::before,*::after {animation-duration:0.01ms !important;animation-iteration-count:1 !important;transition-duration:0.01ms !important;scroll-behavior:auto !important}
      }
    </style>

    <!-- Preload detection and failsafe loader removal -->
    <script defer>
      // Add class to document when fully loaded
      window.addEventListener('load', function() {
        document.documentElement.classList.add('loaded');
      });

      // Detect slow connections
      if ('connection' in navigator) {
        if (navigator.connection.saveData ||
            (navigator.connection.effectiveType &&
             (navigator.connection.effectiveType.includes('2g') ||
              navigator.connection.effectiveType.includes('slow')))) {
          document.documentElement.classList.add('slow-connection');
        }
      }

      // Failsafe: Force remove the loader after 5 seconds no matter what
      // This ensures the app is usable even if there are JS errors
      setTimeout(function() {
        var loader = document.getElementById('initial-loader');
        if (loader && loader.parentNode) {
          loader.parentNode.removeChild(loader);
          console.log('Loader forcibly removed by failsafe timeout');
          document.documentElement.classList.add('loaded');
        }
      }, 5000);
    </script>

    <!-- Preload critical assets -->
    <script src="/preload-critical.js" defer></script>

    <!-- Additional styles with mobile optimizations -->
    <style>
      body { transition: var(--theme-transition, none); }
      :focus-visible { outline: 2px solid #E63946; outline-offset: 2px; }

      /* Mobile optimizations */
      @media (max-width: 640px) {
        input, select, textarea { font-size: 16px !important; } /* Prevent zoom on iOS */
        button, a { cursor: pointer; } /* Better touch feedback */
        * { -webkit-tap-highlight-color: transparent; } /* Remove tap highlight */
      }

      /* Safe area insets for notched devices */
      @supports(padding: max(0px)) {
        body {
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
          padding-top: env(safe-area-inset-top);
          padding-bottom: env(safe-area-inset-bottom);
        }
      }
    </style>
  </head>

  <body>
    <!-- Initial loading spinner that shows before React loads -->
    <div id="initial-loader">
      <div class="spinner"></div>
    </div>

    <div id="root"></div>

    <script type="module" src="/src/main.tsx" crossorigin></script>

    <script defer>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
              console.log('ServiceWorker registration successful with scope: ', registration.scope);
            })
            .catch(error => {
              console.log('ServiceWorker registration failed: ', error);
            });
        });
      }
    </script>
  </body>
</html>
