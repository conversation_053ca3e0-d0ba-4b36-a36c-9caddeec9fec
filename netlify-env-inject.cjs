/**
 * This script injects environment variables into the build process
 * Run this script before the build to ensure environment variables are available
 * Using CommonJS format (.cjs) to avoid ES module issues
 */
const fs = require('fs');
const path = require('path');

// Environment variables to inject
const envVars = {
  VITE_SUPABASE_URL: 'https://jibspqwieubavucdtccv.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI',
  VITE_SUPABASE_SERVICE_ROLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzEyNTM3MiwiZXhwIjoyMDU4NzAxMzcyfQ.I6Vxg6B0FvKPOeKMhAAu75kOf6ct5NBNs-azmAwqNWI',
  VITE_APP_ENVIRONMENT: 'production'
};

// Create .env.production file
const envContent = Object.entries(envVars)
  .map(([key, value]) => `${key}=${value}`)
  .join('\n');

// Write to .env.production
fs.writeFileSync(path.resolve(__dirname, '.env.production'), envContent);

// Also set variables in current process
Object.entries(envVars).forEach(([key, value]) => {
  process.env[key] = value;
});

// Inject variables into Netlify environment
try {
  // Check if we're running in Netlify
  if (process.env.NETLIFY === 'true') {
    // Create a script for Netlify to source
    const netlifyEnvPath = path.resolve(__dirname, '.netlify-env.sh');
    const netlifyEnvContent = Object.entries(envVars)
      .map(([key, value]) => `export ${key}="${value}"`)
      .join('\n');
    
    fs.writeFileSync(netlifyEnvPath, netlifyEnvContent);
  }
} catch (error) {
  console.error('Error creating Netlify environment script:', error);
} 