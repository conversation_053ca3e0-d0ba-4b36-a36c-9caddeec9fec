<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Module Images Test</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }
    .image-card {
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .image-container {
      height: 180px;
      overflow: hidden;
      position: relative;
    }
    .image-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    .image-container:hover img {
      transform: scale(1.05);
    }
    .image-info {
      padding: 15px;
      background: #f9f9f9;
    }
    .image-info h3 {
      margin: 0 0 10px 0;
      color: #444;
    }
    .status {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .placeholder {
      margin-top: 40px;
      padding: 20px;
      border: 1px solid #eee;
      border-radius: 8px;
    }
  </style>
</head>
<body>
  <h1>Module Images Test</h1>
  <p>This page tests whether all required module images are available in the correct location. If you see any "Missing" status, you need to add those images to the public/images folder.</p>
  
  <div class="image-grid">
    <!-- Module 1 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m1.jpg" alt="Module 1" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m1.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 1</h3>
        <p>Path: <code>/public/images/m1.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 2 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m2.jpg" alt="Module 2" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m2.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 2</h3>
        <p>Path: <code>/public/images/m2.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 3 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m3.jpg" alt="Module 3" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m3.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 3</h3>
        <p>Path: <code>/public/images/m3.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 4 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m4.jpg" alt="Module 4" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m4.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 4</h3>
        <p>Path: <code>/public/images/m4.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 5 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m5.jpg" alt="Module 5" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m5.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 5</h3>
        <p>Path: <code>/public/images/m5.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 6 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m6.jpg" alt="Module 6" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m6.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 6</h3>
        <p>Path: <code>/public/images/m6.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Module 7 Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="m7.jpg" alt="Module 7" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+m7.jpg'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Module 7</h3>
        <p>Path: <code>/public/images/m7.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
    
    <!-- Placeholder Image -->
    <div class="image-card">
      <div class="image-container">
        <img src="module-placeholder.jpg" alt="Fallback Placeholder" onerror="this.src='https://placehold.co/600x400/e74c3c/ffffff?text=Missing+placeholder'; this.parentNode.parentNode.querySelector('.status').className='status error'; this.parentNode.parentNode.querySelector('.status').textContent='Missing';">
      </div>
      <div class="image-info">
        <h3>Placeholder</h3>
        <p>Path: <code>/public/images/module-placeholder.jpg</code></p>
        <span class="status success">Available</span>
      </div>
    </div>
  </div>
  
  <div class="placeholder">
    <h2>Need Images?</h2>
    <p>You can download suitable module images from stock photo sites or use placeholder services like:</p>
    <ul>
      <li><a href="https://unsplash.com/search/photos/education" target="_blank">Unsplash - Education Photos</a></li>
      <li><a href="https://pixabay.com/images/search/education/" target="_blank">Pixabay - Education Images</a></li>
      <li><a href="https://placehold.co/600x400" target="_blank">Placehold.co - Generate placeholder images</a></li>
    </ul>
    <p>Remember to name your images according to the pattern: <code>m1.jpg</code>, <code>m2.jpg</code>, etc.</p>
  </div>
  
  <script>
    // Script to count missing images
    window.addEventListener('load', function() {
      const images = document.querySelectorAll('.image-card img');
      let missingCount = 0;
      
      images.forEach(img => {
        img.addEventListener('error', () => {
          missingCount++;
          updateSummary();
        });
      });
      
      function updateSummary() {
        const summaryEl = document.querySelector('p');
        if (missingCount > 0) {
          summaryEl.innerHTML = `<strong style="color: #721c24">Warning: ${missingCount} of 8 required images are missing.</strong> Please add the missing images to the public/images folder as indicated below.`;
        } else {
          summaryEl.innerHTML = `<strong style="color: #155724">All module images are available!</strong> Your application is ready to use static module images.`;
        }
      }
      
      // Check after a short delay to make sure error events have fired
      setTimeout(updateSummary, 1000);
    });
  </script>
</body>
</html> 