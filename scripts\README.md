# IVCAN Course LMS Scripts

This directory contains utility scripts for managing the IVCAN Course LMS.

## Setup

Before running the scripts, install the dependencies:

```bash
cd scripts
npm install
```

## New Unified Fix Script

The `unified-fix.js` script provides a single interface for running various fixes. It replaces multiple individual fix scripts with a more maintainable solution.

### Usage

```bash
node scripts/unified-fix.js [fix-type] [options]
```

### Examples

```bash
# Show help
node scripts/unified-fix.js --help

# Run all fixes
node scripts/unified-fix.js all

# Fix course completion for a specific user and course
node scripts/unified-fix.js course-completion --user=123 --course=456

# Reset module completion for all users
node scripts/unified-fix.js module-completion --reset --all-users

# Create storage buckets
node scripts/unified-fix.js storage --create-buckets
```

### Available Fix Types

- `all`: Run all fixes
- `course-completion`: Fix course completion issues
- `module-completion`: Fix module completion issues
- `lesson-progress`: Fix lesson progress issues
- `certificates`: Fix certificate issues
- `storage`: Fix storage issues
- `auto-completion`: Manage auto-completion settings
- `security`: Apply security fixes
- `profile`: Fix profile page issues
- `help`: Show help message

## Legacy Scripts (Deprecated)

The following scripts are maintained for backward compatibility but are deprecated in favor of the unified fix script:

### Fix Course Completion

This script fixes course completion issues by directly executing SQL.

```bash
# Fix a specific course for a user
node fix-course-completion.js <user_id> <course_id>

# Fix all courses for a user
node fix-course-completion.js <user_id> --all
```

### Reset Module Completion

This script resets module completion status for a user.

```bash
node reset-module-completion.js <user_id>
```

### Reset All Module Completions

This script resets all module completion statuses for all users.

```bash
node reset-all-module-completions.js
```

### Disable Auto-Completion

This script disables auto-completion for all students.

```bash
node disable-auto-completion.js
```

### Fix Incorrect Completions

This script fixes incorrect course completion records, such as courses marked as completed for users who haven't enrolled.

```bash
node fix-incorrect-completions.js
```

### Fix Lesson Progress

This script fixes lesson progress records and updates module completion status based on completed lessons.

```bash
# Fix for a specific user
node fix-lesson-progress.js <user_id>

# Fix for all users
node fix-lesson-progress.js
```

### Create User Module Progress Table

This script creates the user_module_progress table in Supabase.

```bash
node create-user-module-progress-table.js
```

### Fix All Issues

This script runs all the fixes for the application.

```bash
node fix-all-issues.js
```

### Fix Storage RLS

This script fixes the RLS policies for storage buckets in Supabase.

```bash
node fix-storage-rls.js
```

### Run Storage SQL

This script runs the SQL to create storage buckets and RLS policies directly in Supabase.

```bash
node run-storage-sql.js
```

## Configuration

All scripts now use the centralized configuration from `config.js`, which loads environment variables from the `.env` file in the project root.

### Setting Up Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=https://your-project-url.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

### Using with Service Role Key (Admin Only)

For elevated permissions, you can set the `VITE_SUPABASE_SERVICE_ROLE_KEY` environment variable in your `.env` file.

Alternatively, you can set it directly in your terminal:

```bash
# Windows PowerShell
$env:VITE_SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
node unified-fix.js all

# Windows Command Prompt
set VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
node unified-fix.js all

# Linux/Mac
VITE_SUPABASE_SERVICE_ROLE_KEY="your-service-role-key" node unified-fix.js all
```

**Note:** The service role key should be kept secure and only used by administrators.
