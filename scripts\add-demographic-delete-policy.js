import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- VITE_SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addDemographicDeletePolicy() {
  console.log('Adding DELETE policy for teachers on demographic responses...');

  try {
    // Check if the policy already exists
    const { data: existingPolicies, error: checkError } = await supabase
      .from('pg_policies')
      .select('policyname')
      .eq('tablename', 'user_demographic_responses')
      .eq('policyname', 'Teachers can delete all responses');

    if (checkError) {
      console.log('Could not check existing policies (this is normal):', checkError.message);
    }

    if (existingPolicies && existingPolicies.length > 0) {
      console.log('Policy already exists, skipping creation.');
      return;
    }

    // Create the policy using raw SQL
    const policySQL = `
      -- Add policy for teachers to delete demographic responses
      CREATE POLICY "Teachers can delete all responses"
        ON public.user_demographic_responses
        FOR DELETE
        USING (
          EXISTS (
            SELECT 1 FROM public.user_roles 
            WHERE user_id = auth.uid() AND role = 'teacher'
          )
        );
    `;

    const { error: policyError } = await supabase.rpc('exec_sql', { sql: policySQL });

    if (policyError) {
      console.error('Error creating policy with exec_sql:', policyError);
      
      // Try alternative approach - direct SQL execution
      console.log('Trying alternative approach...');
      
      // This might work if we have direct SQL access
      const { error: directError } = await supabase
        .from('_supabase_admin')
        .insert({ sql: policySQL });

      if (directError) {
        console.error('Alternative approach also failed:', directError);
        console.log('\n=== MANUAL STEPS REQUIRED ===');
        console.log('Please run the following SQL in your Supabase SQL editor:');
        console.log('\n' + policySQL);
        console.log('\n=== END MANUAL STEPS ===\n');
        return;
      }
    }

    console.log('✅ Successfully added DELETE policy for teachers on demographic responses');

    // Test the policy by attempting a count (this should work for teachers)
    const { count, error: testError } = await supabase
      .from('user_demographic_responses')
      .select('*', { count: 'exact', head: true });

    if (testError) {
      console.log('Test query failed:', testError.message);
    } else {
      console.log(`✅ Policy test successful. Found ${count} demographic responses.`);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    console.log('\n=== MANUAL STEPS REQUIRED ===');
    console.log('Please run the following SQL in your Supabase SQL editor:');
    console.log(`
      -- Add policy for teachers to delete demographic responses
      CREATE POLICY "Teachers can delete all responses"
        ON public.user_demographic_responses
        FOR DELETE
        USING (
          EXISTS (
            SELECT 1 FROM public.user_roles 
            WHERE user_id = auth.uid() AND role = 'teacher'
          )
        );
    `);
    console.log('\n=== END MANUAL STEPS ===\n');
  }
}

// Run the function
addDemographicDeletePolicy()
  .then(() => {
    console.log('Script completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
