// <PERSON><PERSON><PERSON> to add the image_url column to the courses table
// Run this script with: node scripts/add-image-url-column.js

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function addImageUrlColumn() {
  console.log('Starting script to add image_url column to courses table...');
  
  try {
    // First, check if the column exists
    console.log('Checking if image_url column exists...');
    
    // Execute a query to check if the column exists
    const { data, error } = await supabase.rpc('check_column_exists', {
      table_name: 'courses',
      column_name: 'image_url'
    });
    
    if (error) {
      console.error('Error checking if column exists:', error);
      
      // If the RPC function doesn't exist, we'll try a direct SQL approach
      console.log('Trying direct SQL approach...');
      
      // Add the column using raw SQL
      const { error: sqlError } = await supabase.from('courses').select('id').limit(1);
      
      if (sqlError && sqlError.message.includes('image_url')) {
        console.log('Column does not exist. Adding image_url column...');
        
        // Execute SQL to add the column
        const { error: addError } = await supabase.rpc('add_image_url_column');
        
        if (addError) {
          console.error('Error adding column:', addError);
          console.log('Please add the column manually using SQL: ALTER TABLE public.courses ADD COLUMN image_url TEXT;');
        } else {
          console.log('Column added successfully!');
        }
      } else {
        console.log('Column may already exist or there was a different error.');
      }
      
      return;
    }
    
    if (data) {
      console.log('Column exists:', data);
    } else {
      console.log('Column does not exist. Adding image_url column...');
      
      // Add the column
      const { error: addError } = await supabase.rpc('add_image_url_column');
      
      if (addError) {
        console.error('Error adding column:', addError);
        console.log('Please add the column manually using SQL: ALTER TABLE public.courses ADD COLUMN image_url TEXT;');
      } else {
        console.log('Column added successfully!');
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    console.log('Please add the column manually using SQL: ALTER TABLE public.courses ADD COLUMN image_url TEXT;');
  }
}

// Run the function
addImageUrlColumn();
