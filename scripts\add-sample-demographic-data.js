/**
 * <PERSON><PERSON><PERSON> to add sample demographic data for testing the school-grouped analytics
 * This creates fake demographic responses for different universities
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Sample demographic responses for different universities
const sampleResponses = [
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Male",
      age: 22,
      formal_training: "Yes",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "University of Ghana (UG)",
      undergraduate_program: "BSc Diagnostic Radiography",
      undergraduate_year: "3rd Year"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Female",
      age: 21,
      formal_training: "No",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "University of Ghana (UG)",
      undergraduate_program: "BSc Diagnostic Sonography",
      undergraduate_year: "2nd Year"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Male",
      age: 24,
      formal_training: "Yes",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "Kwame Nkrumah University of Science and Technology (KNUST)",
      undergraduate_program: "BSc Diagnostic Radiography",
      undergraduate_year: "4th Year"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Female",
      age: 23,
      formal_training: "No",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "Kwame Nkrumah University of Science and Technology (KNUST)",
      undergraduate_program: "BSc Nuclear Medicine Technology",
      undergraduate_year: "3rd Year"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Male",
      age: 25,
      formal_training: "Yes",
      role_type: "Student",
      student_level: "Postgraduate",
      university: "University of Cape Coast (UCC)",
      postgraduate_program: "MSc"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Female",
      age: 28,
      formal_training: "Yes",
      role_type: "Practitioner",
      practitioner_work: "Diagnostic Radiographer",
      workplace: "Tertiary Hospital",
      location: "Greater Accra Region",
      experience_years: "3 Years"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Nigeria",
      gender: "Male",
      age: 26,
      formal_training: "Yes",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "University of Health and Allied Sciences (UHAS)",
      undergraduate_program: "BSc Diagnostic Radiography",
      undergraduate_year: "4th Year"
    }
  },
  {
    responses: {
      consent: "Agree",
      country: "Ghana",
      gender: "Female",
      age: 20,
      formal_training: "No",
      role_type: "Student",
      student_level: "Undergraduate",
      university: "Accra Technical University (ATU)",
      undergraduate_program: "BSc Diagnostic Sonography",
      undergraduate_year: "1st Year"
    }
  }
];

/**
 * Add sample demographic data
 */
async function addSampleData() {
  console.log('🧪 Adding sample demographic data for testing...\n');

  try {
    // First, get the active questionnaire
    console.log('📋 Getting active questionnaire...');
    const { data: questionnaire, error: questionnaireError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true)
      .single();

    if (questionnaireError) {
      console.error('❌ Error fetching questionnaire:', questionnaireError);
      return;
    }

    if (!questionnaire) {
      console.error('❌ No active questionnaire found');
      return;
    }

    console.log(`✅ Found questionnaire: ${questionnaire.title}`);

    // Get some existing users to assign responses to
    console.log('\n👥 Getting existing users...');
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name')
      .limit(10);

    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return;
    }

    if (!users || users.length === 0) {
      console.error('❌ No users found. Please create some users first.');
      return;
    }

    console.log(`✅ Found ${users.length} users`);

    // Add sample responses
    console.log('\n📊 Adding sample demographic responses...');
    
    for (let i = 0; i < Math.min(sampleResponses.length, users.length); i++) {
      const user = users[i];
      const sampleResponse = sampleResponses[i];

      console.log(`   Adding response for ${user.first_name || 'User'} ${user.last_name || user.id.substring(0, 8)}...`);

      const { error: insertError } = await supabase
        .from('user_demographic_responses')
        .upsert({
          user_id: user.id,
          questionnaire_id: questionnaire.id,
          responses: sampleResponse.responses,
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        console.error(`   ❌ Error adding response for user ${user.id}:`, insertError);
      } else {
        console.log(`   ✅ Added response for ${sampleResponse.responses.university}`);
      }
    }

    console.log('\n🎉 Sample data added successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Added ${Math.min(sampleResponses.length, users.length)} demographic responses`);
    console.log(`   - Universities represented: ${[...new Set(sampleResponses.map(r => r.responses.university))].length}`);
    console.log(`   - Ready to test school-grouped analytics!`);

  } catch (error) {
    console.error('❌ Script failed:', error);
  }
}

// Run the script
console.log('🚀 Starting sample data creation...');
addSampleData().then(() => {
  console.log('\n✨ Script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  console.error('Error details:', error);
  process.exit(1);
});
