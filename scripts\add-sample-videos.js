/**
 * Add Sample Videos to Lessons Script
 * 
 * This script adds sample video URLs to existing lessons to test video functionality:
 * 1. Finds lessons without video content
 * 2. Adds sample YouTube/Vimeo videos to demonstrate functionality
 * 3. Updates lesson content with proper video URL structure
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Sample educational videos (using public educational content)
const sampleVideos = [
  {
    title: 'Medical Procedures Overview',
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Sample URL - replace with actual educational content
    description: 'Introduction to medical procedures and safety protocols'
  },
  {
    title: 'IV Cannulation Technique',
    url: 'https://www.youtube.com/watch?v=oHg5SJYRHA0', // Sample URL - replace with actual educational content
    description: 'Step-by-step guide to IV cannulation'
  },
  {
    title: 'Patient Safety Protocols',
    url: 'https://vimeo.com/123456789', // Sample Vimeo URL - replace with actual educational content
    description: 'Essential patient safety protocols in medical procedures'
  },
  {
    title: 'Contrast Media Administration',
    url: 'https://www.youtube.com/watch?v=ScMzIvxBSi4', // Sample URL - replace with actual educational content
    description: 'Safe administration of contrast media'
  },
  {
    title: 'Handling Medical Complications',
    url: 'https://youtu.be/dQw4w9WgXcQ', // Sample short URL - replace with actual educational content
    description: 'How to handle complications during medical procedures'
  }
];

/**
 * Process video URL to proper embed format
 */
function processVideoUrl(url) {
  if (!url) return null;
  
  // For data URLs, return as is
  if (url.startsWith('data:')) return url;
  
  // Handle YouTube URLs
  if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
    const videoId = url.includes('youtu.be') 
      ? url.split('/').pop()?.split('?')[0]
      : new URL(url).searchParams.get('v');
    
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}`;
    }
  }
  
  // Handle Vimeo URLs
  if (url.includes('vimeo.com')) {
    const vimeoId = url.split('/').pop()?.split('?')[0];
    if (vimeoId && !isNaN(vimeoId)) {
      return `https://player.vimeo.com/video/${vimeoId}`;
    }
  }
  
  return url;
}

/**
 * Get lessons that don't have video content
 */
async function getLessonsWithoutVideos() {
  console.log(`${colors.blue}🔍 Finding lessons without video content...${colors.reset}`);
  
  const { data: lessons, error } = await supabase
    .from('lessons')
    .select(`
      id,
      title,
      content,
      video_url,
      type,
      module_id,
      modules:module_id (title)
    `)
    .eq('type', 'lesson') // Only regular lessons, not quizzes
    .order('created_at')
    .limit(5); // Limit to first 5 lessons for testing
  
  if (error) {
    console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
    return [];
  }
  
  // Filter out lessons that already have videos
  const lessonsWithoutVideos = lessons?.filter(lesson => {
    // Check if lesson already has video_url
    if (lesson.video_url) return false;
    
    // Check if lesson content contains video
    if (lesson.content) {
      try {
        const parsed = JSON.parse(lesson.content);
        if (parsed.videoUrl) return false;
      } catch (e) {
        // Not JSON, check for video URLs in markdown
        const hasVideoUrl = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/|vimeo\.com\/)/i.test(lesson.content);
        if (hasVideoUrl) return false;
      }
    }
    
    return true;
  }) || [];
  
  return lessonsWithoutVideos;
}

/**
 * Add video content to lessons
 */
async function addVideosToLessons() {
  console.log(`${colors.cyan}🎥 Adding Sample Videos to Lessons${colors.reset}\n`);
  
  const lessons = await getLessonsWithoutVideos();
  
  if (lessons.length === 0) {
    console.log(`${colors.yellow}⚠️ No lessons found without video content${colors.reset}`);
    return;
  }
  
  console.log(`${colors.green}✅ Found ${lessons.length} lessons without videos${colors.reset}\n`);
  
  for (let i = 0; i < lessons.length && i < sampleVideos.length; i++) {
    const lesson = lessons[i];
    const video = sampleVideos[i];
    
    try {
      console.log(`${colors.blue}📹 Adding video to: ${lesson.title}${colors.reset}`);
      console.log(`   Module: ${lesson.modules?.title || 'Unknown'}`);
      console.log(`   Video: ${video.title}`);
      console.log(`   URL: ${video.url}`);
      
      const processedUrl = processVideoUrl(video.url);
      console.log(`   Processed URL: ${processedUrl}`);
      
      // Create rich content with video
      const richContent = {
        content: lesson.content || `# ${lesson.title}\n\n${video.description}\n\n---\n\nWatch the video above to learn more about this topic.`,
        videoUrl: processedUrl,
        videoTitle: video.title,
        videoDescription: video.description
      };
      
      // Update lesson with rich content
      const { error } = await supabase
        .from('lessons')
        .update({ 
          content: JSON.stringify(richContent),
          updated_at: new Date().toISOString()
        })
        .eq('id', lesson.id);
      
      if (error) {
        console.error(`${colors.red}❌ Error updating lesson: ${error.message}${colors.reset}`);
      } else {
        console.log(`${colors.green}✅ Successfully added video to lesson${colors.reset}`);
      }
      
      console.log('');
    } catch (error) {
      console.error(`${colors.red}❌ Error processing lesson ${lesson.title}: ${error.message}${colors.reset}`);
    }
  }
  
  console.log(`${colors.green}🎉 Video addition completed!${colors.reset}`);
}

/**
 * Remove sample videos (cleanup function)
 */
async function removeSampleVideos() {
  console.log(`${colors.yellow}🧹 Removing sample videos from lessons...${colors.reset}`);
  
  const { data: lessons, error } = await supabase
    .from('lessons')
    .select('id, title, content')
    .not('content', 'is', null);
  
  if (error) {
    console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
    return;
  }
  
  let removedCount = 0;
  
  for (const lesson of lessons || []) {
    try {
      const parsed = JSON.parse(lesson.content);
      if (parsed.videoUrl) {
        // Remove video and keep only text content
        const cleanContent = parsed.content || '';
        
        const { error: updateError } = await supabase
          .from('lessons')
          .update({ 
            content: cleanContent,
            updated_at: new Date().toISOString()
          })
          .eq('id', lesson.id);
        
        if (!updateError) {
          console.log(`${colors.green}✅ Removed video from: ${lesson.title}${colors.reset}`);
          removedCount++;
        }
      }
    } catch (e) {
      // Not JSON content, skip
    }
  }
  
  console.log(`${colors.green}🎉 Removed videos from ${removedCount} lessons${colors.reset}`);
}

/**
 * Main function
 */
async function main() {
  try {
    const action = process.argv[2];
    
    if (action === 'remove' || action === 'clean') {
      await removeSampleVideos();
    } else {
      await addVideosToLessons();
      
      console.log(`\n${colors.cyan}💡 Tips:${colors.reset}`);
      console.log(`   • Visit the lesson pages to see the videos in action`);
      console.log(`   • Use the admin panel to edit lessons and add your own videos`);
      console.log(`   • Run 'node scripts/add-sample-videos.js remove' to clean up sample videos`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main();
