// Bundle analysis helper
console.log('Building with bundle analysis...');
const { execSync } = require('child_process');

try {
  // Build with analysis
  execSync('vite build --mode production', { stdio: 'inherit' });
  
  // Generate bundle analysis
  execSync('npx rollup-plugin-visualizer dist/stats.html --open', { stdio: 'inherit' });
  
  console.log('Bundle analysis complete! Check dist/stats.html');
} catch (error) {
  console.error('Error during bundle analysis:', error.message);
}