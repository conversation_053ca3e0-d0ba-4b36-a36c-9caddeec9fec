// This script analyzes the bundle size
const { execSync } = require('child_process');

console.log('Analyzing bundle size...');

try {
  // Install rollup-plugin-visualizer if not already installed
  execSync('npm install --save-dev rollup-plugin-visualizer', { stdio: 'inherit' });
  
  // Build with visualization
  execSync('vite build --mode analyze', { stdio: 'inherit' });
  
  console.log('Bundle analysis completed. Check stats.html in the project root.');
} catch (error) {
  console.error('Error analyzing bundle:', error.message);
  process.exit(1);
}
