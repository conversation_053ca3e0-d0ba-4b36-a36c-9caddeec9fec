import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('VITE_SUPABASE_URL:', !!supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyDemographicMigration() {
  try {
    console.log('Applying demographic questionnaire migration...');

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250115001_demographic_questionnaire.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        
        try {
          const { error } = await supabase.rpc('exec_sql', { 
            sql: statement + ';' 
          });

          if (error) {
            // Try direct execution if RPC fails
            const { error: directError } = await supabase
              .from('_migrations')
              .select('*')
              .limit(1);

            if (directError) {
              console.log('Attempting direct SQL execution...');
              // For some statements, we might need to handle them differently
              console.log('Statement:', statement.substring(0, 100) + '...');
            }
          }
        } catch (err) {
          console.warn(`Warning: Could not execute statement ${i + 1}:`, err.message);
          // Continue with next statement
        }
      }
    }

    // Test if tables were created successfully
    console.log('Testing table creation...');
    
    const { data: questionnaires, error: qError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .limit(1);

    const { data: responses, error: rError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .limit(1);

    if (!qError && !rError) {
      console.log('✅ Migration applied successfully!');
      console.log('Tables created:');
      console.log('- demographic_questionnaires');
      console.log('- user_demographic_responses');
      
      if (questionnaires && questionnaires.length > 0) {
        console.log('✅ Default questionnaire data inserted');
      } else {
        console.log('⚠️  Default questionnaire data may not have been inserted');
      }
    } else {
      console.log('❌ Migration may have failed');
      console.log('Questionnaire table error:', qError?.message);
      console.log('Response table error:', rError?.message);
    }

  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyDemographicMigration();
