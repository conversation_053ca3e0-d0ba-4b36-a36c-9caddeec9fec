/**
 * <PERSON><PERSON><PERSON> to apply the fix migrations to the Supabase database
 * 
 * This script applies the migrations that fix the completed_at column issue
 * in the user_course_enrollment table.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '..', 'supabase', 'migrations');
const FIX_MIGRATIONS = [
  '20250901010_fix_completed_at_column.sql',
  '20250901011_create_execute_sql_function.sql'
];

// Function to apply migrations
async function applyFixMigrations() {
  console.log('Applying fix migrations...');
  
  try {
    // Check if migrations exist
    for (const migration of FIX_MIGRATIONS) {
      const migrationPath = path.join(MIGRATIONS_DIR, migration);
      if (!fs.existsSync(migrationPath)) {
        console.error(`Migration file not found: ${migration}`);
        return false;
      }
    }
    
    // Apply migrations using Supabase CLI
    for (const migration of FIX_MIGRATIONS) {
      console.log(`Applying migration: ${migration}`);
      const migrationPath = path.join(MIGRATIONS_DIR, migration);
      const migrationContent = fs.readFileSync(migrationPath, 'utf8');
      
      // Execute the migration using Supabase CLI
      execSync(`npx supabase db push --db-url ${process.env.DATABASE_URL} --migration-file ${migrationPath}`, {
        stdio: 'inherit'
      });
      
      console.log(`Successfully applied migration: ${migration}`);
    }
    
    console.log('All fix migrations applied successfully!');
    return true;
  } catch (error) {
    console.error('Error applying migrations:', error);
    return false;
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  applyFixMigrations()
    .then(success => {
      if (success) {
        console.log('Migration process completed successfully.');
        process.exit(0);
      } else {
        console.error('Migration process failed.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { applyFixMigrations };
