/**
 * <PERSON><PERSON><PERSON> to apply the new database schema
 * 
 * This script applies the new database schema migrations in the correct order.
 * It will reset the database first to ensure a clean state.
 * 
 * Usage:
 * node scripts/apply-new-database.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '..', 'supabase', 'migrations');
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// New migration files in order
const NEW_MIGRATIONS = [
  '20250901001_users_and_profiles.sql',
  '20250901002_roles_and_permissions.sql',
  '20250901003_courses_and_content.sql',
  '20250901004_user_progress_and_enrollment.sql',
  '20250901005_notifications_and_messaging.sql'
];

// Function to apply migrations
async function applyNewDatabase() {
  console.log('Applying new database schema...');
  
  try {
    // Check if migrations directory exists
    if (!fs.existsSync(MIGRATIONS_DIR)) {
      console.error(`Migrations directory not found: ${MIGRATIONS_DIR}`);
      process.exit(1);
    }
    
    // Verify all migration files exist
    for (const migrationFile of NEW_MIGRATIONS) {
      const filePath = path.join(MIGRATIONS_DIR, migrationFile);
      if (!fs.existsSync(filePath)) {
        console.error(`Migration file not found: ${filePath}`);
        process.exit(1);
      }
    }
    
    console.log(`Found ${NEW_MIGRATIONS.length} migration files.`);
    
    // Ask for confirmation
    console.log('\n⚠️  WARNING: This will reset your database and apply the new schema.');
    console.log('All existing data will be lost.');
    console.log('\nPress Ctrl+C to cancel or wait 5 seconds to continue...');
    
    // Wait for 5 seconds
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Reset the database
    console.log('\nResetting database...');
    
    try {
      // Use Supabase CLI to reset the database
      execSync('supabase db reset', {
        stdio: 'inherit'
      });
      
      console.log('Database reset successfully.');
    } catch (error) {
      console.error('Error resetting database:', error.message);
      process.exit(1);
    }
    
    // Apply each migration
    for (const migrationFile of NEW_MIGRATIONS) {
      const filePath = path.join(MIGRATIONS_DIR, migrationFile);
      console.log(`\nApplying migration: ${migrationFile}`);
      
      try {
        // Read the SQL file
        const sql = fs.readFileSync(filePath, 'utf8');
        
        // Split the SQL into statements
        const statements = sql
          .split(';')
          .filter(stmt => stmt.trim().length > 0)
          .map(stmt => stmt.trim() + ';');
        
        console.log(`Found ${statements.length} SQL statements to execute`);
        
        // Execute each statement
        for (const [index, statement] of statements.entries()) {
          try {
            console.log(`Executing statement ${index + 1}/${statements.length}...`);
            
            // Execute the SQL statement
            const { error } = await supabase.rpc('exec_sql', { sql: statement });
            
            if (error) {
              console.error(`Error executing statement ${index + 1}:`, error);
              // Continue with next statement
            } else {
              console.log(`Statement ${index + 1} executed successfully`);
            }
          } catch (stmtError) {
            console.error(`Error executing statement ${index + 1}:`, stmtError);
            // Continue with next statement
          }
        }
        
        console.log(`Migration ${migrationFile} applied successfully`);
      } catch (error) {
        console.error(`Error applying migration ${migrationFile}:`, error.message);
        process.exit(1);
      }
    }
    
    console.log('\nAll migrations applied successfully.');
    console.log('\nCreating default roles...');
    
    // Create default admin user with teacher role
    try {
      // Create a default teacher user
      const { data: user, error: userError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'password123',
        user_metadata: {
          first_name: 'Admin',
          last_name: 'User',
          full_name: 'Admin User'
        },
        email_confirm: true
      });
      
      if (userError) {
        console.error('Error creating admin user:', userError);
      } else {
        console.log('Admin user created successfully');
        
        // Assign teacher role
        const { error: roleError } = await supabase.rpc('assign_role', {
          _user_id: user.user.id,
          _role: 'teacher'
        });
        
        if (roleError) {
          console.error('Error assigning teacher role:', roleError);
        } else {
          console.log('Teacher role assigned successfully');
        }
      }
    } catch (error) {
      console.error('Error creating default users:', error);
    }
    
    console.log('\nDatabase setup completed successfully.');
    return true;
  } catch (error) {
    console.error('Error applying new database schema:', error);
    return false;
  }
}

// Run the function
applyNewDatabase()
  .then(success => {
    if (success) {
      console.log('New database schema applied successfully');
      process.exit(0);
    } else {
      console.error('Failed to apply new database schema');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
