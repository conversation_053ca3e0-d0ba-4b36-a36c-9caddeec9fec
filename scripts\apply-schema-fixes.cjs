/**
 * Apply Database Schema Fixes
 * This script applies the critical database schema fixes using the Supabase API
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('VITE_SUPABASE_URL:', SUPABASE_URL ? 'Set' : 'Not set');
  console.error('VITE_SUPABASE_SERVICE_ROLE_KEY:', SUPABASE_SERVICE_KEY ? 'Set' : 'Not set');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function executeSQL(sql, description) {
  console.log(`\n🔧 ${description}...`);
  try {
    // Split SQL into individual statements and execute them one by one
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

    for (const statement of statements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement.length === 0) continue;

      // Use the from() method to execute SQL
      const { data, error } = await supabase.rpc('exec_sql', { sql: trimmedStatement });

      if (error) {
        console.error(`❌ Error executing statement: ${error.message}`);
        console.error(`Statement: ${trimmedStatement.substring(0, 100)}...`);
        return false;
      }
    }

    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ Error: ${err.message}`);
    return false;
  }
}

async function createHealthCheckFunction() {
  const sql = `
    CREATE OR REPLACE FUNCTION public.health_check()
    RETURNS jsonb
    LANGUAGE SQL
    SECURITY DEFINER
    AS $$
      SELECT jsonb_build_object(
        'status', 'ok',
        'timestamp', now(),
        'version', current_setting('server_version')
      );
    $$;

    GRANT EXECUTE ON FUNCTION public.health_check() TO anon;
    GRANT EXECUTE ON FUNCTION public.health_check() TO authenticated;
  `;

  return await executeSQL(sql, 'Creating health check function');
}

async function createRLSHelperFunction() {
  const sql = `
    CREATE OR REPLACE FUNCTION public.get_tables_with_rls()
    RETURNS TABLE (
      table_schema text,
      table_name text,
      rls_enabled boolean
    )
    LANGUAGE SQL SECURITY DEFINER
    AS $$
      SELECT
        n.nspname AS table_schema,
        c.relname AS table_name,
        c.relrowsecurity AS rls_enabled
      FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE c.relkind = 'r'
        AND n.nspname = 'public'
      ORDER BY table_schema, table_name;
    $$;

    GRANT EXECUTE ON FUNCTION public.get_tables_with_rls() TO service_role;
  `;

  return await executeSQL(sql, 'Creating RLS helper function');
}

async function createUserModuleProgressTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS public.user_module_progress (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      module_id UUID NOT NULL,
      is_completed BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(user_id, module_id)
    );

    ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;

    CREATE INDEX IF NOT EXISTS idx_user_module_progress_user_id ON public.user_module_progress(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_module_progress_module_id ON public.user_module_progress(module_id);
    CREATE INDEX IF NOT EXISTS idx_user_module_progress_completion ON public.user_module_progress(is_completed);
  `;

  return await executeSQL(sql, 'Creating user_module_progress table');
}

async function createUserModuleProgressPolicies() {
  const sql = `
    DROP POLICY IF EXISTS "Users can view their own module progress" ON public.user_module_progress;
    DROP POLICY IF EXISTS "Users can update their own module progress" ON public.user_module_progress;
    DROP POLICY IF EXISTS "Users can insert their own module progress" ON public.user_module_progress;

    CREATE POLICY "Users can view their own module progress"
      ON public.user_module_progress
      FOR SELECT
      USING (auth.uid() = user_id);

    CREATE POLICY "Users can update their own module progress"
      ON public.user_module_progress
      FOR UPDATE
      USING (auth.uid() = user_id);

    CREATE POLICY "Users can insert their own module progress"
      ON public.user_module_progress
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);
  `;

  return await executeSQL(sql, 'Creating user_module_progress RLS policies');
}

async function createStorageBuckets() {
  console.log('\n🔧 Creating storage buckets...');

  const buckets = [
    { id: 'course-images', name: 'course-images', public: true },
    { id: 'avatars', name: 'avatars', public: true },
    { id: 'app-uploads', name: 'app-uploads', public: true }
  ];

  let success = true;

  for (const bucket of buckets) {
    try {
      const { data, error } = await supabase.storage.createBucket(bucket.id, {
        public: bucket.public,
        allowedMimeTypes: ['image/*', 'application/pdf'],
        fileSizeLimit: 10485760 // 10MB
      });

      if (error && !error.message.includes('already exists')) {
        console.error(`❌ Error creating bucket ${bucket.id}: ${error.message}`);
        success = false;
      } else {
        console.log(`✅ Bucket ${bucket.id} created/verified`);
      }
    } catch (err) {
      console.error(`❌ Error creating bucket ${bucket.id}: ${err.message}`);
      success = false;
    }
  }

  return success;
}

async function main() {
  console.log('🚀 Starting database schema fixes...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);

  let allSuccess = true;

  // Step 1: Create health check function
  if (!await createHealthCheckFunction()) {
    allSuccess = false;
  }

  // Step 2: Create RLS helper function
  if (!await createRLSHelperFunction()) {
    allSuccess = false;
  }

  // Step 3: Create user_module_progress table
  if (!await createUserModuleProgressTable()) {
    allSuccess = false;
  }

  // Step 4: Create RLS policies for user_module_progress
  if (!await createUserModuleProgressPolicies()) {
    allSuccess = false;
  }

  // Step 5: Create storage buckets
  if (!await createStorageBuckets()) {
    allSuccess = false;
  }

  console.log('\n' + '='.repeat(50));
  if (allSuccess) {
    console.log('🎉 All database schema fixes applied successfully!');
    console.log('✅ Task 5: Database Schema Issues - COMPLETED');
  } else {
    console.log('⚠️  Some fixes failed. Check the errors above.');
    console.log('🔧 You may need to run some SQL manually in Supabase SQL Editor');
  }
  console.log('='.repeat(50));
}

main().catch(console.error);
