// This script assigns the teacher role to a user
// Usage: node assign-teacher-role.js <user-id>

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function assignTeacherRole(userId) {
  try {
    console.log(`Attempting to assign teacher role to user: ${userId}`);
    
    // First, check if the user already has a role
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking existing role:', checkError);
      return false;
    }
    
    let result;
    
    if (existingRole) {
      // Update existing role
      console.log('User already has a role, updating to teacher');
      result = await supabase
        .from('user_roles')
        .update({ role: 'teacher' })
        .eq('user_id', userId);
    } else {
      // Insert new role
      console.log('Creating new teacher role for user');
      result = await supabase
        .from('user_roles')
        .insert([{ user_id: userId, role: 'teacher' }]);
    }
    
    if (result.error) {
      console.error('Error assigning teacher role:', result.error);
      return false;
    }
    
    // Call the assign_role function as a fallback
    const { error: fnError } = await supabase
      .rpc('assign_role', { _user_id: userId, _role: 'teacher' });
      
    if (fnError) {
      console.warn('Error calling assign_role function:', fnError);
      // Continue anyway since we already tried the direct approach
    }
    
    console.log('Teacher role assigned successfully');
    return true;
  } catch (error) {
    console.error('Error in assignTeacherRole:', error);
    return false;
  }
}

// Get user ID from command line arguments
const userId = process.argv[2];

if (!userId) {
  console.error('Please provide a user ID');
  process.exit(1);
}

// Assign teacher role
assignTeacherRole(userId)
  .then(success => {
    if (success) {
      console.log('Teacher role assigned successfully');
    } else {
      console.error('Failed to assign teacher role');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
