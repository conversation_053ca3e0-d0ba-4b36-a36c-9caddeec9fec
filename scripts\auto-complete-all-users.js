// <PERSON><PERSON><PERSON> to auto-complete all courses for all users
// This script will:
// 1. Get all users from the auth.users table
// 2. For each user, mark all courses as completed

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project-url.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'your-service-key';
const supabase = createClient(supabaseUrl, supabaseKey);

async function autoCompleteForAllUsers() {
  console.log('Starting auto-completion for all users...');
  
  try {
    // Step 1: Get all users
    console.log('Fetching all users...');
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      process.exit(1);
    }
    
    console.log(`Found ${users.users.length} users`);
    
    // Step 2: Get all courses
    console.log('Fetching all courses...');
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title');
    
    if (coursesError) {
      console.error('Error fetching courses:', coursesError);
      process.exit(1);
    }
    
    console.log(`Found ${courses.length} courses`);
    
    // Step 3: For each user, mark all courses as completed
    let successCount = 0;
    
    for (const user of users.users) {
      console.log(`Processing user ${user.id} (${user.email})...`);
      
      try {
        // Call the complete_all_courses RPC function
        const { data, error } = await supabase.rpc('complete_all_courses', {
          p_user_id: user.id
        });
        
        if (error) {
          console.error(`Error auto-completing courses for user ${user.id}:`, error);
          
          // Try the fallback approach - mark each course individually
          console.log('Trying fallback approach...');
          let courseSuccessCount = 0;
          
          for (const course of courses) {
            try {
              const { error: courseError } = await supabase.rpc('complete_course', {
                p_user_id: user.id,
                p_course_id: course.id
              });
              
              if (!courseError) {
                courseSuccessCount++;
              }
            } catch (courseError) {
              console.error(`Error completing course ${course.id} for user ${user.id}:`, courseError);
            }
          }
          
          console.log(`Successfully completed ${courseSuccessCount} out of ${courses.length} courses for user ${user.id}`);
          
          if (courseSuccessCount > 0) {
            successCount++;
          }
        } else {
          console.log(`Successfully auto-completed all courses for user ${user.id}`);
          successCount++;
        }
      } catch (userError) {
        console.error(`Error processing user ${user.id}:`, userError);
      }
    }
    
    console.log(`Successfully processed ${successCount} out of ${users.users.length} users`);
    console.log('Auto-completion script completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the script
autoCompleteForAllUsers();
