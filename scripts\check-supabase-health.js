/**
 * Comprehensive Supabase Health Check
 * 
 * This script performs a complete health check on your Supabase setup:
 * - Verifies connection to the database
 * - Checks RLS policies on key tables
 * - Validates database schema
 * - Tests read/write operations
 * - Verifies storage buckets
 * - Tests the health_check RPC function
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Set up colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

// Results object to track health check status
const results = {
  envVars: { status: 'pending', issues: [], warnings: [] },
  connection: { status: 'pending', issues: [] },
  rls: { status: 'pending', issues: [], warnings: [] },
  schema: { status: 'pending', issues: [], warnings: [] },
  readWrite: { status: 'pending', issues: [] },
  storage: { status: 'pending', issues: [], warnings: [] },
  rpc: { status: 'pending', issues: [] }
};

// Create clients
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const supabaseAdmin = SUPABASE_SERVICE_KEY ? 
  createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY) : null;

// Print header
console.log(`${colors.bright}${colors.cyan}=== Supabase Health Check ===${colors.reset}`);
console.log(`URL: ${SUPABASE_URL ? SUPABASE_URL : colors.red + 'Not set' + colors.reset}`);
console.log('');

// Helper to log status with color
const logStatus = (message, status) => {
  const icon = status === 'success' ? '✅' : status === 'warning' ? '⚠️' : '❌';
  const color = status === 'success' ? colors.green : status === 'warning' ? colors.yellow : colors.red;
  console.log(`${icon} ${color}${message}${colors.reset}`);
};

// Check 1: Verify environment variables
function checkEnvironmentVariables() {
  console.log(`${colors.bright}=== Checking Environment Variables ===${colors.reset}`);
  
  if (!SUPABASE_URL) {
    results.envVars.issues.push('VITE_SUPABASE_URL is not set');
  }
  
  if (!SUPABASE_ANON_KEY) {
    results.envVars.issues.push('VITE_SUPABASE_ANON_KEY is not set');
  }
  
  if (!SUPABASE_SERVICE_KEY) {
    results.envVars.warnings.push('VITE_SUPABASE_SERVICE_ROLE_KEY is not set (required for admin operations)');
  }
  
  if (results.envVars.issues.length > 0) {
    logStatus('Found critical issues:', 'error');
    results.envVars.issues.forEach(issue => console.log(`  - ${issue}`));
    results.envVars.status = 'error';
  } else {
    results.envVars.status = 'success';
    
    if (results.envVars.warnings.length > 0) {
      logStatus('Warnings:', 'warning');
      results.envVars.warnings.forEach(warning => console.log(`  - ${warning}`));
    } else {
      logStatus('All environment variables are properly set', 'success');
    }
  }
  
  return results.envVars.issues.length === 0; // Only fail for critical issues
}

// Check 2: Test basic connection
async function testConnection() {
  console.log(`\n${colors.bright}=== Testing Supabase Connection ===${colors.reset}`);
  
  try {
    // Try to access public schema tables
    const { data, error } = await supabase
      .from('courses')
      .select('count')
      .limit(1);
    
    if (error) {
      try {
        // Try a second table if the first fails
        const { error: error2 } = await supabase
          .from('user_roles')
          .select('count')
          .limit(1);
          
        if (error2) {
          results.connection.issues.push(`First error: ${error.message}`);
          results.connection.issues.push(`Second error: ${error2.message}`);
          logStatus('Connection failed', 'error');
          console.log(`  - First error: ${error.message}`);
          console.log(`  - Second error: ${error2.message}`);
          results.connection.status = 'error';
          return false;
        }
      } catch (err) {
        results.connection.issues.push(error.message);
        logStatus('Connection failed', 'error');
        console.log(`  - ${error.message}`);
        results.connection.status = 'error';
        return false;
      }
    }
    
    // Try to use the health_check RPC if available
    try {
      const { data: healthData, error: healthError } = await supabase.rpc('health_check');
      
      if (!healthError && healthData && healthData.status === 'ok') {
        logStatus('Successfully connected to Supabase and health_check function is working', 'success');
        console.log(`  - Database version: ${healthData.version}`);
        console.log(`  - Server time: ${new Date(healthData.timestamp).toLocaleString()}`);
      } else {
        logStatus('Connected to Supabase, but health_check function returned an error or is not available', 'warning');
      }
    } catch (err) {
      // Health check function might not be available, that's ok
      logStatus('Successfully connected to Supabase', 'success');
      console.log(`  - Note: health_check RPC function is not available`);
    }
    
    results.connection.status = 'success';
    return true;
  } catch (error) {
    results.connection.issues.push(error.message);
    logStatus('Connection failed', 'error');
    console.log(`  - ${error.message}`);
    results.connection.status = 'error';
    return false;
  }
}

// Check 3: Verify RLS policies on important tables
async function checkRlsPolicies() {
  console.log(`\n${colors.bright}=== Checking RLS Policies ===${colors.reset}`);
  
  if (!supabaseAdmin) {
    results.rls.warnings.push('Service key not available, skipping RLS check');
    logStatus('Skipping RLS check: Service key not available', 'warning');
    results.rls.status = 'warning';
    return true;
  }
  
  try {
    // Try to check RLS policies using the get_tables_with_rls function
    try {
      const { data, error } = await supabaseAdmin.rpc('get_tables_with_rls');
      
      if (error) {
        results.rls.warnings.push(`Could not check RLS policies: ${error.message}`);
        logStatus('Could not check RLS policies', 'warning');
        console.log(`  - This may be because the get_tables_with_rls function is not installed.`);
        console.log(`  - Run the RLS helper functions migration to add this capability.`);
        results.rls.status = 'warning';
        return true; // Continue with health check anyway
      }
      
      // Tables that should have RLS enabled
      const criticalTables = [
        'profiles', 
        'user_roles', 
        'courses', 
        'modules', 
        'lessons',
        'user_course_enrollment',
        'user_course_progress',
        'user_module_progress',
        'user_lesson_progress',
        'quizzes',
        'quiz_questions',
        'quiz_attempts'
      ];
      
      const missingRls = criticalTables.filter(
        table => !data.find(row => row.table_name === table && row.rls_enabled)
      );
      
      if (missingRls.length > 0) {
        results.rls.warnings.push(`${missingRls.length} tables missing RLS`);
        logStatus('Found tables without RLS enabled', 'warning');
        missingRls.forEach(table => console.log(`  - ${table}`));
        results.rls.status = 'warning';
      } else {
        logStatus('All critical tables have RLS enabled', 'success');
        results.rls.status = 'success';
      }
    } catch (error) {
      results.rls.warnings.push(`RLS check failed: ${error.message}`);
      logStatus('RLS check failed', 'warning');
      console.log(`  - This is likely because the get_tables_with_rls function is not available`);
      results.rls.status = 'warning';
    }
    
    return true;
  } catch (error) {
    results.rls.warnings.push(`RLS check failed: ${error.message}`);
    logStatus('RLS check failed', 'warning');
    console.log(`  - ${error.message}`);
    results.rls.status = 'warning';
    return true; // Continue with health check anyway
  }
}

// Check 4: Validate database schema
async function validateSchema() {
  console.log(`\n${colors.bright}=== Validating Database Schema ===${colors.reset}`);
  
  try {
    // List of expected tables
    const expectedTables = [
      'profiles',
      'user_roles',
      'courses',
      'modules',
      'lessons',
      'user_course_enrollment',
      'user_course_progress',
      'user_module_progress',
      'user_lesson_progress',
      'quizzes',
      'quiz_questions',
      'quiz_attempts'
    ];
    
    // Get actual tables
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_type', ['BASE TABLE']);
    
    if (error) {
      results.schema.warnings.push(`Schema validation failed: ${error.message}`);
      logStatus('Schema validation failed', 'warning');
      console.log(`  - ${error.message}`);
      results.schema.status = 'warning';
      return true; // Continue with health check anyway
    }
    
    const actualTables = data.map(row => row.table_name);
    const missingTables = expectedTables.filter(table => !actualTables.includes(table));
    
    if (missingTables.length > 0) {
      results.schema.warnings.push(`${missingTables.length} expected tables are missing`);
      logStatus('Missing tables in schema', 'warning');
      missingTables.forEach(table => console.log(`  - ${table}`));
      results.schema.status = 'warning';
    } else {
      logStatus('All expected tables exist in the database', 'success');
      results.schema.status = 'success';
    }
    
    return true; // Continue with health check anyway
  } catch (error) {
    results.schema.warnings.push(`Schema validation failed: ${error.message}`);
    logStatus('Schema validation failed', 'warning');
    console.log(`  - ${error.message}`);
    results.schema.status = 'warning';
    return true; // Continue with health check anyway
  }
}

// Check 5: Test read/write operations
async function testReadWrite() {
  console.log(`\n${colors.bright}=== Testing Read/Write Operations ===${colors.reset}`);
  
  if (!supabaseAdmin) {
    results.readWrite.warnings.push('Service key not available, skipping read/write test');
    logStatus('Skipping read/write test: Service key not available', 'warning');
    results.readWrite.status = 'warning';
    return true;
  }
  
  try {
    // 1. Generate a test UUID
    const testId = 'test-' + Math.random().toString(36).substring(2, 15);
    
    // 2. Create a temporary record
    const { error: insertError } = await supabaseAdmin
      .from('user_roles')
      .insert({
        id: testId,
        user_id: testId,
        role: 'student'
      });
    
    if (insertError) {
      results.readWrite.issues.push(`Write operation failed: ${insertError.message}`);
      logStatus('Write operation failed', 'error');
      console.log(`  - ${insertError.message}`);
      results.readWrite.status = 'error';
      return true; // Continue with health check anyway
    }
    
    // 3. Read the record back
    const { data, error: readError } = await supabaseAdmin
      .from('user_roles')
      .select('*')
      .eq('id', testId)
      .single();
    
    if (readError || !data) {
      results.readWrite.issues.push(`Read operation failed: ${readError?.message || 'Record not found'}`);
      logStatus('Read operation failed', 'error');
      console.log(`  - ${readError?.message || 'Record not found'}`);
      results.readWrite.status = 'error';
      return true; // Continue with health check anyway
    }
    
    // 4. Delete the test record
    const { error: deleteError } = await supabaseAdmin
      .from('user_roles')
      .delete()
      .eq('id', testId);
    
    if (deleteError) {
      results.readWrite.issues.push(`Delete operation failed: ${deleteError.message}`);
      logStatus('Delete operation failed', 'warning');
      console.log(`  - ${deleteError.message}`);
      console.log(`  - Manual cleanup may be needed for test record with id ${testId}`);
      results.readWrite.status = 'warning';
      return true; // Continue with health check anyway
    }
    
    logStatus('All read/write operations successful', 'success');
    results.readWrite.status = 'success';
    return true;
  } catch (error) {
    results.readWrite.issues.push(`Read/write test failed: ${error.message}`);
    logStatus('Read/write test failed', 'error');
    console.log(`  - ${error.message}`);
    results.readWrite.status = 'error';
    return true; // Continue with health check anyway
  }
}

// Check 6: Test storage buckets
async function checkStorageBuckets() {
  console.log(`\n${colors.bright}=== Checking Storage Buckets ===${colors.reset}`);
  
  // Expected storage buckets
  const expectedBuckets = [
    'course-images',
    'avatars',
    'app-uploads'
  ];
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      results.storage.issues.push(`Storage check failed: ${error.message}`);
      logStatus('Storage check failed', 'error');
      console.log(`  - ${error.message}`);
      results.storage.status = 'error';
      return true; // Continue anyway
    }
    
    const actualBuckets = buckets.map(bucket => bucket.name);
    const missingBuckets = expectedBuckets.filter(bucket => !actualBuckets.includes(bucket));
    
    if (missingBuckets.length > 0) {
      results.storage.warnings.push(`${missingBuckets.length} expected storage buckets are missing`);
      logStatus('Missing storage buckets', 'warning');
      missingBuckets.forEach(bucket => console.log(`  - ${bucket}`));
      results.storage.status = 'warning';
    } else {
      logStatus('All expected storage buckets exist', 'success');
      results.storage.status = 'success';
    }
    
    return true;
  } catch (error) {
    results.storage.issues.push(`Storage check failed: ${error.message}`);
    logStatus('Storage check failed', 'error');
    console.log(`  - ${error.message}`);
    results.storage.status = 'error';
    return true; // Continue anyway
  }
}

// Check 7: Test RPC functions
async function checkRpcFunctions() {
  console.log(`\n${colors.bright}=== Checking RPC Functions ===${colors.reset}`);
  
  // Initialize the status early to avoid undefined errors
  results.rpc.status = 'pending';
  
  try {
    // Try to call the health_check RPC function
    const { data, error } = await supabase.rpc('health_check');
    
    if (error) {
      results.rpc.warnings = results.rpc.warnings || [];
      results.rpc.warnings.push(`health_check RPC failed: ${error.message}`);
      logStatus('health_check RPC function not available or failed', 'warning');
      console.log(`  - ${error.message}`);
      console.log(`  - Run the RLS helper functions migration to add this function.`);
      results.rpc.status = 'warning';
    } else {
      logStatus('health_check RPC function is working', 'success');
      if (data) {
        console.log(`  - Status: ${data.status || 'unknown'}`);
        console.log(`  - Timestamp: ${data.timestamp ? new Date(data.timestamp).toLocaleString() : 'not available'}`);
        console.log(`  - Database version: ${data.version || 'unknown'}`);
      }
      results.rpc.status = 'success';
    }
    
    return true;
  } catch (error) {
    results.rpc.warnings = results.rpc.warnings || [];
    results.rpc.warnings.push(`RPC check failed: ${error.message}`);
    logStatus('RPC check failed', 'warning');
    console.log(`  - ${error.message}`);
    console.log(`  - This is likely because the health_check function is not available`);
    results.rpc.status = 'warning';
    return true; // Continue anyway
  }
}

// Run all checks
async function runHealthCheck() {
  console.log('Starting Supabase health check...\n');
  
  const envCheck = checkEnvironmentVariables();
  if (!envCheck) {
    console.log(`\n${colors.red}❌ Environment variables check failed. Fix these issues first.${colors.reset}`);
    return;
  }
  
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.log(`\n${colors.red}❌ Connection check failed. Cannot proceed with other checks.${colors.reset}`);
    console.log('\nTroubleshooting steps:');
    console.log('1. Verify your VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY values');
    console.log('2. Check if your Supabase project is active and not in maintenance mode');
    console.log('3. Make sure your IP is not blocked by Supabase');
    return;
  }
  
  // Run additional checks, but don't stop if individual checks fail
  await checkRlsPolicies();
  await validateSchema();
  await testReadWrite();
  await checkStorageBuckets();
  await checkRpcFunctions();
  
  // Overall health status
  console.log(`\n${colors.bright}${colors.cyan}=== Health Check Summary ===${colors.reset}`);
  
  // Initialize all result properties to avoid undefined errors
  Object.keys(results).forEach(key => {
    results[key].issues = results[key].issues || [];
    results[key].warnings = results[key].warnings || [];
  });
  
  // Count issues and warnings
  const criticalIssues = Object.values(results).reduce((count, check) => count + check.issues.length, 0);
  const warnings = Object.values(results).reduce((count, check) => count + check.warnings.length, 0);
  
  if (criticalIssues === 0 && warnings === 0) {
    console.log(`${colors.green}✅ Your Supabase setup is healthy! All checks passed.${colors.reset}`);
  } else if (criticalIssues === 0) {
    console.log(`${colors.yellow}⚠️ Your Supabase connection is working, but there are ${warnings} warnings.${colors.reset}`);
    console.log(`   See the details above for specific issues that need attention.`);
  } else {
    console.log(`${colors.yellow}⚠️ Your Supabase connection is working, but there are ${criticalIssues} critical issues and ${warnings} warnings.${colors.reset}`);
    console.log(`   Fix the critical issues first, then address the warnings.`);
  }
  
  // Provide fix command suggestion if get_tables_with_rls is missing
  if (results.rls.warnings.some(w => w && w.includes && w.includes('get_tables_with_rls'))) {
    console.log(`\n${colors.bright}Suggested fix: ${colors.reset}`);
    console.log(`Run the following command to install the get_tables_with_rls function:`);
    console.log(`node scripts/migrate.js --create --name=add_rls_helper_functions`);
    console.log(`(Then edit the migration and run it)`);
  }
}

// Run the health check
runHealthCheck().catch(error => {
  console.error(`${colors.red}Health check failed with an unexpected error:${colors.reset}`, error);
  process.exit(1);
}); 