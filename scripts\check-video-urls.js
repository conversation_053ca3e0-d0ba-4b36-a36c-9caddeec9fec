/**
 * Check Video URLs in Lessons Script
 * 
 * This script checks all lessons for video URL issues and provides fixes:
 * 1. Identifies lessons with video URLs
 * 2. Checks if video URLs are properly formatted
 * 3. Tests if video URLs are accessible
 * 4. Provides fixes for common video URL issues
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

/**
 * Process video URL to proper embed format
 */
function processVideoUrl(url) {
  if (!url) return null;
  
  // For data URLs, return as is
  if (url.startsWith('data:')) return url;
  
  // Handle YouTube URLs
  if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
    const videoId = url.includes('youtu.be') 
      ? url.split('/').pop()?.split('?')[0]
      : new URL(url).searchParams.get('v');
    
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}`;
    }
  }
  
  // Handle Vimeo URLs
  if (url.includes('vimeo.com')) {
    const vimeoId = url.split('/').pop()?.split('?')[0];
    if (vimeoId && !isNaN(vimeoId)) {
      return `https://player.vimeo.com/video/${vimeoId}`;
    }
  }
  
  // Handle other embed URLs
  if (url.includes('embed')) {
    return url;
  }
  
  return url;
}

/**
 * Check if a video URL is accessible
 */
async function checkVideoUrlAccessibility(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Parse lesson content to extract video URLs
 */
function parseContentForVideo(content) {
  if (!content) return null;
  
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(content);
    if (parsed && typeof parsed === 'object' && parsed.videoUrl) {
      return parsed.videoUrl;
    }
  } catch (e) {
    // Not JSON, check for video URLs in markdown
    const videoUrlRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/|vimeo\.com\/|player\.vimeo\.com\/video\/)([a-zA-Z0-9_-]+)/g;
    const match = content.match(videoUrlRegex);
    if (match && match.length > 0) {
      return match[0];
    }
  }
  
  return null;
}

/**
 * Get all lessons with potential video content
 */
async function getLessonsWithVideos() {
  console.log(`${colors.blue}🔍 Fetching lessons with video content...${colors.reset}`);
  
  const { data: lessons, error } = await supabase
    .from('lessons')
    .select(`
      id,
      title,
      slug,
      content,
      video_url,
      type,
      module_id,
      modules:module_id (title)
    `)
    .order('created_at');
  
  if (error) {
    console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
    return [];
  }
  
  return lessons || [];
}

/**
 * Analyze video URLs in lessons
 */
async function analyzeVideoUrls() {
  console.log(`${colors.cyan}🎥 Analyzing Video URLs in Lessons${colors.reset}\n`);
  
  const lessons = await getLessonsWithVideos();
  
  if (lessons.length === 0) {
    console.log(`${colors.yellow}⚠️ No lessons found${colors.reset}`);
    return;
  }
  
  console.log(`${colors.green}✅ Found ${lessons.length} lessons${colors.reset}\n`);
  
  const videoIssues = [];
  const videoLessons = [];
  
  for (const lesson of lessons) {
    const contentVideoUrl = parseContentForVideo(lesson.content);
    const dbVideoUrl = lesson.video_url;
    
    // Check if lesson has any video content
    if (contentVideoUrl || dbVideoUrl) {
      videoLessons.push(lesson);
      
      console.log(`${colors.blue}📹 Lesson: ${lesson.title}${colors.reset}`);
      console.log(`   Module: ${lesson.modules?.title || 'Unknown'}`);
      console.log(`   Type: ${lesson.type}`);
      
      if (contentVideoUrl) {
        console.log(`   Content Video URL: ${contentVideoUrl}`);
        const processedUrl = processVideoUrl(contentVideoUrl);
        if (processedUrl !== contentVideoUrl) {
          console.log(`   ${colors.yellow}⚠️ Should be: ${processedUrl}${colors.reset}`);
          videoIssues.push({
            lessonId: lesson.id,
            lessonTitle: lesson.title,
            issue: 'content_video_url_needs_processing',
            currentUrl: contentVideoUrl,
            suggestedUrl: processedUrl
          });
        }
      }
      
      if (dbVideoUrl) {
        console.log(`   DB Video URL: ${dbVideoUrl}`);
        const processedUrl = processVideoUrl(dbVideoUrl);
        if (processedUrl !== dbVideoUrl) {
          console.log(`   ${colors.yellow}⚠️ Should be: ${processedUrl}${colors.reset}`);
          videoIssues.push({
            lessonId: lesson.id,
            lessonTitle: lesson.title,
            issue: 'db_video_url_needs_processing',
            currentUrl: dbVideoUrl,
            suggestedUrl: processedUrl
          });
        }
      }
      
      console.log('');
    }
  }
  
  console.log(`${colors.green}📊 Summary:${colors.reset}`);
  console.log(`   Total lessons: ${lessons.length}`);
  console.log(`   Lessons with videos: ${videoLessons.length}`);
  console.log(`   Video URL issues found: ${videoIssues.length}`);
  
  if (videoIssues.length > 0) {
    console.log(`\n${colors.red}🚨 Issues Found:${colors.reset}`);
    videoIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.lessonTitle}`);
      console.log(`   Issue: ${issue.issue}`);
      console.log(`   Current: ${issue.currentUrl}`);
      console.log(`   Suggested: ${issue.suggestedUrl}`);
      console.log('');
    });
    
    return videoIssues;
  }
  
  console.log(`\n${colors.green}✅ No video URL issues found!${colors.reset}`);
  return [];
}

/**
 * Fix video URL issues
 */
async function fixVideoUrls(issues) {
  if (issues.length === 0) {
    console.log(`${colors.green}✅ No issues to fix${colors.reset}`);
    return;
  }
  
  console.log(`${colors.yellow}🔧 Fixing ${issues.length} video URL issues...${colors.reset}\n`);
  
  for (const issue of issues) {
    try {
      if (issue.issue === 'content_video_url_needs_processing') {
        // Update content with processed video URL
        const { data: lesson } = await supabase
          .from('lessons')
          .select('content')
          .eq('id', issue.lessonId)
          .single();
        
        if (lesson?.content) {
          try {
            const parsed = JSON.parse(lesson.content);
            parsed.videoUrl = issue.suggestedUrl;
            
            const { error } = await supabase
              .from('lessons')
              .update({ content: JSON.stringify(parsed) })
              .eq('id', issue.lessonId);
            
            if (error) throw error;
            
            console.log(`${colors.green}✅ Fixed content video URL for: ${issue.lessonTitle}${colors.reset}`);
          } catch (parseError) {
            console.log(`${colors.yellow}⚠️ Could not parse content for: ${issue.lessonTitle}${colors.reset}`);
          }
        }
      } else if (issue.issue === 'db_video_url_needs_processing') {
        // Update video_url column
        const { error } = await supabase
          .from('lessons')
          .update({ video_url: issue.suggestedUrl })
          .eq('id', issue.lessonId);
        
        if (error) throw error;
        
        console.log(`${colors.green}✅ Fixed DB video URL for: ${issue.lessonTitle}${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}❌ Error fixing ${issue.lessonTitle}: ${error.message}${colors.reset}`);
    }
  }
  
  console.log(`\n${colors.green}🎉 Video URL fixes completed!${colors.reset}`);
}

/**
 * Main function
 */
async function main() {
  try {
    const issues = await analyzeVideoUrls();
    
    if (issues.length > 0) {
      console.log(`\n${colors.yellow}Would you like to fix these issues? (This will update the database)${colors.reset}`);
      console.log(`${colors.cyan}Run with --fix flag to apply fixes automatically${colors.reset}`);
      
      // Check if --fix flag is provided
      if (process.argv.includes('--fix')) {
        await fixVideoUrls(issues);
      }
    }
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main();
