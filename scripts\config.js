/**
 * Centralized configuration for scripts
 * 
 * This file provides a single source of truth for configuration values
 * used across various scripts, preventing hardcoded credentials.
 */
require('dotenv').config();

// Supabase configuration
const SUPABASE_CONFIG = {
  // URL for the Supabase project
  URL: process.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co',
  
  // Anonymous/public API key (limited permissions)
  ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY,
  
  // Service role key (admin permissions) - only used in secure contexts
  SERVICE_ROLE_KEY: process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY,
  
  // Project ID - used for some API operations
  PROJECT_ID: 'jibspqwieubavucdtccv',
  
  // Default schema
  SCHEMA: 'public',
  
  // Storage bucket names
  STORAGE: {
    COURSE_IMAGES: 'course-images',
    AVATARS: 'avatars',
    APP_UPLOADS: 'app-uploads',
  }
};

// Validate configuration
if (!SUPABASE_CONFIG.URL) {
  console.error('VITE_SUPABASE_URL is not set. Please check your environment variables.');
}

if (!SUPABASE_CONFIG.ANON_KEY) {
  console.error('VITE_SUPABASE_ANON_KEY is not set. Please check your environment variables.');
}

if (!SUPABASE_CONFIG.SERVICE_ROLE_KEY) {
  console.warn('No service role key found. Admin operations will not work.');
  console.warn('Please set either VITE_SUPABASE_SERVICE_ROLE_KEY or SUPABASE_SERVICE_KEY.');
}

module.exports = {
  SUPABASE_CONFIG,
  SUPABASE_URL: SUPABASE_CONFIG.URL,
  SUPABASE_ANON_KEY: SUPABASE_CONFIG.ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: SUPABASE_CONFIG.SERVICE_ROLE_KEY,
  SUPABASE_PROJECT_ID: SUPABASE_CONFIG.PROJECT_ID,
};
