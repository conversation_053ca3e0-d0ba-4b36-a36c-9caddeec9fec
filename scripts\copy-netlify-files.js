// scripts/copy-netlify-files.js
const fs = require('fs');
const path = require('path');

// Paths
const publicDir = path.join(__dirname, '..', 'public');
const distDir = path.join(__dirname, '..', 'dist');

// Create dist directory if it doesn't exist
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Default file contents
const defaultFiles = {
  '_redirects': '/* /index.html 200\n',
  '_headers': `/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Access-Control-Allow-Origin: *

/
  Content-Type: text/html; charset=utf-8

/index.html
  Content-Type: text/html; charset=utf-8
  Cache-Control: no-cache

/*.js
  Content-Type: application/javascript; charset=utf-8

*.js
  Content-Type: application/javascript; charset=utf-8

/*.css
  Content-Type: text/css; charset=utf-8

*.css
  Content-Type: text/css; charset=utf-8

/*.svg
  Content-Type: image/svg+xml

/assets/
  Cache-Control: public, max-age=31536000, immutable

/assets/*.js
  Content-Type: application/javascript; charset=utf-8

/assets/**.js
  Content-Type: application/javascript; charset=utf-8

/assets/*.css
  Content-Type: text/css; charset=utf-8

/assets/**.css
  Content-Type: text/css; charset=utf-8
`,
  'robots.txt': 'User-agent: *\nAllow: /\n'
};

// Files to copy
const filesToCopy = Object.keys(defaultFiles);

// Copy each file
filesToCopy.forEach(file => {
  const sourcePath = path.join(publicDir, file);
  const destPath = path.join(distDir, file);

  if (fs.existsSync(sourcePath)) {
    // Copy existing file
    fs.copyFileSync(sourcePath, destPath);
    console.log(`Copied ${file} to dist folder`);
  } else {
    // Create file with default content
    console.log(`Warning: ${file} not found in public folder, creating with default content`);
    fs.writeFileSync(destPath, defaultFiles[file]);
    console.log(`Created ${file} in dist folder with default content`);
  }
});

// Create a simple index.html if it doesn't exist (fallback)
const indexPath = path.join(distDir, 'index.html');
if (!fs.existsSync(indexPath)) {
  console.log('Warning: index.html not found in dist folder, creating a simple one');
  const simpleHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>IVCAN Course LMS</title>
  <meta http-equiv="refresh" content="0;url=/" />
</head>
<body>
  <p>Redirecting...</p>
</body>
</html>`;
  fs.writeFileSync(indexPath, simpleHtml);
}

console.log('Netlify files copied successfully');
