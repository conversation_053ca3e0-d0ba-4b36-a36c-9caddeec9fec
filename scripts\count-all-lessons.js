const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function countLessons() {
  try {
    console.log('Connecting to Supabase...');
    
    // Get all lessons
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('*');
      
    if (lessonsError) {
      console.error('Error querying lessons:', lessonsError);
      return;
    }
    
    console.log(`\nTotal lessons in the system: ${lessons.length}`);
    
    // Get all modules
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('*');
      
    if (modulesError) {
      console.error('Error querying modules:', modulesError);
    } else {
      console.log(`Total modules in the system: ${modules.length}`);
      
      // Count lessons per module
      const lessonsByModule = {};
      lessons.forEach(lesson => {
        if (!lessonsByModule[lesson.module_id]) {
          lessonsByModule[lesson.module_id] = 0;
        }
        lessonsByModule[lesson.module_id]++;
      });
      
      console.log('\nLesson count by module:');
      for (const moduleId in lessonsByModule) {
        const moduleName = modules.find(m => m.id === moduleId)?.title || 'Unknown Module';
        console.log(`  ${moduleName}: ${lessonsByModule[moduleId]} lessons`);
      }
    }
    
    // Get all courses
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*');
      
    if (coursesError) {
      console.error('Error querying courses:', coursesError);
    } else {
      console.log(`\nTotal courses in the system: ${courses.length}`);
      
      // Display course names
      console.log('\nCourses:');
      courses.forEach(course => {
        console.log(`  ${course.title}`);
      });
    }
    
    // Display sample lesson data
    if (lessons.length > 0) {
      console.log('\nSample lesson:');
      console.log(lessons[0]);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
countLessons()
  .then(() => console.log('\nDone!'))
  .catch(err => console.error('Fatal error:', err)); 