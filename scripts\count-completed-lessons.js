const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function countCompletedLessons() {
  try {
    console.log('Connecting to Supabase...');
    
    // First method: Query all records and count locally
    const { data, error } = await supabase
      .from('user_lesson_progress')
      .select('*');
      
    if (error) {
      console.error('Error querying user_lesson_progress:', error);
      return;
    }
    
    const completedCount = data.filter(record => record.is_completed === true).length;
    console.log(`\nMethod 1: Total completed lessons: ${completedCount}`);
    
    // Second method: Using count() with filter
    const { data: countData, error: countError } = await supabase
      .from('user_lesson_progress')
      .select('*', { count: 'exact', head: true })
      .eq('is_completed', true);
      
    if (countError) {
      console.error('Error counting completed lessons:', countError);
    } else {
      console.log(`Method 2: Total completed lessons: ${countData.count || 'N/A'}`);
    }
    
    // Print additional information
    const totalLessons = data.length;
    console.log(`\nTotal lesson progress records: ${totalLessons}`);
    console.log(`Completion percentage: ${Math.round((completedCount / totalLessons) * 100)}%`);
    
    // Count unique users
    const uniqueUsers = new Set(data.map(record => record.user_id)).size;
    console.log(`Unique users with lesson progress: ${uniqueUsers}`);
    
    // Display sample data
    if (data.length > 0) {
      console.log('\nSample lesson progress record:');
      console.log(data[0]);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
countCompletedLessons()
  .then(() => console.log('\nDone!'))
  .catch(err => console.error('Fatal error:', err)); 