// This script creates the necessary storage buckets in Supabase
// Run this script once to set up the required buckets for the application

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// List of buckets to create
const bucketsToCreate = [
  { name: 'course-images', isPublic: true },
  { name: 'avatars', isPublic: true },
  { name: 'app-uploads', isPublic: true },
  { name: 'uploads', isPublic: true },
  { name: 'default-bucket', isPublic: true }
];

async function createBuckets() {
  console.log('Starting bucket creation process...');

  try {
    // First, list existing buckets
    const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      console.error('This might be due to missing permissions. Make sure you have the correct API keys.');
      return;
    }

    console.log('Existing buckets:', existingBuckets?.map(b => b.name) || 'none');

    // Create each bucket if it doesn't exist
    for (const bucket of bucketsToCreate) {
      if (existingBuckets && existingBuckets.some(b => b.name === bucket.name)) {
        console.log(`Bucket '${bucket.name}' already exists.`);

        // Update bucket to ensure it has the correct settings
        const { error: updateError } = await supabase.storage.updateBucket(bucket.name, {
          public: bucket.isPublic
        });

        if (updateError) {
          console.error(`Error updating bucket '${bucket.name}':`, updateError);
        } else {
          console.log(`Updated bucket '${bucket.name}' settings.`);
        }
      } else {
        console.log(`Creating bucket '${bucket.name}'...`);

        const { data, error: createError } = await supabase.storage.createBucket(bucket.name, {
          public: bucket.isPublic
        });

        if (createError) {
          console.error(`Error creating bucket '${bucket.name}':`, createError);
        } else {
          console.log(`Successfully created bucket '${bucket.name}'.`);
        }
      }
    }

    console.log('Bucket creation process completed.');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createBuckets();
