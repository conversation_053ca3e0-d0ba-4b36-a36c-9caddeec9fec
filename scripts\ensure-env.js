// scripts/ensure-env.js
const fs = require('fs');
const path = require('path');

// Path to the .env file
const envPath = path.join(__dirname, '..', '.env');

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.log('Creating .env file with default values');
  
  const envContent = `VITE_SUPABASE_URL=https://jibspqwieubavucdtccv.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk5NzI5NzYsImV4cCI6MjAxNTU0ODk3Nn0.Yd_LKbxFnEuGQnVwZ8LgsP6APhRZ-6jkU_CWZNmGUXs
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('.env file created successfully');
} else {
  console.log('.env file already exists');
}

// Create a _headers file for Netlify
const headersPath = path.join(__dirname, '..', 'public', '_headers');
const headersContent = `/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
`;

fs.writeFileSync(headersPath, headersContent);
console.log('_headers file created successfully');

console.log('Environment setup completed');
