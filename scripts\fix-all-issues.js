/**
 * Fix All Issues Script
 *
 * This script runs all the fixes for the application
 * Run this script with: node scripts/fix-all-issues.js
 */

const { createClient } = require('@supabase/supabase-js');
const { execSync } = require('child_process');
const { SUPABASE_URL, SUPABASE_ANON_KEY } = require('./config');

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function createUserModuleProgressTable() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Creating user_module_progress table...');

    // Create the table using SQL
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.user_module_progress (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        module_id UUID NOT NULL REFERENCES public.modules(id) ON DELETE CASCADE,
        is_completed BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, module_id)
      );

      -- Add RLS policies
      ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;

      -- Allow users to view their own progress
      CREATE POLICY "Users can view their own module progress"
        ON public.user_module_progress
        FOR SELECT
        USING (auth.uid() = user_id);

      -- Allow users to update their own progress
      CREATE POLICY "Users can update their own module progress"
        ON public.user_module_progress
        FOR UPDATE
        USING (auth.uid() = user_id);

      -- Allow users to insert their own progress
      CREATE POLICY "Users can insert their own module progress"
        ON public.user_module_progress
        FOR INSERT
        WITH CHECK (auth.uid() = user_id);

      -- Add indexes for performance
      CREATE INDEX IF NOT EXISTS idx_user_module_progress_user_id ON public.user_module_progress(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_module_progress_module_id ON public.user_module_progress(module_id);
      CREATE INDEX IF NOT EXISTS idx_user_module_progress_completion ON public.user_module_progress(is_completed);
    `;

    console.log('SQL to create the table:');
    console.log(createTableSQL);
    console.log('Please run this SQL in the Supabase SQL Editor to create the table');

    return true;
  } catch (error) {
    console.error('Error creating user_module_progress table:', error);
    return false;
  }
}

async function fixLessonProgress() {
  try {
    console.log('Running fix-lesson-progress.js for all users...');

    // Run the fix-lesson-progress.js script
    try {
      execSync('node scripts/fix-lesson-progress.js', { stdio: 'inherit' });
      console.log('Successfully ran fix-lesson-progress.js');
      return true;
    } catch (error) {
      console.error('Error running fix-lesson-progress.js:', error);
      return false;
    }
  } catch (error) {
    console.error('Error fixing lesson progress:', error);
    return false;
  }
}

async function resetAllModuleCompletions() {
  try {
    console.log('Running reset-all-module-completions.js...');

    // Run the reset-all-module-completions.js script
    try {
      execSync('node scripts/reset-all-module-completions.js', { stdio: 'inherit' });
      console.log('Successfully ran reset-all-module-completions.js');
      return true;
    } catch (error) {
      console.error('Error running reset-all-module-completions.js:', error);
      return false;
    }
  } catch (error) {
    console.error('Error resetting module completions:', error);
    return false;
  }
}

async function fixIncorrectCompletions() {
  try {
    console.log('Running fix-incorrect-completions.js...');

    // Run the fix-incorrect-completions.js script
    try {
      execSync('node scripts/fix-incorrect-completions.js', { stdio: 'inherit' });
      console.log('Successfully ran fix-incorrect-completions.js');
      return true;
    } catch (error) {
      console.error('Error running fix-incorrect-completions.js:', error);
      return false;
    }
  } catch (error) {
    console.error('Error fixing incorrect completions:', error);
    return false;
  }
}

async function fixStorageRLS() {
  try {
    console.log('Running fix-storage-rls.js...');

    // Run the fix-storage-rls.js script
    try {
      execSync('node scripts/fix-storage-rls.js', { stdio: 'inherit' });
      console.log('Successfully ran fix-storage-rls.js');
      return true;
    } catch (error) {
      console.error('Error running fix-storage-rls.js:', error);
      return false;
    }
  } catch (error) {
    console.error('Error fixing storage RLS:', error);
    return false;
  }
}

async function runAllFixes() {
  console.log('Running all fixes...');

  // Step 1: Create the user_module_progress table
  const tableCreated = await createUserModuleProgressTable();
  if (!tableCreated) {
    console.log('Failed to create user_module_progress table. Please create it manually using the SQL provided above.');
    console.log('Continuing with other fixes...');
  }

  // Step 2: Reset all module completions
  const resetResult = await resetAllModuleCompletions();
  if (!resetResult) {
    console.log('Failed to reset module completions. Continuing with other fixes...');
  }

  // Step 3: Fix incorrect completions
  const fixCompletionsResult = await fixIncorrectCompletions();
  if (!fixCompletionsResult) {
    console.log('Failed to fix incorrect completions. Continuing with other fixes...');
  }

  // Step 4: Fix lesson progress
  const fixProgressResult = await fixLessonProgress();
  if (!fixProgressResult) {
    console.log('Failed to fix lesson progress. Continuing with other fixes...');
  }

  // Step 5: Fix storage RLS policies
  const fixStorageResult = await fixStorageRLS();
  if (!fixStorageResult) {
    console.log('Failed to fix storage RLS policies.');
  }

  console.log('All fixes completed!');
  console.log('');
  console.log('IMPORTANT NEXT STEPS:');
  console.log('1. You need to create the user_module_progress table in Supabase.');
  console.log('   Please copy the SQL from above and run it in the Supabase SQL Editor.');
  console.log('');
  console.log('2. You need to set up proper RLS policies for storage buckets.');
  console.log('   Run the SQL in supabase/migrations/20250501001_fix_storage_rls.sql');
  console.log('   in the Supabase SQL Editor.');
  console.log('');
  console.log('After completing these steps, restart your application.');
}

// Run all fixes
runAllFixes();
