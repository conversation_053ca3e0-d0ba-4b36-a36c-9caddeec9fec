import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixAnalyticsFunction() {
  try {
    console.log('Fixing demographic analytics function...');

    // Drop the existing function
    const dropFunction = `
      DROP FUNCTION IF EXISTS public.get_demographic_analytics();
    `;

    // Create the corrected function
    const createFunction = `
      CREATE OR REPLACE FUNCTION public.get_demographic_analytics()
      RETURNS TABLE (
        total_responses BIGINT,
        response_breakdown JSONB,
        completion_rate NUMERIC
      ) AS $$
      DECLARE
        total_users BIGINT;
        total_resp BIGINT;
      BEGIN
        -- Get total users count
        SELECT COUNT(*) INTO total_users FROM auth.users;
        
        -- Get total responses count
        SELECT COUNT(*) INTO total_resp FROM public.user_demographic_responses;
        
        RETURN QUERY
        SELECT 
          total_resp as total_responses,
          jsonb_build_object(
            'by_country', COALESCE((
              SELECT jsonb_object_agg(country, count)
              FROM (
                SELECT 
                  COALESCE(responses->>'country', 'Unknown') as country,
                  COUNT(*) as count
                FROM public.user_demographic_responses
                GROUP BY responses->>'country'
              ) country_stats
            ), '{}'::jsonb),
            'by_gender', COALESCE((
              SELECT jsonb_object_agg(gender, count)
              FROM (
                SELECT 
                  COALESCE(responses->>'gender', 'Unknown') as gender,
                  COUNT(*) as count
                FROM public.user_demographic_responses
                GROUP BY responses->>'gender'
              ) gender_stats
            ), '{}'::jsonb),
            'by_role', COALESCE((
              SELECT jsonb_object_agg(role_type, count)
              FROM (
                SELECT 
                  COALESCE(responses->>'role_type', 'Unknown') as role_type,
                  COUNT(*) as count
                FROM public.user_demographic_responses
                GROUP BY responses->>'role_type'
              ) role_stats
            ), '{}'::jsonb)
          ) as response_breakdown,
          CASE 
            WHEN total_users > 0 THEN ROUND((total_resp::NUMERIC / total_users) * 100, 2)
            ELSE 0
          END as completion_rate;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    // Grant permissions
    const grantPermissions = `
      GRANT EXECUTE ON FUNCTION public.get_demographic_analytics() TO authenticated;
    `;

    console.log('Dropping existing function...');
    const { error: dropError } = await supabase.rpc('exec_sql', { sql: dropFunction });
    if (dropError) {
      console.log('Note: Could not drop function (may not exist):', dropError.message);
    }

    console.log('Creating new function...');
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createFunction });
    if (createError) {
      console.error('Error creating function:', createError.message);
      return;
    }

    console.log('Granting permissions...');
    const { error: grantError } = await supabase.rpc('exec_sql', { sql: grantPermissions });
    if (grantError) {
      console.log('Note: Could not grant permissions:', grantError.message);
    }

    // Test the function
    console.log('Testing the function...');
    const { data, error } = await supabase.rpc('get_demographic_analytics');
    
    if (error) {
      console.error('Function test failed:', error.message);
    } else {
      console.log('✅ Function working correctly!');
      if (data && data.length > 0) {
        console.log('Result:', data[0]);
      }
    }

  } catch (error) {
    console.error('Error fixing analytics function:', error);
  }
}

fixAnalyticsFunction();
