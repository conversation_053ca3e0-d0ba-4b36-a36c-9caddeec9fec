/**
 * Certificate Fix Script
 *
 * This script fixes certificate generation issues by:
 * 1. Finding all course enrollments with status 'completed' but no completed_at date
 * 2. Updating them with a completed_at date based on updated_at or created_at
 * 3. Ensuring all completed courses have proper entries in user_course_progress
 * 4. Testing certificate generation functionality
 * 5. Verifying certificate display in achievements page
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY is required');
  console.error('Please set VITE_SUPABASE_SERVICE_ROLE_KEY in your .env file');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Fix missing completed_at dates in enrollments
 */
async function fixMissingCompletedDates() {
  console.log(`${colors.blue}🔍 Finding completed enrollments with missing completed_at date...${colors.reset}`);

  const { data: enrollments, error: fetchError } = await supabase
    .from('user_course_enrollment')
    .select('*')
    .eq('status', 'completed')
    .is('completed_at', null);

  if (fetchError) {
    console.error(`${colors.red}❌ Error fetching enrollments: ${fetchError.message}${colors.reset}`);
    return false;
  }

  console.log(`${colors.yellow}📊 Found ${enrollments.length} enrollments to fix${colors.reset}`);

  if (enrollments.length === 0) {
    console.log(`${colors.green}✅ No enrollments need fixing${colors.reset}`);
    return true;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const enrollment of enrollments) {
    try {
      console.log(`${colors.cyan}🔧 Fixing enrollment ${enrollment.id} for user ${enrollment.user_id}${colors.reset}`);

      // Use updated_at or enrolled_at as the completed_at date
      const completedAt = enrollment.updated_at || enrollment.enrolled_at || new Date().toISOString();

      const { error: updateError } = await supabase
        .from('user_course_enrollment')
        .update({ completed_at: completedAt })
        .eq('id', enrollment.id);

      if (updateError) {
        console.error(`${colors.red}❌ Error updating enrollment ${enrollment.id}: ${updateError.message}${colors.reset}`);
        errorCount++;
        continue;
      }

      console.log(`${colors.green}✅ Successfully updated enrollment ${enrollment.id}${colors.reset}`);
      successCount++;

    } catch (err) {
      console.error(`${colors.red}❌ Unexpected error for enrollment ${enrollment.id}: ${err.message}${colors.reset}`);
      errorCount++;
    }
  }

  console.log(`${colors.blue}📊 Completed date fix results:${colors.reset}`);
  console.log(`${colors.green}✅ Success: ${successCount}${colors.reset}`);
  console.log(`${colors.red}❌ Errors: ${errorCount}${colors.reset}`);

  return errorCount === 0;
}

/**
 * Ensure all completed courses have proper progress entries
 */
async function ensureProgressEntries() {
  console.log(`${colors.blue}🔍 Checking for missing progress entries...${colors.reset}`);

  const { data: enrollments, error: fetchError } = await supabase
    .from('user_course_enrollment')
    .select('*')
    .eq('status', 'completed');

  if (fetchError) {
    console.error(`${colors.red}❌ Error fetching completed enrollments: ${fetchError.message}${colors.reset}`);
    return false;
  }

  console.log(`${colors.yellow}📊 Checking ${enrollments.length} completed enrollments${colors.reset}`);

  let successCount = 0;
  let errorCount = 0;
  let existingCount = 0;

  for (const enrollment of enrollments) {
    try {
      // Check if progress entry exists
      const { data: progressData, error: progressError } = await supabase
        .from('user_course_progress')
        .select('*')
        .eq('user_id', enrollment.user_id)
        .eq('course_id', enrollment.course_id)
        .maybeSingle();

      if (progressError) {
        console.error(`${colors.red}❌ Error checking progress for enrollment ${enrollment.id}: ${progressError.message}${colors.reset}`);
        errorCount++;
        continue;
      }

      if (!progressData) {
        console.log(`${colors.cyan}🔧 Creating missing progress entry for enrollment ${enrollment.id}${colors.reset}`);

        const { error: insertError } = await supabase
          .from('user_course_progress')
          .insert({
            user_id: enrollment.user_id,
            course_id: enrollment.course_id,
            hours_spent: 0,
            last_accessed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error(`${colors.red}❌ Error creating progress for enrollment ${enrollment.id}: ${insertError.message}${colors.reset}`);
          errorCount++;
          continue;
        }

        console.log(`${colors.green}✅ Successfully created progress for enrollment ${enrollment.id}${colors.reset}`);
        successCount++;
      } else {
        existingCount++;
      }

    } catch (err) {
      console.error(`${colors.red}❌ Unexpected error for enrollment ${enrollment.id}: ${err.message}${colors.reset}`);
      errorCount++;
    }
  }

  console.log(`${colors.blue}📊 Progress entries results:${colors.reset}`);
  console.log(`${colors.green}✅ Created: ${successCount}${colors.reset}`);
  console.log(`${colors.yellow}📋 Already existed: ${existingCount}${colors.reset}`);
  console.log(`${colors.red}❌ Errors: ${errorCount}${colors.reset}`);

  return errorCount === 0;
}

/**
 * Test certificate functionality
 */
async function testCertificateFunctionality() {
  console.log(`${colors.blue}🧪 Testing certificate functionality...${colors.reset}`);

  try {
    // Test 1: Check if we can fetch certificates
    const { data: certificates, error: certError } = await supabase
      .from('user_course_enrollment')
      .select(`
        id,
        course_id,
        user_id,
        completed_at,
        enrolled_at,
        updated_at,
        status,
        course:courses(id, title, description, image_url)
      `)
      .eq('status', 'completed')
      .limit(5);

    if (certError) {
      console.error(`${colors.red}❌ Certificate fetch test failed: ${certError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Certificate fetch test passed - found ${certificates.length} certificates${colors.reset}`);

    // Test 2: Check if complete_course function exists and works
    const { data: functionTest, error: functionError } = await supabase.rpc('health_check');

    if (functionError) {
      console.error(`${colors.red}❌ Database function test failed: ${functionError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Database function test passed${colors.reset}`);

    // Test 3: Check table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error(`${colors.red}❌ Table structure test failed: ${tableError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Table structure test passed${colors.reset}`);

    return true;

  } catch (err) {
    console.error(`${colors.red}❌ Certificate functionality test error: ${err.message}${colors.reset}`);
    return false;
  }
}

/**
 * Main certificate fix function
 */
async function fixCertificates() {
  console.log(`${colors.magenta}🎓 Certificate Fix Script${colors.reset}`);
  console.log(`${colors.cyan}📍 Supabase URL: ${SUPABASE_URL}${colors.reset}`);
  console.log('='.repeat(60));

  let allSuccess = true;

  // Step 1: Fix missing completed_at dates
  const datesFixed = await fixMissingCompletedDates();
  if (!datesFixed) {
    allSuccess = false;
  }

  // Step 2: Ensure progress entries exist
  const progressFixed = await ensureProgressEntries();
  if (!progressFixed) {
    allSuccess = false;
  }

  // Step 3: Test certificate functionality
  const functionalityOk = await testCertificateFunctionality();
  if (!functionalityOk) {
    allSuccess = false;
  }

  // Final results
  console.log('\n' + '='.repeat(60));
  if (allSuccess) {
    console.log(`${colors.green}🎉 Certificate Fix Completed Successfully!${colors.reset}`);
    console.log(`${colors.green}✅ All certificate issues have been resolved${colors.reset}`);
    console.log(`${colors.green}✅ Certificates should now appear in the Achievements page${colors.reset}`);
    console.log(`${colors.green}✅ Certificate generation and download should work properly${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  Certificate Fix Completed with Issues${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Some certificate issues may still exist${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Please review the errors above and fix manually if needed${colors.reset}`);
  }
  console.log('='.repeat(60));
}

// Run the script
fixCertificates();
