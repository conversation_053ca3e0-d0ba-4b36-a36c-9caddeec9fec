/**
 * Fix Course Completion Issues
 * This script fixes the identified issues with course completion
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function fixCourseTitle() {
  console.log('🔧 Fixing course title...');
  
  try {
    // Find courses with empty titles
    const { data: coursesWithEmptyTitles, error: findError } = await supabase
      .from('courses')
      .select('id, title, description')
      .or('title.is.null,title.eq.');
    
    if (findError) {
      console.error(`❌ Error finding courses: ${findError.message}`);
      return false;
    }
    
    if (!coursesWithEmptyTitles || coursesWithEmptyTitles.length === 0) {
      console.log('✅ No courses with empty titles found');
      return true;
    }
    
    console.log(`📚 Found ${coursesWithEmptyTitles.length} courses with empty titles`);
    
    // Fix each course
    for (const course of coursesWithEmptyTitles) {
      const newTitle = 'IV Cannulation Course'; // Default title for this LMS
      
      const { error: updateError } = await supabase
        .from('courses')
        .update({ 
          title: newTitle,
          updated_at: new Date().toISOString()
        })
        .eq('id', course.id);
      
      if (updateError) {
        console.error(`❌ Error updating course ${course.id}: ${updateError.message}`);
      } else {
        console.log(`✅ Updated course ${course.id} title to "${newTitle}"`);
      }
    }
    
    return true;
    
  } catch (err) {
    console.error(`❌ Course title fix error: ${err.message}`);
    return false;
  }
}

async function fixDuplicateEnrollments() {
  console.log('\n🔧 Fixing duplicate enrollments...');
  
  try {
    // Find duplicate enrollments
    const { data: duplicates, error: duplicateError } = await supabase
      .from('user_course_enrollment')
      .select('user_id, course_id, count(*)')
      .group('user_id, course_id')
      .having('count(*) > 1');
    
    if (duplicateError) {
      console.error(`❌ Error finding duplicates: ${duplicateError.message}`);
      return false;
    }
    
    if (!duplicates || duplicates.length === 0) {
      console.log('✅ No duplicate enrollments found');
      return true;
    }
    
    console.log(`📊 Found ${duplicates.length} duplicate enrollment groups`);
    
    // Fix each duplicate group
    for (const duplicate of duplicates) {
      console.log(`🔧 Fixing duplicates for user ${duplicate.user_id}, course ${duplicate.course_id}`);
      
      // Get all enrollments for this user-course combination
      const { data: enrollments, error: getError } = await supabase
        .from('user_course_enrollment')
        .select('*')
        .eq('user_id', duplicate.user_id)
        .eq('course_id', duplicate.course_id)
        .order('created_at', { ascending: false });
      
      if (getError) {
        console.error(`❌ Error getting enrollments: ${getError.message}`);
        continue;
      }
      
      if (enrollments && enrollments.length > 1) {
        // Keep the most recent one, delete the rest
        const keepEnrollment = enrollments[0];
        const deleteEnrollments = enrollments.slice(1);
        
        console.log(`   📝 Keeping enrollment ${keepEnrollment.id}, deleting ${deleteEnrollments.length} duplicates`);
        
        for (const deleteEnrollment of deleteEnrollments) {
          const { error: deleteError } = await supabase
            .from('user_course_enrollment')
            .delete()
            .eq('id', deleteEnrollment.id);
          
          if (deleteError) {
            console.error(`❌ Error deleting enrollment ${deleteEnrollment.id}: ${deleteError.message}`);
          } else {
            console.log(`   ✅ Deleted duplicate enrollment ${deleteEnrollment.id}`);
          }
        }
      }
    }
    
    return true;
    
  } catch (err) {
    console.error(`❌ Duplicate enrollment fix error: ${err.message}`);
    return false;
  }
}

async function fixEnrollmentConstraints() {
  console.log('\n🔧 Adding unique constraint to prevent future duplicates...');
  
  try {
    // Add unique constraint if it doesn't exist
    const { error: constraintError } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM pg_constraint 
            WHERE conname = 'user_course_enrollment_user_id_course_id_key'
          ) THEN
            ALTER TABLE public.user_course_enrollment 
            ADD CONSTRAINT user_course_enrollment_user_id_course_id_key 
            UNIQUE (user_id, course_id);
          END IF;
        END $$;
      `
    });
    
    if (constraintError) {
      console.error(`❌ Error adding constraint: ${constraintError.message}`);
      return false;
    }
    
    console.log('✅ Unique constraint ensured on user_course_enrollment');
    return true;
    
  } catch (err) {
    console.error(`❌ Constraint fix error: ${err.message}`);
    return false;
  }
}

async function testCourseCompletionFlow() {
  console.log('\n🔧 Testing complete course completion flow...');
  
  try {
    // Create a test user
    const testUserId = 'test-completion-' + Date.now();
    
    // Get the course
    const { data: courses, error: courseError } = await supabase
      .from('courses')
      .select('id, title, modules:modules(id)')
      .limit(1);
    
    if (courseError || !courses || courses.length === 0) {
      console.error('❌ No courses found for testing');
      return false;
    }
    
    const course = courses[0];
    console.log(`📚 Testing with course: "${course.title}"`);
    
    // Step 1: Enroll user
    const { error: enrollError } = await supabase
      .from('user_course_enrollment')
      .insert({
        user_id: testUserId,
        course_id: course.id,
        status: 'in_progress',
        enrolled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    
    if (enrollError) {
      console.error(`❌ Enrollment failed: ${enrollError.message}`);
      return false;
    }
    
    console.log('✅ User enrolled successfully');
    
    // Step 2: Complete all modules
    if (course.modules && course.modules.length > 0) {
      const moduleCompletions = course.modules.map(module => ({
        user_id: testUserId,
        module_id: module.id,
        is_completed: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      const { error: moduleError } = await supabase
        .from('user_module_progress')
        .insert(moduleCompletions);
      
      if (moduleError) {
        console.error(`❌ Module completion failed: ${moduleError.message}`);
        return false;
      }
      
      console.log(`✅ Completed ${course.modules.length} modules`);
    }
    
    // Step 3: Complete course
    const { data: completionResult, error: completionError } = await supabase.rpc('complete_course', {
      p_user_id: testUserId,
      p_course_id: course.id
    });
    
    if (completionError) {
      console.error(`❌ Course completion failed: ${completionError.message}`);
      return false;
    }
    
    console.log('✅ Course completion successful:', completionResult);
    
    // Step 4: Verify completion
    const { data: verification, error: verifyError } = await supabase
      .from('user_course_enrollment')
      .select('status, completed_at')
      .eq('user_id', testUserId)
      .eq('course_id', course.id)
      .single();
    
    if (verifyError) {
      console.error(`❌ Verification failed: ${verifyError.message}`);
      return false;
    }
    
    if (verification.status === 'completed' && verification.completed_at) {
      console.log('✅ Course completion verified');
      console.log(`   Status: ${verification.status}`);
      console.log(`   Completed at: ${verification.completed_at}`);
    } else {
      console.error('❌ Course completion not properly recorded');
      return false;
    }
    
    // Cleanup
    await supabase.from('user_course_enrollment').delete().eq('user_id', testUserId);
    await supabase.from('user_module_progress').delete().eq('user_id', testUserId);
    await supabase.from('user_course_progress').delete().eq('user_id', testUserId);
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (err) {
    console.error(`❌ Flow test error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Fixing Course Completion Issues...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;
  const fixes = [];

  // Fix 1: Course titles
  if (await fixCourseTitle()) {
    fixes.push('✅ Course titles fixed');
  } else {
    allSuccess = false;
    fixes.push('❌ Course title fix failed');
  }

  // Fix 2: Duplicate enrollments
  if (await fixDuplicateEnrollments()) {
    fixes.push('✅ Duplicate enrollments cleaned up');
  } else {
    allSuccess = false;
    fixes.push('❌ Duplicate enrollment cleanup failed');
  }

  // Fix 3: Database constraints
  if (await fixEnrollmentConstraints()) {
    fixes.push('✅ Database constraints added');
  } else {
    allSuccess = false;
    fixes.push('❌ Database constraint addition failed');
  }

  // Test 4: Complete flow
  if (await testCourseCompletionFlow()) {
    fixes.push('✅ Complete flow tested successfully');
  } else {
    allSuccess = false;
    fixes.push('❌ Complete flow test failed');
  }

  console.log('\n' + '='.repeat(60));
  if (allSuccess) {
    console.log('🎉 All course completion issues have been fixed!');
    console.log('✅ Task 3: Course Completion System - COMPLETED');
    console.log('');
    console.log('📋 Fixes Applied:');
    fixes.forEach(fix => console.log(`  ${fix}`));
    console.log('');
    console.log('🎯 Course completion should now work properly for users!');
  } else {
    console.log('⚠️  Some fixes failed:');
    fixes.forEach(fix => console.log(`  ${fix}`));
  }
  console.log('='.repeat(60));
  
  return allSuccess;
}

main().catch(console.error);
