// This script fixes course completion issues by directly executing SQL
// Run this script with: node scripts/fix-course-completion.js <user_id> <course_id>
// Or to fix all courses for a user: node scripts/fix-course-completion.js <user_id> --all

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function fixCourseCompletion(userId, courseId) {
  if (!userId) {
    console.error('Usage: node scripts/fix-course-completion.js <user_id> <course_id>');
    console.error('   or: node scripts/fix-course-completion.js <user_id> --all');
    process.exit(1);
  }

  console.log(`Fixing course completion for user ${userId}${courseId ? ` and course ${courseId}` : ' (all courses)'}...`);

  try {
    // Try multiple approaches to ensure the course is marked as completed
    let success = false;
    const now = new Date().toISOString();
    const client = serviceRoleClient || supabase;

    // Approach 1: Try using the RPC function
    try {
      console.log('Approach 1: Using RPC function...');

      if (courseId) {
        // Complete a specific course
        const { data, error } = await client.rpc('complete_course', {
          p_user_id: userId,
          p_course_id: courseId
        });

        if (error) {
          console.error('RPC approach failed:', error);
        } else {
          console.log('RPC approach succeeded:', data);
          success = true;
        }
      } else {
        // Complete all courses
        const { data, error } = await client.rpc('complete_all_courses', {
          p_user_id: userId
        });

        if (error) {
          console.error('RPC approach failed:', error);
        } else {
          console.log('RPC approach succeeded:', data);
          success = true;
        }
      }
    } catch (rpcError) {
      console.error('Error in RPC approach:', rpcError);
    }

    // If RPC failed, try direct database operations
    if (!success) {
      console.log('Approach 2: Direct database operations...');

      if (courseId) {
        // Fix a specific course
        // First, check if the enrollment exists
        const { data: enrollment, error: checkError } = await client
          .from('user_course_enrollment')
          .select('*')
          .eq('user_id', userId)
          .eq('course_id', courseId)
          .maybeSingle();

        if (checkError) {
          console.error('Error checking enrollment:', checkError);
        } else {
          if (enrollment) {
            console.log('Enrollment found, updating to completed status...');

            // Update existing enrollment
            const { error: updateError } = await client
              .from('user_course_enrollment')
              .update({
                status: 'completed',
                completed_at: now,
                updated_at: now
              })
              .eq('id', enrollment.id);

            if (updateError) {
              console.error('Error updating enrollment:', updateError);
            } else {
              success = true;
            }
          } else {
            console.log('No enrollment found, creating new one...');

            // Create new enrollment
            const { error: insertError } = await client
              .from('user_course_enrollment')
              .insert({
                user_id: userId,
                course_id: courseId,
                status: 'completed',
                enrolled_at: now,
                completed_at: now,
                updated_at: now
              });

            if (insertError) {
              console.error('Error creating enrollment:', insertError);
            } else {
              success = true;
            }
          }

          // Update course progress
          console.log('Updating course progress...');
          const { error: progressError } = await client
            .from('user_course_progress')
            .upsert({
              user_id: userId,
              course_id: courseId,
              completed_modules: 100,
              updated_at: now
            }, { onConflict: 'user_id,course_id' });

          if (progressError) {
            console.error('Error updating progress:', progressError);
            // Continue anyway as this is not critical
          }
        }
      } else {
        // Fix all courses
        // Get all courses
        const { data: courses, error: coursesError } = await client
          .from('courses')
          .select('id');

        if (coursesError) {
          console.error('Error fetching courses:', coursesError);
        } else if (courses && courses.length > 0) {
          console.log(`Found ${courses.length} courses to complete`);
          let completedCount = 0;

          // Process each course
          for (const course of courses) {
            console.log(`Processing course ${course.id}...`);

            // Check if enrollment exists
            const { data: enrollment, error: checkError } = await client
              .from('user_course_enrollment')
              .select('*')
              .eq('user_id', userId)
              .eq('course_id', course.id)
              .maybeSingle();

            if (checkError) {
              console.error(`Error checking enrollment for course ${course.id}:`, checkError);
              continue;
            }

            if (enrollment) {
              // Update existing enrollment
              const { error: updateError } = await client
                .from('user_course_enrollment')
                .update({
                  status: 'completed',
                  completed_at: now,
                  updated_at: now
                })
                .eq('id', enrollment.id);

              if (updateError) {
                console.error(`Error updating enrollment for course ${course.id}:`, updateError);
              } else {
                completedCount++;
              }
            } else {
              // Create new enrollment
              const { error: insertError } = await client
                .from('user_course_enrollment')
                .insert({
                  user_id: userId,
                  course_id: course.id,
                  status: 'completed',
                  enrolled_at: now,
                  completed_at: now,
                  updated_at: now
                });

              if (insertError) {
                console.error(`Error creating enrollment for course ${course.id}:`, insertError);
              } else {
                completedCount++;
              }
            }

            // Update course progress
            await client
              .from('user_course_progress')
              .upsert({
                user_id: userId,
                course_id: course.id,
                completed_modules: 100,
                updated_at: now
              }, { onConflict: 'user_id,course_id' });
          }

          console.log(`Successfully completed ${completedCount} out of ${courses.length} courses`);
          success = completedCount > 0;
        } else {
          console.error('No courses found to complete');
        }
      }
    }

    if (success) {
      console.log('Course completion fixed successfully!');
    } else {
      console.error('Failed to fix course completion. All approaches failed.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Get command line arguments from process.argv
const userId = process.argv[2];
const courseIdOrFlag = process.argv[3];

// Check if we're completing all courses or a specific one
if (courseIdOrFlag === '--all') {
  // Complete all courses for this user
  fixCourseCompletion(userId, null);
} else {
  // Complete a specific course
  fixCourseCompletion(userId, courseIdOrFlag);
}
