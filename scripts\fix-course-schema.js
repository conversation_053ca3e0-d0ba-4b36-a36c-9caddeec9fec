/**
 * <PERSON><PERSON><PERSON> to fix course schema issues
 * 
 * This script applies the migration to ensure the courses table has all required columns,
 * particularly the image_url column.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } = require('./config');

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function fixCourseSchema() {
  console.log('Fixing course schema...');
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250701001_fix_course_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into statements
    const statements = migrationSQL
      .split(';')
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (const [index, statement] of statements.entries()) {
      try {
        console.log(`Executing statement ${index + 1}/${statements.length}...`);
        
        // Execute the SQL statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`Error executing statement ${index + 1}:`, error);
          // Continue with next statement
        } else {
          console.log(`Statement ${index + 1} executed successfully`);
        }
      } catch (stmtError) {
        console.error(`Error executing statement ${index + 1}:`, stmtError);
        // Continue with next statement
      }
    }
    
    console.log('Course schema fix completed');
    
    // Verify the image_url column exists
    try {
      const { data, error } = await supabase
        .from('courses')
        .select('image_url')
        .limit(1);
      
      if (error) {
        console.error('Error verifying image_url column:', error);
      } else {
        console.log('image_url column exists and is accessible');
      }
    } catch (verifyError) {
      console.error('Error verifying image_url column:', verifyError);
    }
    
    return true;
  } catch (error) {
    console.error('Error fixing course schema:', error);
    return false;
  }
}

// Run the function
fixCourseSchema()
  .then(success => {
    if (success) {
      console.log('Course schema fix completed successfully');
      process.exit(0);
    } else {
      console.error('Course schema fix failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
