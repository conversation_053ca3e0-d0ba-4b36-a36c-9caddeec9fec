/**
 * Fix Lesson Navigation Script
 * 
 * This script diagnoses and fixes lesson navigation issues by:
 * 1. Checking for missing or null lesson_number values
 * 2. Identifying gaps in lesson_number sequences
 * 3. Fixing duplicate lesson_number values within modules
 * 4. Testing the navigation logic
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

/**
 * Check for lessons with missing lesson_number values
 */
async function checkMissingLessonNumbers() {
  console.log(`\n${colors.blue}🔍 Checking for missing lesson_number values...${colors.reset}`);
  
  const { data: lessonsWithoutNumbers, error } = await supabase
    .from('lessons')
    .select('id, slug, title, module_id, lesson_number')
    .is('lesson_number', null);
  
  if (error) {
    console.error(`${colors.red}❌ Error checking lesson numbers:${colors.reset}`, error);
    return false;
  }
  
  if (lessonsWithoutNumbers && lessonsWithoutNumbers.length > 0) {
    console.log(`${colors.red}❌ Found ${lessonsWithoutNumbers.length} lessons without lesson_number:${colors.reset}`);
    lessonsWithoutNumbers.forEach(lesson => {
      console.log(`  - ${lesson.title} (${lesson.slug}) - Module: ${lesson.module_id}`);
    });
    return false;
  } else {
    console.log(`${colors.green}✅ All lessons have lesson_number values${colors.reset}`);
    return true;
  }
}

/**
 * Check for duplicate lesson_number values within modules
 */
async function checkDuplicateLessonNumbers() {
  console.log(`\n${colors.blue}🔍 Checking for duplicate lesson_number values...${colors.reset}`);
  
  const { data: duplicates, error } = await supabase
    .rpc('find_duplicate_lesson_numbers');
  
  if (error) {
    console.log(`${colors.yellow}⚠️ Could not check for duplicates (function may not exist):${colors.reset}`, error.message);
    
    // Manual check using a query
    const { data: lessons, error: lessonError } = await supabase
      .from('lessons')
      .select('module_id, lesson_number, slug, title')
      .order('module_id, lesson_number');
    
    if (lessonError) {
      console.error(`${colors.red}❌ Error fetching lessons:${colors.reset}`, lessonError);
      return false;
    }
    
    // Group by module and check for duplicates
    const moduleGroups = {};
    lessons.forEach(lesson => {
      if (!moduleGroups[lesson.module_id]) {
        moduleGroups[lesson.module_id] = [];
      }
      moduleGroups[lesson.module_id].push(lesson);
    });
    
    let foundDuplicates = false;
    Object.entries(moduleGroups).forEach(([moduleId, moduleLessons]) => {
      const numberCounts = {};
      moduleLessons.forEach(lesson => {
        if (!numberCounts[lesson.lesson_number]) {
          numberCounts[lesson.lesson_number] = [];
        }
        numberCounts[lesson.lesson_number].push(lesson);
      });
      
      Object.entries(numberCounts).forEach(([number, lessonsWithNumber]) => {
        if (lessonsWithNumber.length > 1) {
          foundDuplicates = true;
          console.log(`${colors.red}❌ Duplicate lesson_number ${number} in module ${moduleId}:${colors.reset}`);
          lessonsWithNumber.forEach(lesson => {
            console.log(`  - ${lesson.title} (${lesson.slug})`);
          });
        }
      });
    });
    
    if (!foundDuplicates) {
      console.log(`${colors.green}✅ No duplicate lesson_number values found${colors.reset}`);
    }
    
    return !foundDuplicates;
  }
  
  return true;
}

/**
 * Check for gaps in lesson_number sequences within modules
 */
async function checkLessonNumberGaps() {
  console.log(`\n${colors.blue}🔍 Checking for gaps in lesson_number sequences...${colors.reset}`);
  
  const { data: modules, error: moduleError } = await supabase
    .from('modules')
    .select('id, title, course_id');
  
  if (moduleError) {
    console.error(`${colors.red}❌ Error fetching modules:${colors.reset}`, moduleError);
    return false;
  }
  
  let foundGaps = false;
  
  for (const module of modules) {
    const { data: lessons, error: lessonError } = await supabase
      .from('lessons')
      .select('lesson_number, title, slug')
      .eq('module_id', module.id)
      .order('lesson_number');
    
    if (lessonError) {
      console.error(`${colors.red}❌ Error fetching lessons for module ${module.title}:${colors.reset}`, lessonError);
      continue;
    }
    
    if (lessons && lessons.length > 0) {
      const numbers = lessons.map(l => l.lesson_number).sort((a, b) => a - b);
      
      // Check if sequence starts at 1
      if (numbers[0] !== 1) {
        foundGaps = true;
        console.log(`${colors.yellow}⚠️ Module "${module.title}" lessons don't start at 1 (starts at ${numbers[0]})${colors.reset}`);
      }
      
      // Check for gaps in sequence
      for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] !== numbers[i-1] + 1) {
          foundGaps = true;
          console.log(`${colors.yellow}⚠️ Gap in lesson sequence in module "${module.title}": ${numbers[i-1]} → ${numbers[i]}${colors.reset}`);
        }
      }
    }
  }
  
  if (!foundGaps) {
    console.log(`${colors.green}✅ No gaps in lesson_number sequences found${colors.reset}`);
  }
  
  return !foundGaps;
}

/**
 * Test navigation logic with a sample lesson
 */
async function testNavigationLogic() {
  console.log(`\n${colors.blue}🔍 Testing navigation logic...${colors.reset}`);
  
  // Get a sample lesson from the middle of a module
  const { data: sampleLesson, error } = await supabase
    .from('lessons')
    .select('slug, title, lesson_number, module_id')
    .gt('lesson_number', 1)
    .limit(1)
    .single();
  
  if (error || !sampleLesson) {
    console.log(`${colors.yellow}⚠️ Could not find a suitable test lesson${colors.reset}`);
    return true;
  }
  
  console.log(`${colors.cyan}Testing with lesson: ${sampleLesson.title} (${sampleLesson.slug})${colors.reset}`);
  console.log(`${colors.cyan}Lesson number: ${sampleLesson.lesson_number}, Module: ${sampleLesson.module_id}${colors.reset}`);
  
  // Test finding next lesson in same module
  const { data: nextLesson, error: nextError } = await supabase
    .from('lessons')
    .select('slug, title, lesson_number')
    .eq('module_id', sampleLesson.module_id)
    .gt('lesson_number', sampleLesson.lesson_number)
    .order('lesson_number', { ascending: true })
    .limit(1);
  
  if (nextError) {
    console.error(`${colors.red}❌ Error finding next lesson:${colors.reset}`, nextError);
    return false;
  }
  
  if (nextLesson && nextLesson.length > 0) {
    console.log(`${colors.green}✅ Next lesson found: ${nextLesson[0].title} (${nextLesson[0].slug})${colors.reset}`);
    console.log(`${colors.green}   Lesson number: ${nextLesson[0].lesson_number}${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️ No next lesson found in same module (this might be the last lesson)${colors.reset}`);
  }
  
  // Test finding previous lesson
  const { data: prevLesson, error: prevError } = await supabase
    .from('lessons')
    .select('slug, title, lesson_number')
    .eq('module_id', sampleLesson.module_id)
    .lt('lesson_number', sampleLesson.lesson_number)
    .order('lesson_number', { ascending: false })
    .limit(1);
  
  if (prevError) {
    console.error(`${colors.red}❌ Error finding previous lesson:${colors.reset}`, prevError);
    return false;
  }
  
  if (prevLesson && prevLesson.length > 0) {
    console.log(`${colors.green}✅ Previous lesson found: ${prevLesson[0].title} (${prevLesson[0].slug})${colors.reset}`);
    console.log(`${colors.green}   Lesson number: ${prevLesson[0].lesson_number}${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️ No previous lesson found (this might be the first lesson)${colors.reset}`);
  }
  
  return true;
}

/**
 * Fix lesson numbering issues
 */
async function fixLessonNumbering() {
  console.log(`\n${colors.blue}🔧 Attempting to fix lesson numbering issues...${colors.reset}`);
  
  const { data: modules, error: moduleError } = await supabase
    .from('modules')
    .select('id, title');
  
  if (moduleError) {
    console.error(`${colors.red}❌ Error fetching modules:${colors.reset}`, moduleError);
    return false;
  }
  
  let fixedModules = 0;
  
  for (const module of modules) {
    console.log(`${colors.cyan}Fixing lesson numbering for module: ${module.title}${colors.reset}`);
    
    // Get all lessons in this module ordered by created_at (original order)
    const { data: lessons, error: lessonError } = await supabase
      .from('lessons')
      .select('id, title, slug, created_at')
      .eq('module_id', module.id)
      .order('created_at', { ascending: true });
    
    if (lessonError) {
      console.error(`${colors.red}❌ Error fetching lessons for module ${module.title}:${colors.reset}`, lessonError);
      continue;
    }
    
    if (!lessons || lessons.length === 0) {
      console.log(`${colors.yellow}⚠️ No lessons found in module ${module.title}${colors.reset}`);
      continue;
    }
    
    // Update lesson numbers to be sequential starting from 1
    for (let i = 0; i < lessons.length; i++) {
      const lesson = lessons[i];
      const newLessonNumber = i + 1;
      
      const { error: updateError } = await supabase
        .from('lessons')
        .update({ lesson_number: newLessonNumber })
        .eq('id', lesson.id);
      
      if (updateError) {
        console.error(`${colors.red}❌ Error updating lesson ${lesson.title}:${colors.reset}`, updateError);
      } else {
        console.log(`${colors.green}✅ Updated ${lesson.title} to lesson_number ${newLessonNumber}${colors.reset}`);
      }
    }
    
    fixedModules++;
  }
  
  console.log(`${colors.green}✅ Fixed lesson numbering for ${fixedModules} modules${colors.reset}`);
  return true;
}

/**
 * Main function to run all checks and fixes
 */
async function main() {
  console.log(`${colors.blue}🚀 Starting Lesson Navigation Fix Script${colors.reset}`);
  
  try {
    // Run diagnostic checks
    const hasValidNumbers = await checkMissingLessonNumbers();
    const hasNoDuplicates = await checkDuplicateLessonNumbers();
    const hasNoGaps = await checkLessonNumberGaps();
    
    // If there are issues, attempt to fix them
    if (!hasValidNumbers || !hasNoDuplicates || !hasNoGaps) {
      console.log(`\n${colors.yellow}⚠️ Issues found with lesson numbering. Attempting to fix...${colors.reset}`);
      await fixLessonNumbering();
      
      // Re-run checks after fixing
      console.log(`\n${colors.blue}🔍 Re-running checks after fixes...${colors.reset}`);
      await checkMissingLessonNumbers();
      await checkDuplicateLessonNumbers();
      await checkLessonNumberGaps();
    }
    
    // Test navigation logic
    await testNavigationLogic();
    
    console.log(`\n${colors.green}✅ Lesson navigation fix script completed successfully!${colors.reset}`);
    console.log(`\n${colors.cyan}Next steps:${colors.reset}`);
    console.log(`1. Apply the database migration: supabase/migrations/20250102005_fix_lesson_navigation_function.sql`);
    console.log(`2. Test the navigation in your application`);
    console.log(`3. Check browser console for detailed navigation logs`);
    
  } catch (error) {
    console.error(`${colors.red}❌ Script failed:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run the script
main();
