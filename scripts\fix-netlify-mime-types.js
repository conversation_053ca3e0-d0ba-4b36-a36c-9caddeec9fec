// scripts/fix-netlify-mime-types.js
const fs = require('fs');
const path = require('path');

console.log('Running MIME type fix for Netlify...');

// Paths
const distDir = path.join(__dirname, '..', 'dist');
const headersPath = path.join(distDir, '_headers');

// Create a comprehensive _headers file with all possible patterns
const headersContent = `/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Access-Control-Allow-Origin: *

/
  Content-Type: text/html; charset=utf-8

/index.html
  Content-Type: text/html; charset=utf-8
  Cache-Control: no-cache

/*.js
  Content-Type: application/javascript; charset=utf-8

*.js
  Content-Type: application/javascript; charset=utf-8

/assets/*.js
  Content-Type: application/javascript; charset=utf-8

/assets/**.js
  Content-Type: application/javascript; charset=utf-8

/assets/index*.js
  Content-Type: application/javascript; charset=utf-8

/assets/vendor*.js
  Content-Type: application/javascript; charset=utf-8

/*.css
  Content-Type: text/css; charset=utf-8

*.css
  Content-Type: text/css; charset=utf-8

/assets/*.css
  Content-Type: text/css; charset=utf-8

/assets/**.css
  Content-Type: text/css; charset=utf-8

/assets/index*.css
  Content-Type: text/css; charset=utf-8

/*.svg
  Content-Type: image/svg+xml

/assets/*.svg
  Content-Type: image/svg+xml

/assets/
  Cache-Control: public, max-age=31536000, immutable
`;

// Write the headers file
fs.writeFileSync(headersPath, headersContent);
console.log('Created comprehensive _headers file in dist folder');

// Create a netlify.toml file in the dist folder
const netlifyTomlPath = path.join(distDir, 'netlify.toml');
const netlifyTomlContent = `[build]
  publish = "."

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Access-Control-Allow-Origin = "*"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/assets/*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/assets/**.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "/*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "/assets/*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "/assets/**.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = true
`;

// Write the netlify.toml file
fs.writeFileSync(netlifyTomlPath, netlifyTomlContent);
console.log('Created netlify.toml file in dist folder');

// Create a _redirects file in the dist folder
const redirectsPath = path.join(distDir, '_redirects');
const redirectsContent = `
/auth/callback/* /index.html 200
/* /index.html 200
`;

// Write the _redirects file
fs.writeFileSync(redirectsPath, redirectsContent);
console.log('Created _redirects file in dist folder');

console.log('MIME type fix for Netlify completed');
console.log('');
console.log('IMPORTANT: After deploying to Netlify, if you still have MIME type issues:');
console.log('1. Log in to your Netlify dashboard');
console.log('2. Go to Site settings > Build & deploy > Post processing');
console.log('3. Disable "Asset optimization"');
console.log('4. Trigger a new deployment');