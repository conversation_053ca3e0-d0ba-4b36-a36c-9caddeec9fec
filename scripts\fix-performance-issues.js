/**
 * Performance Issue Fix Script
 * 
 * This script addresses performance issues in the LMS platform:
 * 1. Optimizes database queries for progress tracking
 * 2. Creates indexes on commonly queried fields
 * 3. Creates a table to cache progress statistics
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Create client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Console output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Main function
async function main() {
  console.log(`${colors.bright}${colors.cyan}=====================================${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}= PERFORMANCE OPTIMIZATION UTILITY =${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}=====================================${colors.reset}`);
  
  try {
    // Step 1: Check connection
    await checkConnection();
    
    // Step 2: Create indexes for faster querying
    await createIndexes();
    
    // Step 3: Create progress stats cache table
    await createProgressStatsCache();
    
    // Step 4: Fix any existing database issues
    await runGeneralFixes();
    
    console.log(`\n${colors.bright}${colors.green}✓ Performance optimizations applied successfully!${colors.reset}`);
  } catch (error) {
    console.error(`\n${colors.red}Unexpected error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Check if we can connect to Supabase
async function checkConnection() {
  console.log(`${colors.bright}${colors.cyan}=== Checking Connection ===${colors.reset}`);
  
  try {
    const { data, error } = await supabase.from('courses').select('count');
    if (error) {
      console.error(`${colors.red}Error connecting to Supabase: ${error.message}${colors.reset}`);
      process.exit(1);
    }
    console.log(`${colors.green}✓ Connected to Supabase successfully${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error connecting to Supabase: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Create indexes for faster querying
async function createIndexes() {
  console.log(`${colors.bright}${colors.cyan}=== Creating Performance Indexes ===${colors.reset}`);
  
  try {
    // SQL for creating performance-improving indexes
    const createIndexesSql = `
    -- Add indexes to user_lesson_progress for faster progress lookups
    CREATE INDEX IF NOT EXISTS user_lesson_progress_user_id_idx ON public.user_lesson_progress(user_id);
    CREATE INDEX IF NOT EXISTS user_lesson_progress_lesson_id_idx ON public.user_lesson_progress(lesson_id);
    CREATE INDEX IF NOT EXISTS user_lesson_progress_completed_idx ON public.user_lesson_progress(is_completed);
    
    -- Add indexes to user_module_progress for faster progress lookups
    CREATE INDEX IF NOT EXISTS user_module_progress_user_id_idx ON public.user_module_progress(user_id);
    CREATE INDEX IF NOT EXISTS user_module_progress_module_id_idx ON public.user_module_progress(module_id);
    CREATE INDEX IF NOT EXISTS user_module_progress_completed_idx ON public.user_module_progress(is_completed);
    
    -- Add indexes to lessons table for faster module-based lookups
    CREATE INDEX IF NOT EXISTS lessons_module_id_idx ON public.lessons(module_id);
    `;
    
    // Execute SQL either through RPC or directly
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: createIndexesSql }).maybeSingle();
      
      if (error) {
        console.error(`${colors.red}Failed to create indexes via RPC: ${error.message}${colors.reset}`);
        console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
        console.log(createIndexesSql);
      } else {
        console.log(`${colors.green}✓ Created performance indexes successfully${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}Error creating indexes: ${error.message}${colors.reset}`);
      console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
      console.log(createIndexesSql);
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error creating indexes: ${error.message}${colors.reset}`);
    return false;
  }
}

// Create a table to cache progress statistics
async function createProgressStatsCache() {
  console.log(`${colors.bright}${colors.cyan}=== Creating Progress Stats Cache ===${colors.reset}`);
  
  try {
    // SQL for creating a progress stats cache table
    const createCacheSql = `
    -- Create progress stats cache table
    CREATE TABLE IF NOT EXISTS public.module_progress_stats (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
      module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
      total_lessons INTEGER NOT NULL DEFAULT 0,
      completed_lessons INTEGER NOT NULL DEFAULT 0,
      progress_percent INTEGER NOT NULL DEFAULT 0,
      last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now()),
      UNIQUE(user_id, module_id)
    );
    
    -- Add indexes
    CREATE INDEX IF NOT EXISTS module_progress_stats_user_id_idx ON public.module_progress_stats(user_id);
    CREATE INDEX IF NOT EXISTS module_progress_stats_module_id_idx ON public.module_progress_stats(module_id);
    
    -- Enable RLS
    ALTER TABLE public.module_progress_stats ENABLE ROW LEVEL SECURITY;
    
    -- Add RLS policy
    CREATE POLICY "Users can view their own progress stats" 
      ON public.module_progress_stats 
      FOR SELECT USING (auth.uid() = user_id);
    
    -- Create function to update the cache when progress changes
    CREATE OR REPLACE FUNCTION update_module_progress_cache() RETURNS TRIGGER AS $$
    DECLARE
      v_module_id UUID;
      v_total_lessons INTEGER;
      v_completed_lessons INTEGER;
      v_progress_percent INTEGER;
    BEGIN
      -- Get the module_id for this lesson
      SELECT module_id INTO v_module_id FROM public.lessons WHERE id = NEW.lesson_id;
      
      IF v_module_id IS NULL THEN
        RETURN NEW;
      END IF;
      
      -- Count total lessons for this module
      SELECT COUNT(*) INTO v_total_lessons 
      FROM public.lessons 
      WHERE module_id = v_module_id;
      
      -- Count completed lessons for this module and user
      SELECT COUNT(*) INTO v_completed_lessons 
      FROM public.user_lesson_progress 
      JOIN public.lessons ON user_lesson_progress.lesson_id = lessons.id
      WHERE lessons.module_id = v_module_id 
        AND user_lesson_progress.user_id = NEW.user_id
        AND user_lesson_progress.is_completed = true;
      
      -- Calculate progress percentage
      IF v_total_lessons > 0 THEN
        v_progress_percent := (v_completed_lessons * 100) / v_total_lessons;
      ELSE
        v_progress_percent := 0;
      END IF;
      
      -- Update the cache
      INSERT INTO public.module_progress_stats (
        user_id, module_id, total_lessons, completed_lessons, progress_percent
      ) VALUES (
        NEW.user_id, v_module_id, v_total_lessons, v_completed_lessons, v_progress_percent
      )
      ON CONFLICT (user_id, module_id) DO UPDATE SET
        total_lessons = EXCLUDED.total_lessons,
        completed_lessons = EXCLUDED.completed_lessons,
        progress_percent = EXCLUDED.progress_percent,
        last_updated = timezone('utc'::text, now());
        
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    -- Create the trigger
    DROP TRIGGER IF EXISTS update_module_progress_cache_trigger ON public.user_lesson_progress;
    
    CREATE TRIGGER update_module_progress_cache_trigger
      AFTER INSERT OR UPDATE
      ON public.user_lesson_progress
      FOR EACH ROW
      EXECUTE FUNCTION update_module_progress_cache();
    `;
    
    // Execute SQL either through RPC or directly
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: createCacheSql }).maybeSingle();
      
      if (error) {
        console.error(`${colors.red}Failed to create progress cache via RPC: ${error.message}${colors.reset}`);
        console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
        console.log(createCacheSql);
      } else {
        console.log(`${colors.green}✓ Created progress stats cache successfully${colors.reset}`);
        
        // Pre-populate the cache for existing data
        await populateProgressCache();
      }
    } catch (error) {
      console.error(`${colors.red}Error creating progress cache: ${error.message}${colors.reset}`);
      console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
      console.log(createCacheSql);
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error creating progress cache: ${error.message}${colors.reset}`);
    return false;
  }
}

// Populate the progress stats cache with existing data
async function populateProgressCache() {
  console.log(`${colors.bright}${colors.cyan}=== Pre-populating Progress Cache ===${colors.reset}`);
  
  try {
    // SQL to populate the cache from existing data
    const populateCacheSql = `
    -- Temporary function to populate the cache
    CREATE OR REPLACE FUNCTION populate_module_progress_cache() RETURNS void AS $$
    DECLARE
      lesson_rec RECORD;
      user_rec RECORD;
      module_rec RECORD;
      v_total_lessons INTEGER;
      v_completed_lessons INTEGER;
      v_progress_percent INTEGER;
    BEGIN
      -- Loop through all modules
      FOR module_rec IN SELECT id FROM public.modules LOOP
        -- Count total lessons for this module
        SELECT COUNT(*) INTO v_total_lessons FROM public.lessons WHERE module_id = module_rec.id;
        
        -- Loop through users with lesson progress
        FOR user_rec IN 
          SELECT DISTINCT user_id FROM public.user_lesson_progress 
          JOIN public.lessons ON user_lesson_progress.lesson_id = lessons.id 
          WHERE lessons.module_id = module_rec.id
        LOOP
          -- Count completed lessons for this module and user
          SELECT COUNT(*) INTO v_completed_lessons 
          FROM public.user_lesson_progress 
          JOIN public.lessons ON user_lesson_progress.lesson_id = lessons.id
          WHERE lessons.module_id = module_rec.id 
            AND user_lesson_progress.user_id = user_rec.user_id
            AND user_lesson_progress.is_completed = true;
          
          -- Calculate progress percentage
          IF v_total_lessons > 0 THEN
            v_progress_percent := (v_completed_lessons * 100) / v_total_lessons;
          ELSE
            v_progress_percent := 0;
          END IF;
          
          -- Update the cache
          INSERT INTO public.module_progress_stats (
            user_id, module_id, total_lessons, completed_lessons, progress_percent
          ) VALUES (
            user_rec.user_id, module_rec.id, v_total_lessons, v_completed_lessons, v_progress_percent
          )
          ON CONFLICT (user_id, module_id) DO UPDATE SET
            total_lessons = EXCLUDED.total_lessons,
            completed_lessons = EXCLUDED.completed_lessons,
            progress_percent = EXCLUDED.progress_percent,
            last_updated = timezone('utc'::text, now());
        END LOOP;
      END LOOP;
    END;
    $$ LANGUAGE plpgsql;
    
    -- Call the function
    SELECT populate_module_progress_cache();
    
    -- Drop the temporary function
    DROP FUNCTION populate_module_progress_cache();
    `;
    
    // Execute SQL either through RPC or directly
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: populateCacheSql }).maybeSingle();
      
      if (error) {
        console.error(`${colors.red}Failed to populate progress cache via RPC: ${error.message}${colors.reset}`);
      } else {
        console.log(`${colors.green}✓ Pre-populated progress cache successfully${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}Error populating progress cache: ${error.message}${colors.reset}`);
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error populating progress cache: ${error.message}${colors.reset}`);
    return false;
  }
}

// Run general fixes for database issues
async function runGeneralFixes() {
  console.log(`${colors.bright}${colors.cyan}=== Applying General Fixes ===${colors.reset}`);
  
  try {
    // SQL for fixing common issues
    const fixesSql = `
    -- Ensure is_completed column exists in user_lesson_progress
    DO $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_lesson_progress' 
        AND column_name = 'is_completed'
      ) THEN
        IF EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'user_lesson_progress' 
          AND column_name = 'completed'
        ) THEN
          ALTER TABLE public.user_lesson_progress RENAME COLUMN completed TO is_completed;
        ELSE
          ALTER TABLE public.user_lesson_progress ADD COLUMN is_completed BOOLEAN DEFAULT false;
        END IF;
      END IF;
    END $$;
    
    -- Create schema refresh function
    CREATE OR REPLACE FUNCTION refresh_schema_cache() RETURNS VOID AS $$
    BEGIN
      NULL; -- This is a no-op function that triggers a cache refresh
    END
    $$ LANGUAGE plpgsql;
    
    -- Create exec_sql function
    CREATE OR REPLACE FUNCTION exec_sql(sql text) RETURNS void AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    
    -- Update any NULL progress values to false
    UPDATE public.user_lesson_progress 
    SET is_completed = false 
    WHERE is_completed IS NULL;
    
    -- Update any NULL module completion values
    UPDATE public.user_module_progress 
    SET is_completed = false 
    WHERE is_completed IS NULL;
    `;
    
    // Execute SQL either through RPC or directly
    try {
      // Try to use our existing SQL execution function
      const { error: execError } = await supabase.rpc('exec_sql', { sql: fixesSql }).maybeSingle();
      
      if (execError) {
        // If that fails, we need to find another way to execute the SQL
        console.error(`${colors.red}Failed to apply fixes via RPC: ${execError.message}${colors.reset}`);
        console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
        console.log(fixesSql);
      } else {
        console.log(`${colors.green}✓ Applied general fixes successfully${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}Error applying general fixes: ${error.message}${colors.reset}`);
      console.log(`${colors.yellow}Please run the following SQL in your database:${colors.reset}`);
      console.log(fixesSql);
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error applying general fixes: ${error.message}${colors.reset}`);
    return false;
  }
}

// Run the script
main(); 