// This script fixes the RLS policies for storage buckets
// Run this script with: node scripts/fix-storage-rls.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

// List of buckets to create
const bucketsToCreate = [
  { name: 'course-images', isPublic: true },
  { name: 'avatars', isPublic: true },
  { name: 'app-uploads', isPublic: true },
  { name: 'uploads', isPublic: true },
  { name: 'default-bucket', isPublic: true }
];

async function createBuckets() {
  console.log('Starting bucket creation process...');
  const client = serviceRoleClient || supabase;

  try {
    // First, list existing buckets
    const { data: existingBuckets, error: listError } = await client.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      console.error('This might be due to missing permissions. Make sure you have the correct API keys.');
      console.log('Trying to create buckets using SQL...');
      return await createBucketsWithSQL();
    }

    console.log('Existing buckets:', existingBuckets?.map(b => b.name) || 'none');

    // Create each bucket if it doesn't exist
    let allBucketsCreated = true;
    for (const bucket of bucketsToCreate) {
      if (existingBuckets && existingBuckets.some(b => b.name === bucket.name)) {
        console.log(`Bucket '${bucket.name}' already exists.`);

        // Update bucket to ensure it has the correct settings
        const { error: updateError } = await client.storage.updateBucket(bucket.name, {
          public: bucket.isPublic
        });

        if (updateError) {
          console.error(`Error updating bucket '${bucket.name}':`, updateError);
          allBucketsCreated = false;
        } else {
          console.log(`Updated bucket '${bucket.name}' settings.`);
        }
      } else {
        console.log(`Creating bucket '${bucket.name}'...`);

        const { data, error: createError } = await client.storage.createBucket(bucket.name, {
          public: bucket.isPublic
        });

        if (createError) {
          console.error(`Error creating bucket '${bucket.name}':`, createError);
          allBucketsCreated = false;
        } else {
          console.log(`Successfully created bucket '${bucket.name}'.`);
        }
      }
    }

    if (!allBucketsCreated) {
      console.log('Some buckets could not be created using the API. Trying SQL approach...');
      return await createBucketsWithSQL();
    }

    return true;
  } catch (error) {
    console.error('Unexpected error in createBuckets:', error);
    console.log('Trying to create buckets using SQL...');
    return await createBucketsWithSQL();
  }
}

async function createBucketsWithSQL() {
  console.log('Creating buckets using SQL...');
  const client = serviceRoleClient || supabase;

  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501000_create_storage_buckets.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error } = await client.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error creating buckets with SQL:', error);
      console.log('You may need to run the SQL manually in the Supabase SQL Editor.');
      console.log('SQL file path:', sqlFilePath);
      return false;
    }

    console.log('Successfully created buckets using SQL.');
    return true;
  } catch (error) {
    console.error('Unexpected error in createBucketsWithSQL:', error);
    console.log('You may need to run the SQL manually in the Supabase SQL Editor.');
    return false;
  }
}

async function applyRLSPolicies() {
  console.log('Applying RLS policies for storage buckets...');
  const client = serviceRoleClient || supabase;

  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501001_fix_storage_rls.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error } = await client.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying RLS policies:', error);
      console.log('You may need to run the SQL manually in the Supabase SQL Editor.');
      console.log('SQL file path:', sqlFilePath);
      return false;
    }

    console.log('Successfully applied RLS policies for storage buckets.');
    return true;
  } catch (error) {
    console.error('Unexpected error in applyRLSPolicies:', error);
    console.log('You may need to run the SQL manually in the Supabase SQL Editor.');
    return false;
  }
}

async function fixStorageRLS() {
  console.log('Starting storage RLS fix...');

  // Step 1: Create buckets
  const bucketsCreated = await createBuckets();
  if (!bucketsCreated) {
    console.log('Failed to create or update buckets. Continuing with RLS policies...');
  }

  // Step 2: Apply RLS policies
  const policiesApplied = await applyRLSPolicies();
  if (!policiesApplied) {
    console.log('Failed to apply RLS policies.');
    console.log('Please run the SQL manually in the Supabase SQL Editor.');
    console.log('SQL file path: supabase/migrations/20250501001_fix_storage_rls.sql');
  }

  console.log('\n--- Summary ---');
  console.log(`Buckets created/updated: ${bucketsCreated ? 'Success' : 'Failed'}`);
  console.log(`RLS policies applied: ${policiesApplied ? 'Success' : 'Failed'}`);

  if (!bucketsCreated || !policiesApplied) {
    console.log('\nManual steps required:');
    console.log('1. Log in to your Supabase dashboard');
    console.log('2. Go to Storage and create the following buckets if they don\'t exist:');
    bucketsToCreate.forEach(bucket => {
      console.log(`   - ${bucket.name} (public: ${bucket.isPublic})`);
    });
    console.log('3. Go to the SQL Editor and run the SQL from:');
    console.log('   supabase/migrations/20250501001_fix_storage_rls.sql');
  }
}

// Run the function
fixStorageRLS();
