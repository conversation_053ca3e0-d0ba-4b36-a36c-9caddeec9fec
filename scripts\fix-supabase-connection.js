/**
 * This script checks and fixes Supabase connection issues
 * It verifies environment variables and tests the connection
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('=== Supabase Connection Checker ===');

// Check if environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or key is missing in environment variables');
  console.log('Checking for .env file...');
  
  const envPath = path.join(__dirname, '..', '.env');
  const envLocalPath = path.join(__dirname, '..', '.env.local');
  
  if (fs.existsSync(envLocalPath)) {
    console.log('.env.local file found. This file takes precedence over .env');
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    console.log('Checking .env.local content...');
    
    const urlMatch = envContent.match(/VITE_SUPABASE_URL=(.*)/);
    const keyMatch = envContent.match(/VITE_SUPABASE_ANON_KEY=(.*)/);
    
    if (!urlMatch || !keyMatch) {
      console.error('Error: Supabase URL or key is missing in .env.local file');
      process.exit(1);
    }
    
    console.log('Supabase URL and key found in .env.local file');
  } else if (fs.existsSync(envPath)) {
    console.log('.env file found');
    const envContent = fs.readFileSync(envPath, 'utf8');
    console.log('Checking .env content...');
    
    const urlMatch = envContent.match(/VITE_SUPABASE_URL=(.*)/);
    const keyMatch = envContent.match(/VITE_SUPABASE_ANON_KEY=(.*)/);
    
    if (!urlMatch || !keyMatch) {
      console.error('Error: Supabase URL or key is missing in .env file');
      process.exit(1);
    }
    
    console.log('Supabase URL and key found in .env file');
  } else {
    console.error('Error: No .env or .env.local file found');
    console.log('Creating .env file with default values...');
    
    const defaultEnvContent = `VITE_SUPABASE_URL=https://jibspqwieubavucdtccv.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI
`;
    
    fs.writeFileSync(envPath, defaultEnvContent);
    console.log('.env file created successfully');
    
    // Reload environment variables
    dotenv.config();
    supabaseUrl = process.env.VITE_SUPABASE_URL;
    supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
  }
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test connection
async function testConnection() {
  console.log('Testing connection to Supabase...');
  
  try {
    // Try a simple query
    const { data, error } = await supabase.from('user_roles').select('count').limit(1);
    
    if (error) {
      console.error('Error connecting to Supabase:', error.message);
      return false;
    }
    
    console.log('Successfully connected to Supabase!');
    return true;
  } catch (error) {
    console.error('Error connecting to Supabase:', error.message);
    return false;
  }
}

// Main function
async function main() {
  const connected = await testConnection();
  
  if (!connected) {
    console.log('Checking for connection issues...');
    
    // Try to ping the Supabase URL
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': supabaseKey,
        },
      });
      
      if (response.ok) {
        console.log('API endpoint is reachable, but query failed. This might be a permissions issue.');
      } else {
        console.error(`API endpoint returned status ${response.status}`);
      }
    } catch (error) {
      console.error('Could not reach Supabase API endpoint:', error.message);
      console.log('This might be a network connectivity issue or the Supabase project might be down.');
    }
    
    console.log('\nTroubleshooting steps:');
    console.log('1. Check your internet connection');
    console.log('2. Verify that your Supabase project is active');
    console.log('3. Make sure your API keys are correct');
    console.log('4. Check if your IP is allowed in Supabase Auth settings');
    console.log('5. Try clearing browser cache and cookies');
    
    process.exit(1);
  }
  
  console.log('\nConnection is working properly!');
  console.log('If you were experiencing "Using cached login" issues:');
  console.log('1. Try clearing your browser cache and cookies');
  console.log('2. Restart your development server');
  console.log('3. Refresh your browser');
}

main().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
