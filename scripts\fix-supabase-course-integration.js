/**
 * Unified script to fix Supabase course integration issues
 * 
 * This script:
 * 1. Ensures the courses table has the image_url column
 * 2. Fixes any missing data in the courses table
 * 3. Ensures proper RLS policies are in place
 * 4. Verifies the integration is working correctly
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } = require('./config');

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function fixSupabaseCourseIntegration() {
  console.log('Starting Supabase course integration fix...');
  
  // Step 1: Fix course schema
  console.log('\n=== Step 1: Fixing course schema ===');
  const schemaFixed = await fixCourseSchema();
  if (!schemaFixed) {
    console.error('Failed to fix course schema');
    return false;
  }
  
  // Step 2: Fix course data
  console.log('\n=== Step 2: Fixing course data ===');
  const dataFixed = await fixCourseData();
  if (!dataFixed) {
    console.error('Failed to fix course data');
    return false;
  }
  
  // Step 3: Fix RLS policies
  console.log('\n=== Step 3: Fixing RLS policies ===');
  const rlsFixed = await fixRLSPolicies();
  if (!rlsFixed) {
    console.error('Failed to fix RLS policies');
    return false;
  }
  
  // Step 4: Verify integration
  console.log('\n=== Step 4: Verifying integration ===');
  const integrationVerified = await verifyIntegration();
  if (!integrationVerified) {
    console.error('Failed to verify integration');
    return false;
  }
  
  console.log('\nSupabase course integration fix completed successfully');
  return true;
}

// Fix course schema
async function fixCourseSchema() {
  console.log('Fixing course schema...');
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250701001_fix_course_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into statements
    const statements = migrationSQL
      .split(';')
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (const [index, statement] of statements.entries()) {
      try {
        console.log(`Executing statement ${index + 1}/${statements.length}...`);
        
        // Execute the SQL statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`Error executing statement ${index + 1}:`, error);
          // Continue with next statement
        } else {
          console.log(`Statement ${index + 1} executed successfully`);
        }
      } catch (stmtError) {
        console.error(`Error executing statement ${index + 1}:`, stmtError);
        // Continue with next statement
      }
    }
    
    // Verify the image_url column exists
    try {
      const { data, error } = await supabase
        .from('courses')
        .select('image_url')
        .limit(1);
      
      if (error) {
        console.error('Error verifying image_url column:', error);
        return false;
      } else {
        console.log('image_url column exists and is accessible');
      }
    } catch (verifyError) {
      console.error('Error verifying image_url column:', verifyError);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error fixing course schema:', error);
    return false;
  }
}

// Fix course data
async function fixCourseData() {
  console.log('Fixing course data...');
  
  try {
    // Get all courses
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*');
    
    if (coursesError) {
      console.error('Error fetching courses:', coursesError);
      return false;
    }
    
    console.log(`Found ${courses.length} courses`);
    
    // Check for any issues with courses
    let fixedCount = 0;
    
    for (const course of courses) {
      let needsUpdate = false;
      const updates = {};
      
      // Check if slug is missing or invalid
      if (!course.slug || course.slug.trim() === '') {
        needsUpdate = true;
        updates.slug = course.title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
        console.log(`Course ${course.id}: Missing slug, setting to "${updates.slug}"`);
      }
      
      // Check if total_modules is null
      if (course.total_modules === null) {
        needsUpdate = true;
        updates.total_modules = 0;
        console.log(`Course ${course.id}: Missing total_modules, setting to 0`);
      }
      
      // Check if completed_modules is null
      if (course.completed_modules === null) {
        needsUpdate = true;
        updates.completed_modules = 0;
        console.log(`Course ${course.id}: Missing completed_modules, setting to 0`);
      }
      
      // Update the course if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('courses')
          .update(updates)
          .eq('id', course.id);
        
        if (updateError) {
          console.error(`Error updating course ${course.id}:`, updateError);
        } else {
          console.log(`Course ${course.id} updated successfully`);
          fixedCount++;
        }
      }
    }
    
    console.log(`Fixed ${fixedCount} courses`);
    return true;
  } catch (error) {
    console.error('Error fixing course data:', error);
    return false;
  }
}

// Fix RLS policies
async function fixRLSPolicies() {
  console.log('Fixing RLS policies...');
  
  try {
    // SQL to fix RLS policies
    const sql = `
      -- Enable RLS for courses table
      ALTER TABLE public.courses ENABLE ROW LEVEL SECURITY;
      
      -- Drop existing policies
      DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
      DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
      DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
      DROP POLICY IF EXISTS "Teachers can delete courses" ON public.courses;
      
      -- Create new policies
      CREATE POLICY "Anyone can view courses" 
      ON public.courses FOR SELECT 
      USING (true);
      
      CREATE POLICY "Teachers can insert courses" 
      ON public.courses FOR INSERT 
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.user_roles
          WHERE user_id = auth.uid() AND role = 'teacher'
        )
      );
      
      CREATE POLICY "Teachers can update courses" 
      ON public.courses FOR UPDATE 
      USING (
        EXISTS (
          SELECT 1 FROM public.user_roles
          WHERE user_id = auth.uid() AND role = 'teacher'
        )
      );
      
      CREATE POLICY "Teachers can delete courses" 
      ON public.courses FOR DELETE 
      USING (
        EXISTS (
          SELECT 1 FROM public.user_roles
          WHERE user_id = auth.uid() AND role = 'teacher'
        )
      );
    `;
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error fixing RLS policies:', error);
      return false;
    }
    
    console.log('RLS policies fixed successfully');
    return true;
  } catch (error) {
    console.error('Error fixing RLS policies:', error);
    return false;
  }
}

// Verify integration
async function verifyIntegration() {
  console.log('Verifying integration...');
  
  try {
    // Test 1: Fetch courses
    console.log('Test 1: Fetching courses...');
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*')
      .limit(5);
    
    if (coursesError) {
      console.error('Test 1 failed - Error fetching courses:', coursesError);
      return false;
    }
    
    console.log(`Test 1 passed - Successfully fetched ${courses.length} courses`);
    
    // Test 2: Check image_url column
    console.log('Test 2: Checking image_url column...');
    const { data: imageUrlData, error: imageUrlError } = await supabase
      .from('courses')
      .select('image_url')
      .limit(1);
    
    if (imageUrlError) {
      console.error('Test 2 failed - Error checking image_url column:', imageUrlError);
      return false;
    }
    
    console.log('Test 2 passed - image_url column is accessible');
    
    // Test 3: Check RLS policies
    console.log('Test 3: Checking RLS policies...');
    const { data: policies, error: policiesError } = await supabase.rpc('get_policies', { table_name: 'courses' });
    
    if (policiesError) {
      console.error('Test 3 failed - Error checking RLS policies:', policiesError);
      // This is not a critical failure, so continue
      console.log('Continuing despite RLS policy check failure');
    } else {
      console.log(`Test 3 passed - Found ${policies.length} RLS policies for courses table`);
    }
    
    console.log('All tests passed - Integration verified successfully');
    return true;
  } catch (error) {
    console.error('Error verifying integration:', error);
    return false;
  }
}

// Run the function
fixSupabaseCourseIntegration()
  .then(success => {
    if (success) {
      console.log('Supabase course integration fix completed successfully');
      process.exit(0);
    } else {
      console.error('Supabase course integration fix failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
