/**
 * Supabase RPC Fix Script
 * 
 * This script ensures that all necessary RPC functions exist in the database.
 * It adds missing functions that are needed for various operations including schema repair.
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Create client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Console output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Main function
async function main() {
  console.log(`${colors.bright}${colors.cyan}================================${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}= SUPABASE RPC FUNCTION FIX =${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}================================${colors.reset}`);
  
  try {
    // Step 1: Check connection
    console.log(`${colors.bright}${colors.cyan}=== Checking Connection ===${colors.reset}`);
    await checkConnection();
    
    // Step 2: Create exec_sql function
    console.log(`${colors.bright}${colors.cyan}=== Creating SQL Execution Function ===${colors.reset}`);
    await createExecSQLFunction();
    
    // Step 3: Create schema cache refresh function
    console.log(`${colors.bright}${colors.cyan}=== Creating Schema Cache Refresh Function ===${colors.reset}`);
    await createSchemaCacheRefreshFunction();

    console.log(`\n${colors.bright}${colors.green}✓ RPC functions created successfully!${colors.reset}`);
    console.log(`\n${colors.yellow}You may need to restart your application for changes to take effect.${colors.reset}`);
  } catch (error) {
    console.error(`\n${colors.red}Unexpected error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Check if we can connect to Supabase
async function checkConnection() {
  try {
    const { data, error } = await supabase.from('user_lesson_progress').select('count');
    
    if (error) {
      console.error(`${colors.red}Error connecting to Supabase: ${error.message}${colors.reset}`);
      process.exit(1);
    }
    
    console.log(`${colors.green}✓ Connected to Supabase successfully${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error connecting to Supabase: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Create the exec_sql function
async function createExecSQLFunction() {
  try {
    // Try to call the function to check if it exists
    const { data, error } = await supabase
      .rpc('exec_sql', { sql: 'SELECT 1' })
      .single();
    
    if (!error) {
      console.log(`${colors.green}✓ exec_sql function already exists${colors.reset}`);
      return;
    }
    
    console.log(`${colors.yellow}Creating exec_sql function...${colors.reset}`);
    
    // SQL for creating the function
    const createFuncSql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql text) RETURNS void AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    
    COMMENT ON FUNCTION exec_sql IS 'Execute arbitrary SQL with proper permissions';
    `;
    
    // Create the function directly using the pg client
    const { error: pgError } = await supabase.from('_dummy_table_that_will_fail').select('*');
    if (pgError) {
      console.log(`${colors.yellow}Using alternative method to create function${colors.reset}`);
      // We can't execute SQL directly, so we'll have to rely on the UI to create it
      console.log(`${colors.yellow}Please run the following SQL in the Supabase Dashboard SQL Editor:${colors.reset}`);
      console.log(createFuncSql);
      console.log(`${colors.yellow}... waiting 10 seconds for manual execution ...${colors.reset}`);
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
    
    // Verify the function was created
    const { error: verifyError } = await supabase
      .rpc('exec_sql', { sql: 'SELECT 1' })
      .single();
    
    if (verifyError) {
      console.error(`${colors.red}exec_sql function creation failed: ${verifyError.message}${colors.reset}`);
      return false;
    }
    
    console.log(`${colors.green}✓ exec_sql function created successfully${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Error creating exec_sql function: ${error.message}${colors.reset}`);
    return false;
  }
}

// Create the schema cache refresh function
async function createSchemaCacheRefreshFunction() {
  try {
    // Try to call the function to check if it exists
    const { data, error } = await supabase
      .rpc('refresh_schema_cache')
      .single();
    
    if (!error) {
      console.log(`${colors.green}✓ refresh_schema_cache function already exists${colors.reset}`);
      return;
    }
    
    console.log(`${colors.yellow}Creating refresh_schema_cache function...${colors.reset}`);
    
    // SQL for creating the function
    const createFuncSql = `
    CREATE OR REPLACE FUNCTION refresh_schema_cache() RETURNS void AS $$
    BEGIN
      -- This is a no-op function that serves as a signal to the client to refresh the schema cache
      NULL;
    END;
    $$ LANGUAGE plpgsql;
    
    COMMENT ON FUNCTION refresh_schema_cache IS 'Signal to refresh the schema cache on the client side';
    `;
    
    // Create the function using exec_sql
    const { error: execError } = await supabase
      .rpc('exec_sql', { sql: createFuncSql })
      .single();
    
    if (execError) {
      console.error(`${colors.red}Error creating refresh_schema_cache function: ${execError.message}${colors.reset}`);
      
      // Direct creation failed, show instructions
      console.log(`${colors.yellow}Please run the following SQL in the Supabase Dashboard SQL Editor:${colors.reset}`);
      console.log(createFuncSql);
      console.log(`${colors.yellow}... waiting 10 seconds for manual execution ...${colors.reset}`);
      await new Promise(resolve => setTimeout(resolve, 10000));
    } else {
      console.log(`${colors.green}✓ refresh_schema_cache function created successfully${colors.reset}`);
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Error creating refresh_schema_cache function: ${error.message}${colors.reset}`);
    return false;
  }
}

// Run the script
main(); 