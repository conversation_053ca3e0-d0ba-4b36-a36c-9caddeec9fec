const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Install sharp if not already installed
try {
  require.resolve('sharp');
} catch (e) {
  console.log('Installing sharp...');
  require('child_process').execSync('npm install --save-dev sharp', { stdio: 'inherit' });
}

const PUBLIC_DIR = path.join(__dirname, '..', 'public');

// Create an educational medical icon - a stylized IV bag with graduation cap
async function generateIcons() {
  // Create base SVG for the icon
  const svgIcon = `
    <svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
      <!-- IV Bag -->
      <rect x="156" y="100" width="200" height="300" rx="20" fill="#ffffff" stroke="#E63946" stroke-width="16"/>
      <!-- IV Liquid -->
      <rect x="156" y="250" width="200" height="150" rx="20" fill="#E63946"/>
      <!-- Graduation Cap -->
      <path d="M256 50L96 160L256 270L416 160L256 50Z" fill="#1D3557"/>
      <path d="M176 200V280L256 320L336 280V200L256 240L176 200Z" fill="#1D3557"/>
      <!-- Tassel -->
      <path d="M336 160V280" stroke="#457B9D" stroke-width="8"/>
      <!-- IV Line -->
      <path d="M256 400V450" stroke="#457B9D" stroke-width="8"/>
    </svg>
  `;

  // Write the SVG file first
  fs.writeFileSync(path.join(PUBLIC_DIR, 'icon.svg'), svgIcon);

  // Generate PNG versions
  await sharp(Buffer.from(svgIcon))
    .resize(512, 512)
    .png()
    .toFile(path.join(PUBLIC_DIR, 'logo512.png'));

  await sharp(Buffer.from(svgIcon))
    .resize(192, 192)
    .png()
    .toFile(path.join(PUBLIC_DIR, 'logo192.png'));

  // Generate favicon (32x32 PNG instead of ICO)
  await sharp(Buffer.from(svgIcon))
    .resize(32, 32)
    .png()
    .toFile(path.join(PUBLIC_DIR, 'favicon.png'));

  console.log('Icons generated successfully!');
}

generateIcons().catch(console.error);