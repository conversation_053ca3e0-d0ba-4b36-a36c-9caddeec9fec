/**
 * <PERSON><PERSON><PERSON> to generate TypeScript types from Supabase schema
 * 
 * This script uses the Supabase CLI to generate TypeScript types
 * from your Supabase database schema.
 * 
 * Usage:
 * node scripts/generate-types.js
 * 
 * Requirements:
 * - Supabase CLI installed
 * - Logged in to Supabase CLI
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const OUTPUT_FILE = path.join(__dirname, '..', 'src', 'integrations', 'supabase', 'types.ts');
const PROJECT_ID = 'jibspqwieubavucdtccv'; // Your Supabase project ID

// Function to generate types
function generateTypes() {
  try {
    console.log('Generating TypeScript types from Supabase schema...');
    
    // Create the output directory if it doesn't exist
    const outputDir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Generate types using Supabase CLI
    execSync(`supabase gen types typescript --project-id ${PROJECT_ID} --output ${OUTPUT_FILE}`, {
      stdio: 'inherit'
    });
    
    console.log(`TypeScript types generated successfully at: ${OUTPUT_FILE}`);
  } catch (error) {
    console.error('Error generating types:', error.message);
    process.exit(1);
  }
}

// Run the function
generateTypes();
