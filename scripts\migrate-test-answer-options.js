#!/usr/bin/env node

/**
 * Migration script to update existing module tests to use the new standardized answer options
 * Changes from: "do not understand/not familiar" to "very understanding/very familiar"
 * To: "Strongly disagree", "Disagree", "Agree", "Strongly agree"
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateTestAnswerOptions() {
  console.log('🔄 Migrating test answer options to new standardized format...\n');
  
  try {
    // 1. Fetch all existing module tests
    console.log('1. Fetching existing module tests...');
    
    const { data: tests, error: testsError } = await supabase
      .from('module_tests')
      .select('*');
    
    if (testsError) {
      console.log('❌ Error fetching tests:', testsError.message);
      return;
    }
    
    if (!tests || tests.length === 0) {
      console.log('ℹ️  No tests found to migrate');
      return;
    }
    
    console.log(`✅ Found ${tests.length} tests to migrate`);
    
    // 2. Update each test's questions
    let updatedCount = 0;
    
    for (const test of tests) {
      console.log(`\n2. Processing test: "${test.title}" (${test.type})`);
      
      if (!test.questions || test.questions.length === 0) {
        console.log('   ⚠️  No questions found, skipping...');
        continue;
      }
      
      // Parse questions if they're stored as JSON string
      let questions = test.questions;
      if (typeof questions === 'string') {
        try {
          questions = JSON.parse(questions);
        } catch (e) {
          console.log('   ❌ Error parsing questions JSON, skipping...');
          continue;
        }
      }
      
      // Update each question's labels
      let hasChanges = false;
      const updatedQuestions = questions.map(question => {
        if (question.minLabel === 'do not understand/not familiar' || 
            question.maxLabel === 'very understanding/very familiar') {
          hasChanges = true;
          return {
            ...question,
            minLabel: 'Strongly disagree',
            maxLabel: 'Strongly agree'
          };
        }
        return question;
      });
      
      if (!hasChanges) {
        console.log('   ✅ Already using new format, skipping...');
        continue;
      }
      
      // Update the test in the database
      const { error: updateError } = await supabase
        .from('module_tests')
        .update({
          questions: updatedQuestions,
          description: 'Please indicate your level of agreement with each statement using the scale provided.',
          updated_at: new Date().toISOString()
        })
        .eq('id', test.id);
      
      if (updateError) {
        console.log(`   ❌ Error updating test: ${updateError.message}`);
        continue;
      }
      
      console.log(`   ✅ Updated ${updatedQuestions.length} questions`);
      updatedCount++;
    }
    
    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Summary:`);
    console.log(`   • Total tests found: ${tests.length}`);
    console.log(`   • Tests updated: ${updatedCount}`);
    console.log(`   • Tests skipped: ${tests.length - updatedCount}`);
    
    console.log(`\n📋 Changes made:`);
    console.log(`   • Updated minLabel: "do not understand/not familiar" → "Strongly disagree"`);
    console.log(`   • Updated maxLabel: "very understanding/very familiar" → "Strongly agree"`);
    console.log(`   • Updated description to match new answer format`);
    console.log(`   • Maintained 1-4 rating scale`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
  }
}

// Run the migration
migrateTestAnswerOptions();
