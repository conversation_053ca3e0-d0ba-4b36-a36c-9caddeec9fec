/**
 * Standardized Migration Script
 * 
 * This script provides a standardized way to apply database migrations using the Supabase CLI.
 * It replaces the previous custom migration scripts with a more reliable approach.
 * 
 * Usage:
 * node scripts/migrate.js [options]
 * 
 * Options:
 *   --local       Apply migrations to the local development database (default)
 *   --remote      Apply migrations to the remote production database
 *   --create      Create a new migration file
 *   --name=NAME   Name for the new migration (required with --create)
 *   --reset       Reset the database before applying migrations (USE WITH CAUTION)
 *   --help        Show this help message
 * 
 * Examples:
 *   node scripts/migrate.js                     # Apply migrations to local database
 *   node scripts/migrate.js --remote            # Apply migrations to remote database
 *   node scripts/migrate.js --create --name=add_new_table  # Create a new migration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } = require('./config');

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    local: true,
    remote: false,
    create: false,
    name: null,
    reset: false,
    help: false
  };

  for (const arg of args) {
    if (arg === '--local') options.local = true;
    else if (arg === '--remote') {
      options.local = false;
      options.remote = true;
    }
    else if (arg === '--create') options.create = true;
    else if (arg.startsWith('--name=')) options.name = arg.substring(7);
    else if (arg === '--reset') options.reset = true;
    else if (arg === '--help') options.help = true;
  }

  return options;
}

// Show help message
function showHelp() {
  console.log(`
Standardized Migration Script
-----------------------------

Usage: node scripts/migrate.js [options]

Options:
  --local       Apply migrations to the local development database (default)
  --remote      Apply migrations to the remote production database
  --create      Create a new migration file
  --name=NAME   Name for the new migration (required with --create)
  --reset       Reset the database before applying migrations (USE WITH CAUTION)
  --help        Show this help message

Examples:
  node scripts/migrate.js                     # Apply migrations to local database
  node scripts/migrate.js --remote            # Apply migrations to remote database
  node scripts/migrate.js --create --name=add_new_table  # Create a new migration
  `);
}

// Check if Supabase CLI is installed
function checkSupabaseCLI() {
  try {
    execSync('supabase --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    console.error('Supabase CLI is not installed or not in PATH.');
    console.log('Please install it using: npm install -g supabase');
    return false;
  }
}

// Create a new migration file
function createMigration(name) {
  if (!name) {
    console.error('Error: Migration name is required with --create option.');
    console.log('Example: node scripts/migrate.js --create --name=add_new_table');
    return false;
  }

  try {
    const timestamp = new Date().toISOString().replace(/[-T:.Z]/g, '').substring(0, 14);
    const fileName = `${timestamp}_${name}.sql`;
    const filePath = path.join(__dirname, '..', 'supabase', 'migrations', fileName);
    
    // Create migrations directory if it doesn't exist
    const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir, { recursive: true });
    }
    
    // Create the migration file with a template
    const template = `-- Migration: ${name}
-- Created at: ${new Date().toISOString()}
-- Description: Add your migration description here

-- Write your SQL statements here
-- Example:
-- CREATE TABLE IF NOT EXISTS public.new_table (
--   id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
--   name TEXT NOT NULL,
--   created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
-- );

-- Remember to add appropriate RLS policies if needed
-- Example:
-- ALTER TABLE public.new_table ENABLE ROW LEVEL SECURITY;
-- 
-- CREATE POLICY "Users can view their own data" 
-- ON public.new_table FOR SELECT 
-- USING (auth.uid() = user_id);
`;
    
    fs.writeFileSync(filePath, template);
    console.log(`Migration file created: ${fileName}`);
    console.log(`Edit the file at: ${filePath}`);
    return true;
  } catch (error) {
    console.error('Error creating migration file:', error);
    return false;
  }
}

// Apply migrations to local database
function applyLocalMigrations(reset = false) {
  try {
    console.log('Applying migrations to local database...');
    
    if (reset) {
      console.log('Resetting local database...');
      execSync('supabase db reset', { stdio: 'inherit' });
    } else {
      execSync('supabase db push', { stdio: 'inherit' });
    }
    
    console.log('Migrations applied successfully to local database.');
    return true;
  } catch (error) {
    console.error('Error applying migrations to local database:', error);
    return false;
  }
}

// Apply migrations to remote database
function applyRemoteMigrations(reset = false) {
  if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
    console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required for remote migrations.');
    console.log('Please set these variables in your .env file or environment.');
    return false;
  }
  
  try {
    console.log('Applying migrations to remote database...');
    
    // Construct the database URL
    const dbUrl = `${SUPABASE_URL}/rest/v1/?apikey=${SUPABASE_SERVICE_ROLE_KEY}`;
    
    if (reset) {
      console.error('Error: Reset is not supported for remote databases.');
      console.log('Please use the Supabase dashboard to reset your remote database.');
      return false;
    } else {
      execSync(`supabase db push --db-url "${dbUrl}"`, { stdio: 'inherit' });
    }
    
    console.log('Migrations applied successfully to remote database.');
    return true;
  } catch (error) {
    console.error('Error applying migrations to remote database:', error);
    return false;
  }
}

// Main function
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  if (!checkSupabaseCLI()) {
    return;
  }
  
  if (options.create) {
    return createMigration(options.name);
  }
  
  if (options.remote) {
    return applyRemoteMigrations(options.reset);
  } else {
    return applyLocalMigrations(options.reset);
  }
}

// Run the script
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
