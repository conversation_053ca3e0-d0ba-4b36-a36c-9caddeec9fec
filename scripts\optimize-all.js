// This script runs all optimizations to make the application load faster
// Run this script with: node scripts/optimize-all.js

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting comprehensive optimization...');

// Function to read a file
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return null;
  }
}

// Function to write a file
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error.message);
    return false;
  }
}

// Function to run a command
function runCommand(command) {
  try {
    console.log(`Running command: ${command}`);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Error running command: ${command}`, error.message);
    return false;
  }
}

// 1. Create a script to optimize images
function createImageOptimizationScript() {
  const filePath = path.join(__dirname, '..', 'scripts', 'optimize-images.js');
  const content = `// This script optimizes all images in the public directory
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting image optimization...');

// Install sharp if not already installed
try {
  require.resolve('sharp');
  console.log('sharp is already installed');
} catch (e) {
  console.log('Installing sharp...');
  execSync('npm install --save-dev sharp', { stdio: 'inherit' });
}

const sharp = require('sharp');

// Function to optimize an image
async function optimizeImage(inputPath, outputPath) {
  try {
    await sharp(inputPath)
      .resize(1200, null, { withoutEnlargement: true }) // Resize to max width 1200px
      .webp({ quality: 80 }) // Convert to WebP with 80% quality
      .toFile(outputPath);
    
    console.log(\`Optimized: \${inputPath} -> \${outputPath}\`);
    return true;
  } catch (error) {
    console.error(\`Error optimizing \${inputPath}:\`, error.message);
    return false;
  }
}

// Function to process all images in a directory
async function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Recursively process subdirectories
      await processDirectory(filePath);
    } else if (/\\.(jpe?g|png|gif)$/i.test(file)) {
      // Process image files
      const outputPath = filePath.replace(/\\.[^.]+$/, '.webp');
      await optimizeImage(filePath, outputPath);
    }
  }
}

// Start processing images
(async () => {
  const publicDir = path.join(__dirname, '..', 'public');
  
  if (!fs.existsSync(publicDir)) {
    console.error(\`Public directory not found: \${publicDir}\`);
    process.exit(1);
  }
  
  console.log(\`Processing images in \${publicDir}...\`);
  await processDirectory(publicDir);
  
  console.log('Image optimization completed!');
})();
`;

  return writeFile(filePath, content);
}

// 2. Create a script to optimize fonts
function createFontOptimizationScript() {
  const filePath = path.join(__dirname, '..', 'scripts', 'optimize-fonts.js');
  const content = `// This script optimizes font loading
const fs = require('fs');
const path = require('path');
const https = require('https');

console.log('Starting font optimization...');

// Function to download a file
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(outputPath);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', (error) => {
      fs.unlink(outputPath, () => {});
      reject(error);
    });
  });
}

// Create fonts directory if it doesn't exist
const fontsDir = path.join(__dirname, '..', 'public', 'fonts');
if (!fs.existsSync(fontsDir)) {
  fs.mkdirSync(fontsDir, { recursive: true });
}

// Download Inter font files
(async () => {
  const fontFiles = [
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2',
      output: path.join(fontsDir, 'inter-400.woff2')
    },
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7.woff2',
      output: path.join(fontsDir, 'inter-500.woff2')
    },
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7.woff2',
      output: path.join(fontsDir, 'inter-600.woff2')
    }
  ];

  for (const font of fontFiles) {
    try {
      console.log(\`Downloading \${font.url}...\`);
      await downloadFile(font.url, font.output);
      console.log(\`Downloaded to \${font.output}\`);
    } catch (error) {
      console.error(\`Error downloading \${font.url}:\`, error.message);
    }
  }

  // Create CSS file for local fonts
  const cssContent = \`/* Local font definitions */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/inter-400.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('/fonts/inter-500.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('/fonts/inter-600.woff2') format('woff2');
}
\`;

  const cssPath = path.join(fontsDir, 'fonts.css');
  fs.writeFileSync(cssPath, cssContent);
  console.log(\`Created font CSS at \${cssPath}\`);

  console.log('Font optimization completed!');
})();
`;

  return writeFile(filePath, content);
}

// 3. Update index.html to use local fonts
function updateIndexHtmlForLocalFonts() {
  const filePath = path.join(__dirname, '..', 'index.html');
  let content = readFile(filePath);
  if (!content) return false;

  // Replace Google Fonts with local fonts
  content = content.replace(
    /<link rel="preload" href="https:\/\/fonts\.googleapis\.com\/css2\?family=Inter:wght@400;500;600&display=swap" as="style" \/>\s*<link rel="stylesheet" href="https:\/\/fonts\.googleapis\.com\/css2\?family=Inter:wght@400;500;600&display=swap" \/>/g,
    '<link rel="stylesheet" href="/fonts/fonts.css" />'
  );

  // Remove preconnect to Google Fonts
  content = content.replace(
    /<link rel="preconnect" href="https:\/\/fonts\.googleapis\.com" crossorigin \/>\s*<link rel="preconnect" href="https:\/\/fonts\.gstatic\.com" crossorigin \/>/g,
    ''
  );

  return writeFile(filePath, content);
}

// 4. Create a script to optimize CSS
function createCssOptimizationScript() {
  const filePath = path.join(__dirname, '..', 'scripts', 'optimize-css.js');
  const content = `// This script optimizes CSS by removing unused styles
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting CSS optimization...');

// Install PurgeCSS if not already installed
try {
  require.resolve('purgecss');
  console.log('purgecss is already installed');
} catch (e) {
  console.log('Installing purgecss...');
  execSync('npm install --save-dev purgecss', { stdio: 'inherit' });
}

const { PurgeCSS } = require('purgecss');

// Function to optimize CSS
async function optimizeCss() {
  try {
    const result = await new PurgeCSS().purge({
      content: [
        './src/**/*.{js,jsx,ts,tsx}',
        './index.html'
      ],
      css: [
        './src/index.css',
        './src/**/*.css'
      ],
      safelist: {
        standard: [/^dark/, /^light/, /^bg-/, /^text-/, /^border-/, /^hover:/, /^focus:/, /^active:/],
        deep: [/^dark/, /^light/],
        greedy: [/^dark/, /^light/]
      }
    });

    // Create optimized CSS file
    const outputDir = path.join(__dirname, '..', 'src', 'optimized');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    for (const file of result) {
      const outputPath = path.join(outputDir, path.basename(file.file));
      fs.writeFileSync(outputPath, file.css);
      console.log(\`Optimized CSS saved to \${outputPath}\`);
    }

    console.log('CSS optimization completed!');
    return true;
  } catch (error) {
    console.error('Error optimizing CSS:', error.message);
    return false;
  }
}

// Run CSS optimization
optimizeCss();
`;

  return writeFile(filePath, content);
}

// 5. Create a script to run all optimizations
function createRunAllScript() {
  const filePath = path.join(__dirname, '..', 'scripts', 'run-all-optimizations.js');
  const content = `// This script runs all optimization scripts
const { execSync } = require('child_process');

console.log('Running all optimizations...');

const scripts = [
  'optimize-performance.js',
  'optimize-bundle.js',
  'optimize-images.js',
  'optimize-fonts.js',
  'optimize-css.js'
];

for (const script of scripts) {
  try {
    console.log(\`\\n=== Running \${script} ===\\n\`);
    execSync(\`node scripts/\${script}\`, { stdio: 'inherit' });
  } catch (error) {
    console.error(\`Error running \${script}:\`, error.message);
  }
}

console.log('\\n=== Building production version ===\\n');
execSync('npm run build:prod', { stdio: 'inherit' });

console.log('\\nAll optimizations completed!');
`;

  return writeFile(filePath, content);
}

// 6. Update package.json to include the new scripts
function updatePackageJson() {
  const filePath = path.join(__dirname, '..', 'package.json');
  let content = readFile(filePath);
  if (!content) return false;

  // Add the new scripts
  content = content.replace(
    /"optimize:bundle": "node scripts\/optimize-bundle.js"/g,
    `"optimize:bundle": "node scripts/optimize-bundle.js",
    "optimize:images": "node scripts/optimize-images.js",
    "optimize:fonts": "node scripts/optimize-fonts.js",
    "optimize:css": "node scripts/optimize-css.js",
    "optimize:all": "node scripts/run-all-optimizations.js"`
  );

  return writeFile(filePath, content);
}

// Run all optimizations
async function runAllOptimizations() {
  console.log('Setting up comprehensive optimization scripts...');
  
  const results = {
    imageOptimization: createImageOptimizationScript(),
    fontOptimization: createFontOptimizationScript(),
    updateIndexHtml: updateIndexHtmlForLocalFonts(),
    cssOptimization: createCssOptimizationScript(),
    runAllScript: createRunAllScript(),
    updatePackageJson: updatePackageJson()
  };
  
  console.log('\n--- Setup Results ---');
  for (const [script, result] of Object.entries(results)) {
    console.log(`${script}: ${result ? 'Success' : 'Failed'}`);
  }
  
  if (Object.values(results).every(result => result)) {
    console.log('\nAll optimization scripts created successfully!');
    console.log('\nTo run all optimizations at once, use:');
    console.log('npm run optimize:all');
    console.log('\nOr run individual optimizations with:');
    console.log('npm run optimize:performance');
    console.log('npm run optimize:bundle');
    console.log('npm run optimize:images');
    console.log('npm run optimize:fonts');
    console.log('npm run optimize:css');
  } else {
    console.log('\nSome scripts could not be created. Please check the logs above for details.');
  }
}

// Run the setup
runAllOptimizations();
