// This script optimizes CSS by removing unused styles
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting CSS optimization...');

// Install PurgeCSS if not already installed
try {
  require.resolve('purgecss');
  console.log('purgecss is already installed');
} catch (e) {
  console.log('Installing purgecss...');
  execSync('npm install --save-dev purgecss', { stdio: 'inherit' });
}

const { PurgeCSS } = require('purgecss');

// Function to optimize CSS
async function optimizeCss() {
  try {
    const result = await new PurgeCSS().purge({
      content: [
        './src/**/*.{js,jsx,ts,tsx}',
        './index.html'
      ],
      css: [
        './src/index.css',
        './src/**/*.css'
      ],
      safelist: {
        standard: [/^dark/, /^light/, /^bg-/, /^text-/, /^border-/, /^hover:/, /^focus:/, /^active:/],
        deep: [/^dark/, /^light/],
        greedy: [/^dark/, /^light/]
      }
    });

    // Create optimized CSS file
    const outputDir = path.join(__dirname, '..', 'src', 'optimized');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    for (const file of result) {
      const outputPath = path.join(outputDir, path.basename(file.file));
      fs.writeFileSync(outputPath, file.css);
      console.log(`Optimized CSS saved to ${outputPath}`);
    }

    console.log('CSS optimization completed!');
    return true;
  } catch (error) {
    console.error('Error optimizing CSS:', error.message);
    return false;
  }
}

// Run CSS optimization
optimizeCss();
