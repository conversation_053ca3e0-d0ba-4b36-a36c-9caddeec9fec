// This script optimizes font loading
const fs = require('fs');
const path = require('path');
const https = require('https');

console.log('Starting font optimization...');

// Function to download a file
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(outputPath);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', (error) => {
      fs.unlink(outputPath, () => {});
      reject(error);
    });
  });
}

// Create fonts directory if it doesn't exist
const fontsDir = path.join(__dirname, '..', 'public', 'fonts');
if (!fs.existsSync(fontsDir)) {
  fs.mkdirSync(fontsDir, { recursive: true });
}

// Download Inter font files
(async () => {
  const fontFiles = [
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2',
      output: path.join(fontsDir, 'inter-400.woff2')
    },
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7.woff2',
      output: path.join(fontsDir, 'inter-500.woff2')
    },
    {
      url: 'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7.woff2',
      output: path.join(fontsDir, 'inter-600.woff2')
    }
  ];

  for (const font of fontFiles) {
    try {
      console.log(`Downloading ${font.url}...`);
      await downloadFile(font.url, font.output);
      console.log(`Downloaded to ${font.output}`);
    } catch (error) {
      console.error(`Error downloading ${font.url}:`, error.message);
    }
  }

  // Create CSS file for local fonts
  const cssContent = `/* Local font definitions */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/inter-400.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('/fonts/inter-500.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('/fonts/inter-600.woff2') format('woff2');
}
`;

  const cssPath = path.join(fontsDir, 'fonts.css');
  fs.writeFileSync(cssPath, cssContent);
  console.log(`Created font CSS at ${cssPath}`);

  console.log('Font optimization completed!');
})();
