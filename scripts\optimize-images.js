// This script optimizes all images in the public directory
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting image optimization...');

// Install sharp if not already installed
try {
  require.resolve('sharp');
  console.log('sharp is already installed');
} catch (e) {
  console.log('Installing sharp...');
  execSync('npm install --save-dev sharp', { stdio: 'inherit' });
}

const sharp = require('sharp');

// Function to optimize an image
async function optimizeImage(inputPath, outputPath) {
  try {
    await sharp(inputPath)
      .resize(1200, null, { withoutEnlargement: true }) // Resize to max width 1200px
      .webp({ quality: 80 }) // Convert to WebP with 80% quality
      .toFile(outputPath);
    
    console.log(`Optimized: ${inputPath} -> ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Error optimizing ${inputPath}:`, error.message);
    return false;
  }
}

// Function to process all images in a directory
async function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Recursively process subdirectories
      await processDirectory(filePath);
    } else if (/\.(jpe?g|png|gif)$/i.test(file)) {
      // Process image files
      const outputPath = filePath.replace(/\.[^.]+$/, '.webp');
      await optimizeImage(filePath, outputPath);
    }
  }
}

// Start processing images
(async () => {
  const publicDir = path.join(__dirname, '..', 'public');
  
  if (!fs.existsSync(publicDir)) {
    console.error(`Public directory not found: ${publicDir}`);
    process.exit(1);
  }
  
  console.log(`Processing images in ${publicDir}...`);
  await processDirectory(publicDir);
  
  console.log('Image optimization completed!');
})();
