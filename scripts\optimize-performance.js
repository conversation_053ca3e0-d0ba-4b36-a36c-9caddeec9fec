// This script optimizes the application for better performance
// Run this script with: node scripts/optimize-performance.js

const fs = require('fs');
const path = require('path');

console.log('Starting performance optimization...');

// Function to read a file
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return null;
  }
}

// Function to write a file
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error.message);
    return false;
  }
}

// 1. Optimize App.tsx - Remove StrictMode and optimize providers
function optimizeAppTsx() {
  const filePath = path.join(__dirname, '..', 'src', 'App.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Remove StrictMode which causes double rendering in development
  content = content.replace(
    /<React.StrictMode>\s*<EnhancedThemeProvider/g,
    '<EnhancedThemeProvider'
  );
  
  content = content.replace(
    /<\/OnboardingWrapper>\s*<\/AuthProvider>\s*<\/BrowserRouter>\s*<\/TooltipProvider>\s*<\/QueryClientProvider>\s*<\/MotionProvider>\s*<\/EnhancedThemeProvider>\s*<\/React.StrictMode>/g,
    '</OnboardingWrapper></AuthProvider></BrowserRouter></TooltipProvider></QueryClientProvider></MotionProvider></EnhancedThemeProvider>'
  );

  // Optimize preloadRoutes function
  content = content.replace(
    /const preloadRoutes = \(\) => {\s*\/\/ Use a small timeout to ensure this happens after the main page is rendered\s*setTimeout\(\(\) => {\s*\/\/ Preload the most commonly accessed routes\s*import\("\.\/pages\/Dashboard"\);\s*import\("\.\/pages\/Login"\);\s*}, 2000\);\s*};/g,
    `const preloadRoutes = () => {
  // Use requestIdleCallback for better performance
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      import("./pages/Dashboard");
      import("./pages/Login");
    }, { timeout: 2000 });
  } else {
    // Fallback for browsers that don't support requestIdleCallback
    setTimeout(() => {
      import("./pages/Dashboard");
      import("./pages/Login");
    }, 2000);
  }
};`
  );

  return writeFile(filePath, content);
}

// 2. Optimize main.tsx - Improve initial rendering
function optimizeMainTsx() {
  const filePath = path.join(__dirname, '..', 'src', 'main.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Optimize renderApp function to render faster
  content = content.replace(
    /const renderApp = \(\) => {\s*\/\/ Check if the browser is idle or if we should render immediately\s*const w = window as any;\s*if \('requestIdleCallback' in w\) {\s*\/\/ Use requestIdleCallback for non-critical rendering\s*\/\/ This allows the browser to handle more important tasks first\s*w\.requestIdleCallback\(\(\) => {\s*root\.render\(<App \/>\);\s*\s*\/\/ Mark when the app was rendered\s*if \(import\.meta\.env\.DEV\) {\s*console\.log\(`App rendered in \${\(performance\.now\(\) - startTime\)\.toFixed\(2\)}ms`\);\s*}\s*}, { timeout: 2000 }\); \/\/ Ensure rendering happens within 2 seconds even if browser is busy\s*} else {\s*\/\/ Fallback for browsers that don't support requestIdleCallback\s*setTimeout\(\(\) => {\s*root\.render\(<App \/>\);\s*\s*\/\/ Mark when the app was rendered\s*if \(import\.meta\.env\.DEV\) {\s*console\.log\(`App rendered in \${\(performance\.now\(\) - startTime\)\.toFixed\(2\)}ms`\);\s*}\s*}, 0\);\s*}\s*};/g,
    `const renderApp = () => {
  // Render immediately for better performance
  root.render(<App />);
  
  // Mark when the app was rendered
  if (import.meta.env.DEV) {
    console.log(\`App rendered in \${(performance.now() - startTime).toFixed(2)}ms\`);
  }
};`
  );

  return writeFile(filePath, content);
}

// 3. Optimize AuthContext.tsx - Improve authentication performance
function optimizeAuthContext() {
  const filePath = path.join(__dirname, '..', 'src', 'context', 'AuthContext.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Optimize handleAutoCompletion function to be more efficient
  content = content.replace(
    /const handleAutoCompletion = useCallback\(async \(userId: string\) => {\s*\/\/ Check if we've already run auto-completion for this user in this session\s*const autoCompletionKey = `auto_completion_${userId}`;\s*if \(sessionStorage\.getItem\(autoCompletionKey\)\) {\s*console\.log\('Auto-completion already run for this user in this session'\);\s*return;\s*}\s*\s*\/\/ Use setTimeout to make this non-blocking\s*setTimeout\(async \(\) => {/g,
    `const handleAutoCompletion = useCallback(async (userId: string) => {
  // Check if we've already run auto-completion for this user in this session
  const autoCompletionKey = \`auto_completion_\${userId}\`;
  if (sessionStorage.getItem(autoCompletionKey)) {
    return;
  }
  
  // Use requestIdleCallback to make this non-blocking and more performance-friendly
  if ('requestIdleCallback' in window) {
    (window as any).requestIdleCallback(async () => {`
  );

  // Replace setTimeout with requestIdleCallback closing
  content = content.replace(
    /}, 0\); \/\/ Use 0ms timeout to run immediately but non-blocking/g,
    `}, { timeout: 2000 });
  } else {
    // Fallback for browsers that don't support requestIdleCallback
    setTimeout(async () => {`
  );

  // Add closing bracket for the fallback
  content = content.replace(
    /}, \[isTeacher, isAutoCompletionEnabled\]\);/g,
    `    }, 0);
  }
}, [isTeacher, isAutoCompletionEnabled]);`
  );

  return writeFile(filePath, content);
}

// 4. Optimize MotionContext.tsx - Reduce animation overhead
function optimizeMotionContext() {
  const filePath = path.join(__dirname, '..', 'src', 'context', 'MotionContext.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Optimize transitions for better performance
  content = content.replace(
    /const defaultContext: MotionContextType = {\s*shouldReduceMotion: false,\s*enableAnimations: true,\s*setEnableAnimations: \(\) => {},\s*transitions: {\s*default: {\s*type: 'tween',\s*duration: 0.3,\s*ease: 'easeOut',\s*},\s*spring: {\s*type: 'spring',\s*stiffness: 300,\s*damping: 25,\s*},\s*microInteraction: {\s*type: 'tween',\s*duration: 0.15,\s*},\s*},\s*};/g,
    `const defaultContext: MotionContextType = {
  shouldReduceMotion: false,
  enableAnimations: true,
  setEnableAnimations: () => {},
  transitions: {
    default: {
      type: 'tween',
      duration: 0.2, // Reduced from 0.3
      ease: 'easeOut',
    },
    spring: {
      type: 'spring',
      stiffness: 400, // Increased from 300 for faster animations
      damping: 30, // Increased from 25 for less oscillation
    },
    microInteraction: {
      type: 'tween',
      duration: 0.1, // Reduced from 0.15
    },
  },
};`
  );

  return writeFile(filePath, content);
}

// 5. Optimize enhanced-theme-provider.tsx - Improve theme switching performance
function optimizeThemeProvider() {
  const filePath = path.join(__dirname, '..', 'src', 'components', 'theme', 'enhanced-theme-provider.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Optimize theme transition
  content = content.replace(
    /root\.style\.setProperty\('--theme-transition', 'all 0\.3s ease'\);/g,
    "root.style.setProperty('--theme-transition', 'background-color 0.2s ease, color 0.2s ease');"
  );

  // Reduce timeout duration
  content = content.replace(
    /}, 300\);/g,
    '}, 200);'
  );

  return writeFile(filePath, content);
}

// 6. Optimize animated-hero.tsx - Reduce animation complexity
function optimizeAnimatedHero() {
  const filePath = path.join(__dirname, '..', 'src', 'components', 'ui', 'animated-hero.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Reduce animation complexity
  content = content.replace(
    /animate\({\s*y: \[0, 15, 0\],\s*opacity: \[0\.5, 0\.7, 0\.5\]\s*}\)/g,
    'animate({ y: [0, 10, 0], opacity: [0.4, 0.6, 0.4] })'
  );

  content = content.replace(
    /transition\({\s*repeat: Infinity,\s*duration: 8,\s*ease: "easeInOut"\s*}\)/g,
    'transition({ repeat: Infinity, duration: 10, ease: "easeInOut" })'
  );

  // Optimize motion divs
  content = content.replace(
    /initial\({ opacity: 0, y: 20 }\)/g,
    'initial={{ opacity: 0, y: 10 }}'
  );

  return writeFile(filePath, content);
}

// 7. Optimize particle-button.tsx - Reduce particle effects
function optimizeParticleButton() {
  const filePath = path.join(__dirname, '..', 'src', 'components', 'ui', 'particle-button.tsx');
  let content = readFile(filePath);
  if (!content) return false;

  // Reduce number of particles
  content = content.replace(
    /{\.\.\.Array\(6\)}\.map\(\(_, i\) =>/g,
    '{...Array(4)}.map((_, i) =>'
  );

  // Optimize particle animation
  content = content.replace(
    /scale: \[0, 1, 0\],/g,
    'scale: [0, 0.8, 0],'
  );

  content = content.replace(
    /duration: 0\.6,/g,
    'duration: 0.4,'
  );

  return writeFile(filePath, content);
}

// 8. Optimize index.html - Improve initial loading
function optimizeIndexHtml() {
  const filePath = path.join(__dirname, '..', 'index.html');
  let content = readFile(filePath);
  if (!content) return false;

  // Remove unnecessary preloads
  content = content.replace(
    /<link rel="preload" href="https:\/\/fonts\.googleapis\.com\/css2\?family=Inter:wght@400;500;600&display=swap" as="style" \/>/g,
    ''
  );

  // Optimize font loading
  content = content.replace(
    /<link rel="stylesheet" href="https:\/\/fonts\.googleapis\.com\/css2\?family=Inter:wght@400;500;600&display=swap" media="print" onload="this\.media='all'" \/>/g,
    '<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" />'
  );

  // Remove unnecessary script
  content = content.replace(
    /<script src="https:\/\/cdn\.gpteng\.co\/gptengineer\.js" type="module"><\/script>/g,
    ''
  );

  return writeFile(filePath, content);
}

// 9. Optimize vite.config.ts - Improve build performance
function optimizeViteConfig() {
  const filePath = path.join(__dirname, '..', 'vite.config.ts');
  let content = readFile(filePath);
  if (!content) return false;

  // Enhance build optimization
  content = content.replace(
    /build: {\s*commonjsOptions: {\s*include: \[\/dompurify\/, \/node_modules\/\]\s*},\s*\/\/ Simple optimization settings that work reliably\s*minify: 'esbuild',\s*target: 'es2015',\s*cssCodeSplit: true,\s*chunkSizeWarningLimit: 1000,\s*}/g,
    `build: {
    commonjsOptions: {
      include: [/dompurify/, /node_modules/]
    },
    // Enhanced optimization settings
    minify: 'esbuild',
    target: 'es2015',
    cssCodeSplit: true,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: [
            '@/components/ui/button',
            '@/components/ui/dialog',
            '@/components/ui/form',
            '@/components/ui/input',
            '@/components/ui/tabs'
          ],
          supabase: ['@supabase/supabase-js']
        }
      }
    },
    // Improve tree-shaking
    modulePreload: true,
    reportCompressedSize: false
  }`
  );

  return writeFile(filePath, content);
}

// 10. Create a new preload-critical.js file for better asset preloading
function createPreloadCritical() {
  const filePath = path.join(__dirname, '..', 'public', 'preload-critical.js');
  const content = `// This script preloads critical assets for better performance
(() => {
  try {
    // Only run in production
    if (window.location.hostname === 'localhost') return;

    // Function to preload JavaScript
    const preloadScript = (src) => {
      const link = document.createElement('link');
      link.rel = 'modulepreload';
      link.href = src;
      document.head.appendChild(link);
    };

    // Function to preload CSS
    const preloadStyle = (href) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      document.head.appendChild(link);
    };

    // Function to preload images
    const preloadImage = (src) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    };

    // Preload main chunks based on route
    const path = window.location.pathname;
    
    // Preload common assets for all routes
    if (path === '/') {
      // Home page
      preloadScript('/assets/index.js');
      preloadStyle('/assets/index.css');
    } else if (path.includes('/dashboard')) {
      // Dashboard page
      preloadScript('/assets/Dashboard.js');
    } else if (path.includes('/profile')) {
      // Profile page
      preloadScript('/assets/Profile.js');
    }
    
    // Preload vendor chunks for all routes
    preloadScript('/assets/vendor.js');
    
    // Preload common images
    preloadImage('/placeholder-avatar.svg');
  } catch (error) {
    console.warn('Error in preload-critical script:', error);
  }
})();
`;

  return writeFile(filePath, content);
}

// 11. Update index.html to include the new preload-critical.js
function updateIndexHtmlForPreload() {
  const filePath = path.join(__dirname, '..', 'index.html');
  let content = readFile(filePath);
  if (!content) return false;

  // Add the new preload script
  content = content.replace(
    /<script src="\/preload\.js" async><\/script>/g,
    '<script src="/preload-critical.js" async></script>'
  );

  return writeFile(filePath, content);
}

// Run all optimizations
async function runAllOptimizations() {
  console.log('Running all performance optimizations...');
  
  const results = {
    appTsx: optimizeAppTsx(),
    mainTsx: optimizeMainTsx(),
    authContext: optimizeAuthContext(),
    motionContext: optimizeMotionContext(),
    themeProvider: optimizeThemeProvider(),
    animatedHero: optimizeAnimatedHero(),
    particleButton: optimizeParticleButton(),
    indexHtml: optimizeIndexHtml(),
    viteConfig: optimizeViteConfig(),
    preloadCritical: createPreloadCritical(),
    updateIndexHtml: updateIndexHtmlForPreload()
  };
  
  console.log('\n--- Optimization Results ---');
  for (const [optimization, result] of Object.entries(results)) {
    console.log(`${optimization}: ${result ? 'Success' : 'Failed'}`);
  }
  
  if (Object.values(results).every(result => result)) {
    console.log('\nAll optimizations applied successfully!');
    console.log('\nTo apply these changes, please rebuild your application with:');
    console.log('npm run build');
    console.log('\nThen preview the optimized build with:');
    console.log('npm run preview');
  } else {
    console.log('\nSome optimizations failed. Please check the logs above for details.');
  }
}

// Run the optimizations
runAllOptimizations();
