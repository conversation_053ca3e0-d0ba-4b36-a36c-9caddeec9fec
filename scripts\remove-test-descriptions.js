#!/usr/bin/env node

/**
 * <PERSON>ript to remove description text from all module tests in the database
 * This will make the test interface cleaner by removing instructional text
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function removeTestDescriptions() {
  console.log('🧹 Removing description text from all module tests...\n');
  
  try {
    // 1. Fetch all existing module tests
    console.log('1. Fetching existing module tests...');
    
    const { data: tests, error: testsError } = await supabase
      .from('module_tests')
      .select('id, title, type, description');
    
    if (testsError) {
      console.log('❌ Error fetching tests:', testsError.message);
      return;
    }
    
    if (!tests || tests.length === 0) {
      console.log('ℹ️  No tests found');
      return;
    }
    
    console.log(`✅ Found ${tests.length} tests to process`);
    
    // 2. Count tests that have descriptions
    const testsWithDescriptions = tests.filter(test => test.description && test.description.trim() !== '');
    console.log(`📝 Tests with descriptions: ${testsWithDescriptions.length}`);
    
    if (testsWithDescriptions.length === 0) {
      console.log('✅ All tests already have empty descriptions');
      return;
    }
    
    // 3. Show what descriptions will be removed
    console.log('\n📋 Descriptions to be removed:');
    testsWithDescriptions.forEach((test, index) => {
      console.log(`   ${index + 1}. ${test.title} (${test.type})`);
      console.log(`      "${test.description}"`);
    });
    
    // 4. Remove descriptions from all tests
    console.log('\n🔄 Removing descriptions...');
    
    const { error: updateError } = await supabase
      .from('module_tests')
      .update({
        description: null,
        updated_at: new Date().toISOString()
      })
      .not('description', 'is', null);
    
    if (updateError) {
      console.log('❌ Error removing descriptions:', updateError.message);
      return;
    }
    
    console.log('✅ Successfully removed descriptions from all tests');
    
    // 5. Verify the changes
    console.log('\n🔍 Verifying changes...');
    
    const { data: updatedTests, error: verifyError } = await supabase
      .from('module_tests')
      .select('id, title, description')
      .not('description', 'is', null);
    
    if (verifyError) {
      console.log('❌ Error verifying changes:', verifyError.message);
      return;
    }
    
    if (!updatedTests || updatedTests.length === 0) {
      console.log('✅ Verification successful: No tests have descriptions');
    } else {
      console.log(`⚠️  Warning: ${updatedTests.length} tests still have descriptions`);
      updatedTests.forEach(test => {
        console.log(`   - ${test.title}: "${test.description}"`);
      });
    }
    
    console.log('\n🎉 Description removal completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Total tests processed: ${tests.length}`);
    console.log(`   • Descriptions removed: ${testsWithDescriptions.length}`);
    console.log(`   • Tests already clean: ${tests.length - testsWithDescriptions.length}`);
    
    console.log('\n✨ Benefits:');
    console.log('   • Cleaner, more streamlined test interface');
    console.log('   • Reduced visual clutter');
    console.log('   • Self-explanatory answer options');
    console.log('   • Better focus on actual questions');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the script
removeTestDescriptions();
