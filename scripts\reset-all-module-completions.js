// This script resets all module completion statuses
// Run this script with: node scripts/reset-all-module-completions.js

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function resetAllModuleCompletions() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Resetting all module completion statuses...');

    // Step 1: Reset all module completion statuses in the modules table
    console.log('Resetting module completion statuses in modules table...');
    const { error: moduleError } = await client
      .from('modules')
      .update({ is_completed: false })
      .not('id', 'is', null); // This will update all records

    if (moduleError) {
      console.error('Error resetting module completion statuses:', moduleError);
      process.exit(1);
    }

    // Step 2: Delete all records from the user_module_progress table
    console.log('Deleting all records from user_module_progress table...');
    try {
      const { error: deleteError } = await client
        .from('user_module_progress')
        .delete()
        .not('id', 'is', null); // This will delete all records

      if (deleteError) {
        console.error('Error deleting user module progress records:', deleteError);
        process.exit(1);
      }
    } catch (error) {
      // If the table doesn't exist yet, that's fine
      console.log('Note: user_module_progress table may not exist yet');
    }

    console.log('Successfully reset all module completion statuses');

  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
resetAllModuleCompletions();
