// This script resets module completion status for a user
// Run this script with: node scripts/reset-module-completion.js <user_id>

const { createClient } = require('@supabase/supabase-js');
const { argv, exit } = require('process');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function resetModuleCompletion(userId) {
  if (!userId) {
    console.error('Usage: node scripts/reset-module-completion.js <user_id>');
    exit(1);
  }

  console.log(`Resetting module completion for user ${userId}...`);

  try {
    const client = serviceRoleClient || supabase;

    // Approach 1: Try using the RPC function
    try {
      console.log('Approach 1: Using RPC function...');
      const { data, error } = await client.rpc('reset_module_completion', {
        p_user_id: userId
      });

      if (error) {
        console.error('RPC approach failed:', error);
      } else {
        console.log('RPC approach succeeded:', data);
        console.log('Module completion reset successfully!');
        return;
      }
    } catch (rpcError) {
      console.error('Error in RPC approach:', rpcError);
    }

    // Approach 2: Direct database operations
    console.log('Approach 2: Direct database operations...');

    // Delete all user lesson progress
    const { error: deleteError } = await client
      .from('user_lesson_progress')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting lesson progress:', deleteError);
      exit(1);
    }

    console.log('Successfully deleted all lesson progress');

    // Update user preferences to disable auto-completion
    const { error: prefError } = await client
      .from('user_preferences')
      .upsert({
        user_id: userId,
        auto_complete_courses: false,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (prefError) {
      console.error('Error updating user preferences:', prefError);
      // Continue anyway as this is not critical
    } else {
      console.log('Successfully disabled auto-completion for user');
    }

    console.log('Module completion reset successfully!');
  } catch (error) {
    console.error('Unexpected error:', error);
    exit(1);
  }
}

// Get command line arguments
const userId = argv[2];

// Run the function
resetModuleCompletion(userId);
