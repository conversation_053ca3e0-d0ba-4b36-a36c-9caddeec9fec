// This script runs all optimization scripts
const { execSync } = require('child_process');

console.log('Running all optimizations...');

const scripts = [
  'optimize-performance.js',
  'optimize-bundle.js',
  'optimize-images.js',
  'optimize-fonts.js',
  'optimize-css.js'
];

for (const script of scripts) {
  try {
    console.log(`\n=== Running ${script} ===\n`);
    execSync(`node scripts/${script}`, { stdio: 'inherit' });
  } catch (error) {
    console.error(`Error running ${script}:`, error.message);
  }
}

console.log('\n=== Building production version ===\n');
execSync('npm run build:prod', { stdio: 'inherit' });

console.log('\nAll optimizations completed!');
