/**
 * Test Application Connection
 * This script tests the actual application-level database connection
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client as the app would (with anon key)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testAppConnection() {
  console.log('🔧 Testing application-level connection...');
  
  try {
    // Test 1: Basic connection with health check
    const { data: healthData, error: healthError } = await supabase.rpc('health_check');
    
    if (healthError) {
      console.error(`❌ Health check failed: ${healthError.message}`);
      return false;
    }
    
    console.log('✅ Health check successful:', healthData);
    
    // Test 2: Read from courses table (public access)
    const { data: coursesData, error: coursesError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(5);
    
    if (coursesError) {
      console.error(`❌ Courses read failed: ${coursesError.message}`);
      return false;
    }
    
    console.log(`✅ Courses read successful: Found ${coursesData?.length || 0} courses`);
    
    // Test 3: Read from modules table
    const { data: modulesData, error: modulesError } = await supabase
      .from('modules')
      .select('id, title')
      .limit(5);
    
    if (modulesError) {
      console.error(`❌ Modules read failed: ${modulesError.message}`);
      return false;
    }
    
    console.log(`✅ Modules read successful: Found ${modulesData?.length || 0} modules`);
    
    // Test 4: Check authentication state
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error(`❌ Auth check failed: ${authError.message}`);
      return false;
    }
    
    console.log('✅ Auth system working:', session ? 'User logged in' : 'No active session (normal)');
    
    return true;
    
  } catch (err) {
    console.error(`❌ Connection test error: ${err.message}`);
    return false;
  }
}

async function testStorageAccess() {
  console.log('\n🔧 Testing storage access...');
  
  try {
    // Test storage bucket access
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error(`❌ Storage access failed: ${error.message}`);
      return false;
    }
    
    console.log('✅ Storage access successful:', buckets.map(b => b.name));
    return true;
    
  } catch (err) {
    console.error(`❌ Storage test error: ${err.message}`);
    return false;
  }
}

async function testRealTimeConnection() {
  console.log('\n🔧 Testing real-time connection...');
  
  try {
    // Test real-time subscription capability
    const channel = supabase.channel('test-channel');
    
    // Subscribe to a test event
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log('✅ Real-time connection successful');
        channel.unsubscribe();
      } else if (status === 'CHANNEL_ERROR') {
        console.error('❌ Real-time connection failed');
      }
    });
    
    // Give it a moment to connect
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return true;
    
  } catch (err) {
    console.error(`❌ Real-time test error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing application database connection...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  console.log(`🔑 Using anon key: ${SUPABASE_ANON_KEY.substring(0, 20)}...`);
  
  let allSuccess = true;

  // Test basic app connection
  if (!await testAppConnection()) {
    allSuccess = false;
  }

  // Test storage access
  if (!await testStorageAccess()) {
    allSuccess = false;
  }

  // Test real-time connection
  if (!await testRealTimeConnection()) {
    allSuccess = false;
  }

  console.log('\n' + '='.repeat(60));
  if (allSuccess) {
    console.log('🎉 All application connections are working perfectly!');
    console.log('✅ Task 2: Database Connection Issues - RESOLVED');
    console.log('');
    console.log('📋 Connection Summary:');
    console.log('  ✅ Supabase project is active and accessible');
    console.log('  ✅ Database connectivity working');
    console.log('  ✅ Authentication flow functional');
    console.log('  ✅ No "supabaseUrl is required" errors');
    console.log('  ✅ Storage buckets accessible');
    console.log('  ✅ Real-time features working');
  } else {
    console.log('⚠️  Some connection issues remain. Check the errors above.');
  }
  console.log('='.repeat(60));
  
  // Exit cleanly
  process.exit(0);
}

main().catch(console.error);
