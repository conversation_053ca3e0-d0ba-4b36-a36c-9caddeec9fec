// Test script for authentication and user management system
// This script tests user roles, authentication, and auto-completion security

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testAuthTables() {
  console.log('=== Testing Authentication Tables ===');

  try {
    // Test user_roles table
    console.log('\n--- Testing user_roles table ---');
    const { data: userRoles, error: userRolesError } = await supabase
      .from('user_roles')
      .select('*')
      .limit(5);

    if (userRolesError) {
      console.error('❌ Error accessing user_roles table:', userRolesError);
    } else {
      console.log('✅ user_roles table accessible');
      console.log(`Found ${userRoles.length} user roles`);
      if (userRoles.length > 0) {
        console.log('Sample roles:', userRoles.map(r => `${r.user_id}: ${r.role}`));
      }
    }

    // Test user_preferences table
    console.log('\n--- Testing user_preferences table ---');
    const { data: userPrefs, error: userPrefsError } = await supabase
      .from('user_preferences')
      .select('*')
      .limit(5);

    if (userPrefsError) {
      console.error('❌ Error accessing user_preferences table:', userPrefsError);
    } else {
      console.log('✅ user_preferences table accessible');
      console.log(`Found ${userPrefs.length} user preferences`);
    }

    return !userRolesError && !userPrefsError;
  } catch (error) {
    console.error('❌ Unexpected error testing auth tables:', error);
    return false;
  }
}

async function testRoleFunctions() {
  console.log('\n=== Testing Role Functions ===');

  try {
    // Test has_role function
    console.log('\n--- Testing has_role function ---');
    const testUserId = '56c48eae-c9e6-4c58-aaca-6b5f8b47ca77'; // Real teacher user

    const { data: hasTeacherRole, error: hasRoleError } = await supabase
      .rpc('has_role', {
        _user_id: testUserId,
        _role: 'teacher'
      });

    if (hasRoleError) {
      console.error('❌ Error calling has_role function:', hasRoleError);
    } else {
      console.log('✅ has_role function working');
      console.log(`User ${testUserId} has teacher role: ${hasTeacherRole}`);
    }

    // Test assign_role function
    console.log('\n--- Testing assign_role function ---');
    const { data: assignResult, error: assignError } = await supabase
      .rpc('assign_role', {
        _user_id: testUserId,
        _role: 'teacher'
      });

    if (assignError) {
      console.error('❌ Error calling assign_role function:', assignError);
    } else {
      console.log('✅ assign_role function working');
      console.log('Assign role result:', assignResult);
    }

    return !hasRoleError && !assignError;
  } catch (error) {
    console.error('❌ Unexpected error testing role functions:', error);
    return false;
  }
}

async function testGoogleOAuthConfig() {
  console.log('\n=== Testing Google OAuth Configuration ===');

  try {
    // Check if Google OAuth is enabled in Supabase
    // We can't directly test this without authentication, but we can check the auth settings
    console.log('✅ Google OAuth configuration check (manual verification needed)');
    console.log('To verify Google OAuth:');
    console.log('1. Go to Supabase Dashboard > Authentication > Providers');
    console.log('2. Check if Google provider is enabled');
    console.log('3. Verify Client ID and Client Secret are configured');
    console.log('4. Check Site URL and Redirect URLs are correct');

    return true;
  } catch (error) {
    console.error('❌ Error testing Google OAuth config:', error);
    return false;
  }
}

async function testAutoCompletionSecurity() {
  console.log('\n=== Testing Auto-completion Security ===');

  try {
    const testUserId = '56c48eae-c9e6-4c58-aaca-6b5f8b47ca77'; // Real teacher user

    // Test if user has auto-completion enabled
    console.log('\n--- Testing auto-completion preferences ---');
    const { data: prefs, error: prefsError } = await supabase
      .from('user_preferences')
      .select('auto_complete_courses')
      .eq('user_id', testUserId)
      .maybeSingle();

    if (prefsError) {
      console.error('❌ Error checking auto-completion preferences:', prefsError);
    } else {
      console.log('✅ Auto-completion preferences accessible');
      console.log(`User ${testUserId} auto-completion enabled: ${prefs?.auto_complete_courses || false}`);
    }

    // Test role verification for auto-completion
    console.log('\n--- Testing role verification for auto-completion ---');
    const { data: hasRole, error: roleError } = await supabase
      .rpc('has_role', {
        _user_id: testUserId,
        _role: 'teacher'
      });

    if (roleError) {
      console.error('❌ Error verifying teacher role:', roleError);
    } else {
      console.log('✅ Role verification working');
      console.log(`User ${testUserId} is teacher: ${hasRole}`);

      if (hasRole) {
        console.log('✅ Auto-completion security: Teacher role verified');
      } else {
        console.log('⚠️ Auto-completion security: User is not a teacher');
      }
    }

    return !prefsError && !roleError;
  } catch (error) {
    console.error('❌ Unexpected error testing auto-completion security:', error);
    return false;
  }
}

async function testUserRoleAssignment() {
  console.log('\n=== Testing User Role Assignment ===');

  try {
    // Create a test user role assignment (we'll use a real user)
    const testUserId = '68d851be-9186-4cc9-a269-22dc72397299'; // Real user

    console.log('\n--- Testing role assignment ---');
    const { data: assignResult, error: assignError } = await supabase
      .rpc('assign_role', {
        _user_id: testUserId,
        _role: 'student'
      });

    if (assignError) {
      console.error('❌ Error assigning role:', assignError);
    } else {
      console.log('✅ Role assignment function working');
      console.log('Assignment result:', assignResult);
    }

    // Test role retrieval
    console.log('\n--- Testing role retrieval ---');
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', testUserId)
      .maybeSingle();

    if (roleError) {
      console.error('❌ Error retrieving role:', roleError);
    } else {
      console.log('✅ Role retrieval working');
      console.log(`User ${testUserId} role: ${userRole?.role || 'none'}`);
    }

    // Clean up test data
    if (!assignError) {
      await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', testUserId);
      console.log('✅ Test data cleaned up');
    }

    return !assignError && !roleError;
  } catch (error) {
    console.error('❌ Unexpected error testing user role assignment:', error);
    return false;
  }
}

async function runAuthTests() {
  console.log('🔐 Starting Authentication System Tests\n');

  const results = {
    authTables: await testAuthTables(),
    roleFunctions: await testRoleFunctions(),
    googleOAuth: await testGoogleOAuthConfig(),
    autoCompletion: await testAutoCompletionSecurity(),
    roleAssignment: await testUserRoleAssignment()
  };

  console.log('\n=== Test Results Summary ===');
  console.log(`Auth Tables: ${results.authTables ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Role Functions: ${results.roleFunctions ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Google OAuth Config: ${results.googleOAuth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Auto-completion Security: ${results.autoCompletion ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Role Assignment: ${results.roleAssignment ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result === true);

  if (allPassed) {
    console.log('\n🎉 All authentication tests passed! Auth system is working correctly.');
  } else {
    console.log('\n⚠️ Some authentication tests failed. Check the errors above.');
  }

  console.log('\n=== Manual Verification Steps ===');
  console.log('1. Test Google OAuth login in the application');
  console.log('2. Verify role-based access control in admin pages');
  console.log('3. Test auto-completion for teacher users');
  console.log('4. Verify user role assignments work correctly');

  return allPassed;
}

// Run the tests
runAuthTests().catch(console.error);
