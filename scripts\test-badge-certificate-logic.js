/**
 * Test Badge and Certificate Logic
 * 
 * This script tests the new badge and certificate logic to ensure:
 * 1. Badges are awarded when individual modules are completed
 * 2. Course completion requires explicit "Finish Course" action
 * 3. Certificates are only generated when course is explicitly finished
 * 4. Module completion tracking works correctly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

async function testBadgeCertificateLogic() {
  console.log(`${colors.cyan}🧪 Testing Badge and Certificate Logic${colors.reset}\n`);

  try {
    // 1. Get a test user
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();
    if (userError || !users || users.users.length === 0) {
      console.error(`${colors.red}❌ No users found for testing${colors.reset}`);
      return false;
    }

    const testUser = users.users[0];
    console.log(`${colors.blue}👤 Using test user: ${testUser.email}${colors.reset}`);

    // 2. Get a test course with modules
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select(`
        id,
        title,
        modules(id, title, module_number, lessons(id, title))
      `)
      .limit(1);

    if (coursesError || !courses || courses.length === 0) {
      console.error(`${colors.red}❌ No courses found for testing${colors.reset}`);
      return false;
    }

    const testCourse = courses[0];
    console.log(`${colors.blue}📚 Using test course: ${testCourse.title}${colors.reset}`);
    console.log(`${colors.blue}📖 Course has ${testCourse.modules.length} modules${colors.reset}\n`);

    // 3. Test module completion and badge awarding
    console.log(`${colors.yellow}🎯 Testing Module Completion and Badge Logic${colors.reset}`);
    
    for (let i = 0; i < testCourse.modules.length; i++) {
      const module = testCourse.modules[i];
      console.log(`\n${colors.cyan}Testing module ${i + 1}: ${module.title}${colors.reset}`);

      // Mark all lessons in this module as completed
      for (const lesson of module.lessons) {
        const { error: lessonError } = await supabase
          .from('user_lesson_progress')
          .upsert({
            user_id: testUser.id,
            lesson_id: lesson.id,
            is_completed: true,
            completed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'user_id,lesson_id'
          });

        if (lessonError) {
          console.error(`${colors.red}❌ Error marking lesson as completed: ${lessonError.message}${colors.reset}`);
        } else {
          console.log(`${colors.green}✅ Lesson "${lesson.title}" marked as completed${colors.reset}`);
        }
      }

      // Check if module is now marked as completed
      const { data: moduleProgress, error: moduleError } = await supabase
        .from('user_module_progress')
        .select('is_completed')
        .eq('user_id', testUser.id)
        .eq('module_id', module.id)
        .single();

      if (moduleError) {
        console.log(`${colors.yellow}⚠️ Module progress not found, checking if it gets created automatically${colors.reset}`);
      } else if (moduleProgress.is_completed) {
        console.log(`${colors.green}✅ Module marked as completed${colors.reset}`);
      } else {
        console.log(`${colors.red}❌ Module not marked as completed${colors.reset}`);
      }

      // Check if badge was awarded for this module
      const { data: badges, error: badgeError } = await supabase
        .from('user_achievements')
        .select(`
          id,
          achievement:achievements(name, description)
        `)
        .eq('user_id', testUser.id)
        .eq('module_id', module.id);

      if (badgeError) {
        console.error(`${colors.red}❌ Error checking badges: ${badgeError.message}${colors.reset}`);
      } else if (badges && badges.length > 0) {
        console.log(`${colors.green}✅ Badge awarded: ${badges[0].achievement.name}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ No badge found for this module${colors.reset}`);
      }
    }

    // 4. Check course enrollment status after all modules completed
    console.log(`\n${colors.yellow}🎯 Testing Course Status After All Modules Completed${colors.reset}`);
    
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_course_enrollment')
      .select('status')
      .eq('user_id', testUser.id)
      .eq('course_id', testCourse.id)
      .single();

    if (enrollmentError) {
      console.log(`${colors.yellow}⚠️ No enrollment record found${colors.reset}`);
    } else {
      console.log(`${colors.blue}📊 Course enrollment status: ${enrollment.status}${colors.reset}`);
      
      if (enrollment.status === 'modules_completed') {
        console.log(`${colors.green}✅ Correct! Course status is 'modules_completed' (not 'completed')${colors.reset}`);
      } else if (enrollment.status === 'completed') {
        console.log(`${colors.red}❌ Wrong! Course should be 'modules_completed', not 'completed'${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ Unexpected status: ${enrollment.status}${colors.reset}`);
      }
    }

    // 5. Test explicit course completion
    console.log(`\n${colors.yellow}🎯 Testing Explicit Course Completion${colors.reset}`);
    
    const { data: completionResult, error: completionError } = await supabase.rpc('complete_course', {
      p_user_id: testUser.id,
      p_course_id: testCourse.id
    });

    if (completionError) {
      console.error(`${colors.red}❌ Error completing course: ${completionError.message}${colors.reset}`);
    } else if (completionResult) {
      console.log(`${colors.green}✅ Course completed successfully${colors.reset}`);
      
      // Check if certificate is now available
      const { data: certificate, error: certError } = await supabase
        .from('user_course_enrollment')
        .select('status, completed_at')
        .eq('user_id', testUser.id)
        .eq('course_id', testCourse.id)
        .eq('status', 'completed')
        .single();

      if (certError) {
        console.error(`${colors.red}❌ Certificate not found: ${certError.message}${colors.reset}`);
      } else {
        console.log(`${colors.green}✅ Certificate available! Completed at: ${certificate.completed_at}${colors.reset}`);
      }

      // Check if course badge was awarded
      const { data: courseBadges, error: courseBadgeError } = await supabase
        .from('user_achievements')
        .select(`
          id,
          achievement:achievements(name, description)
        `)
        .eq('user_id', testUser.id)
        .eq('course_id', testCourse.id);

      if (courseBadgeError) {
        console.error(`${colors.red}❌ Error checking course badges: ${courseBadgeError.message}${colors.reset}`);
      } else if (courseBadges && courseBadges.length > 0) {
        console.log(`${colors.green}✅ Course badge awarded: ${courseBadges[0].achievement.name}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ No course badge found${colors.reset}`);
      }
    } else {
      console.log(`${colors.red}❌ Course completion failed${colors.reset}`);
    }

    console.log(`\n${colors.green}🎉 Badge and Certificate Logic Test Completed${colors.reset}`);
    return true;

  } catch (error) {
    console.error(`${colors.red}❌ Unexpected error during testing: ${error.message}${colors.reset}`);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testBadgeCertificateLogic()
    .then(success => {
      if (success) {
        console.log(`\n${colors.green}✅ All tests completed successfully${colors.reset}`);
        process.exit(0);
      } else {
        console.log(`\n${colors.red}❌ Some tests failed${colors.reset}`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`\n${colors.red}❌ Test execution failed: ${error.message}${colors.reset}`);
      process.exit(1);
    });
}

module.exports = { testBadgeCertificateLogic };
