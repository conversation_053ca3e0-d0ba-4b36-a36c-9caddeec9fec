/**
 * Test Course Completion System
 * This script tests the course completion functionality to identify issues
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create both clients
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testRPCFunction() {
  console.log('🔧 Testing complete_course RPC function...');
  
  try {
    // Test if the function exists by calling it with dummy data
    const testUserId = '00000000-0000-0000-0000-000000000000';
    const testCourseId = '11111111-1111-1111-1111-111111111111';
    
    const { data, error } = await supabaseService.rpc('complete_course', {
      p_user_id: testUserId,
      p_course_id: testCourseId
    });
    
    if (error) {
      if (error.message.includes('function complete_course') && error.message.includes('does not exist')) {
        console.error('❌ complete_course RPC function does not exist');
        return false;
      } else if (error.message.includes('violates foreign key constraint')) {
        console.log('✅ complete_course RPC function exists (foreign key error is expected with dummy data)');
        return true;
      } else {
        console.error(`❌ RPC function error: ${error.message}`);
        return false;
      }
    }
    
    console.log('✅ complete_course RPC function working:', data);
    return true;
    
  } catch (err) {
    console.error(`❌ RPC test error: ${err.message}`);
    return false;
  }
}

async function testDatabaseSchema() {
  console.log('\n🔧 Testing database schema for course completion...');
  
  try {
    // Check if user_course_enrollment table exists and has correct columns
    const { data: enrollmentColumns, error: enrollmentError } = await supabaseService
      .from('user_course_enrollment')
      .select('*')
      .limit(1);
    
    if (enrollmentError) {
      console.error(`❌ user_course_enrollment table issue: ${enrollmentError.message}`);
      return false;
    }
    
    console.log('✅ user_course_enrollment table exists');
    
    // Check if completed_at column exists by trying to select it
    const { data: completedAtTest, error: completedAtError } = await supabaseService
      .from('user_course_enrollment')
      .select('completed_at')
      .limit(1);
    
    if (completedAtError) {
      console.error(`❌ completed_at column issue: ${completedAtError.message}`);
      return false;
    }
    
    console.log('✅ completed_at column exists');
    
    // Check user_course_progress table
    const { data: progressTest, error: progressError } = await supabaseService
      .from('user_course_progress')
      .select('*')
      .limit(1);
    
    if (progressError) {
      console.error(`❌ user_course_progress table issue: ${progressError.message}`);
      return false;
    }
    
    console.log('✅ user_course_progress table exists');
    
    return true;
    
  } catch (err) {
    console.error(`❌ Schema test error: ${err.message}`);
    return false;
  }
}

async function testCourseData() {
  console.log('\n🔧 Testing course data availability...');
  
  try {
    // Check if there are courses available
    const { data: courses, error: coursesError } = await supabaseAnon
      .from('courses')
      .select('id, title')
      .limit(5);
    
    if (coursesError) {
      console.error(`❌ Courses query error: ${coursesError.message}`);
      return false;
    }
    
    if (!courses || courses.length === 0) {
      console.error('❌ No courses found in database');
      return false;
    }
    
    console.log(`✅ Found ${courses.length} courses:`, courses.map(c => c.title));
    
    // Check if there are modules for the courses
    const { data: modules, error: modulesError } = await supabaseAnon
      .from('modules')
      .select('id, title, course_id')
      .in('course_id', courses.map(c => c.id))
      .limit(10);
    
    if (modulesError) {
      console.error(`❌ Modules query error: ${modulesError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${modules?.length || 0} modules for these courses`);
    
    return { courses, modules };
    
  } catch (err) {
    console.error(`❌ Course data test error: ${err.message}`);
    return false;
  }
}

async function testFinishCourseButton() {
  console.log('\n🔧 Testing FinishCourseButton logic...');
  
  try {
    // Test the module completion check logic
    const { data: moduleProgress, error: progressError } = await supabaseAnon
      .from('user_module_progress')
      .select('*')
      .limit(5);
    
    if (progressError) {
      console.error(`❌ Module progress query error: ${progressError.message}`);
      return false;
    }
    
    console.log(`✅ user_module_progress table accessible, found ${moduleProgress?.length || 0} records`);
    
    // Test enrollment status check
    const { data: enrollments, error: enrollmentError } = await supabaseAnon
      .from('user_course_enrollment')
      .select('*')
      .limit(5);
    
    if (enrollmentError) {
      console.error(`❌ Enrollment query error: ${enrollmentError.message}`);
      return false;
    }
    
    console.log(`✅ user_course_enrollment table accessible, found ${enrollments?.length || 0} records`);
    
    return true;
    
  } catch (err) {
    console.error(`❌ FinishCourseButton test error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing Course Completion System...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;
  const issues = [];

  // Test 1: RPC Function
  if (!await testRPCFunction()) {
    allSuccess = false;
    issues.push('complete_course RPC function missing or broken');
  }

  // Test 2: Database Schema
  if (!await testDatabaseSchema()) {
    allSuccess = false;
    issues.push('Database schema issues (missing tables/columns)');
  }

  // Test 3: Course Data
  const courseData = await testCourseData();
  if (!courseData) {
    allSuccess = false;
    issues.push('No course data available or query issues');
  }

  // Test 4: FinishCourseButton Logic
  if (!await testFinishCourseButton()) {
    allSuccess = false;
    issues.push('FinishCourseButton component logic issues');
  }

  console.log('\n' + '='.repeat(60));
  if (allSuccess) {
    console.log('🎉 Course completion system appears to be working!');
    console.log('✅ All tests passed');
    console.log('');
    console.log('📋 System Status:');
    console.log('  ✅ complete_course RPC function exists');
    console.log('  ✅ Database schema is correct');
    console.log('  ✅ Course data is available');
    console.log('  ✅ Component logic should work');
  } else {
    console.log('⚠️  Course completion system has issues:');
    issues.forEach(issue => console.log(`  ❌ ${issue}`));
    console.log('');
    console.log('🔧 These issues need to be fixed for course completion to work properly.');
  }
  console.log('='.repeat(60));
  
  return allSuccess;
}

main().catch(console.error);
