// Simple test for image upload functionality
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testBuckets() {
  console.log('Testing storage buckets...');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error:', error);
      return false;
    }
    
    console.log('Found buckets:', buckets.map(b => b.name));
    return true;
  } catch (error) {
    console.error('Exception:', error);
    return false;
  }
}

async function testUpload() {
  console.log('Testing image upload...');
  
  try {
    // Create a minimal test file
    const testData = 'test image data';
    const testFile = new Blob([testData], { type: 'text/plain' });
    const fileName = `test-${Date.now()}.txt`;
    
    const { data, error } = await supabase.storage
      .from('course-images')
      .upload(fileName, testFile);
    
    if (error) {
      console.error('Upload error:', error);
      return false;
    }
    
    console.log('Upload successful:', data.path);
    
    // Clean up
    await supabase.storage.from('course-images').remove([fileName]);
    console.log('Cleanup successful');
    
    return true;
  } catch (error) {
    console.error('Exception:', error);
    return false;
  }
}

async function runTests() {
  console.log('=== Simple Storage Test ===\n');
  
  const bucketsOk = await testBuckets();
  console.log('Buckets test:', bucketsOk ? 'PASS' : 'FAIL');
  
  const uploadOk = await testUpload();
  console.log('Upload test:', uploadOk ? 'PASS' : 'FAIL');
  
  console.log('\nOverall:', (bucketsOk && uploadOk) ? 'SUCCESS' : 'FAILED');
}

runTests().catch(console.error);
