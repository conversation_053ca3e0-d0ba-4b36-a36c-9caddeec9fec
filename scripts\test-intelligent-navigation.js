// Test script to verify intelligent navigation functionality
// Run with: node scripts/test-intelligent-navigation.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Mock implementation of the new navigation function for testing
async function findNextContentItemInModule(currentLessonSlug, userId) {
  try {
    console.log(`${colors.blue}[TEST] Finding next content item for lesson: ${currentLessonSlug}${colors.reset}`);

    // Get the current lesson
    const { data: currentLesson, error: lessonError } = await supabase
      .from('lessons')
      .select('id, slug, module_id, lesson_number, created_at')
      .eq('slug', currentLessonSlug)
      .single();

    if (lessonError || !currentLesson) {
      console.log(`${colors.red}[TEST] Failed to find current lesson: ${lessonError?.message}${colors.reset}`);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    console.log(`${colors.cyan}[TEST] Current lesson: ${currentLesson.slug} (lesson_number: ${currentLesson.lesson_number})${colors.reset}`);

    // Get the current module info
    const { data: currentModule, error: moduleError } = await supabase
      .from('modules')
      .select('course_id, module_number, title')
      .eq('id', currentLesson.module_id)
      .single();

    if (moduleError || !currentModule) {
      console.log(`${colors.red}[TEST] Failed to find current module: ${moduleError?.message}${colors.reset}`);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    console.log(`${colors.cyan}[TEST] Current module: ${currentModule.title} (module_number: ${currentModule.module_number})${colors.reset}`);

    // STEP 1: Check for next lesson in the same module
    const { data: nextLessonsInModule, error: nextLessonError } = await supabase
      .from('lessons')
      .select('slug, lesson_number, title')
      .eq('module_id', currentLesson.module_id)
      .gt('lesson_number', currentLesson.lesson_number)
      .order('lesson_number', { ascending: true })
      .limit(1);

    if (!nextLessonError && nextLessonsInModule && nextLessonsInModule.length > 0) {
      const nextLesson = nextLessonsInModule[0];
      console.log(`${colors.green}[TEST] ✅ Next item is lesson in same module: ${nextLesson.slug}${colors.reset}`);
      return {
        nextItemType: 'lesson',
        nextItemSlug: nextLesson.slug,
        isLastItem: false,
        navigationUrl: `/course/${currentModule.course_id}/lesson/${nextLesson.slug}`
      };
    }

    // STEP 2: Check for post-test in current module
    const { data: postTest, error: postTestError } = await supabase
      .from('module_tests')
      .select('id, title')
      .eq('module_id', currentLesson.module_id)
      .eq('type', 'post_test')
      .maybeSingle();

    if (!postTestError && postTest) {
      // Check if post-test is completed
      const { data: postTestResponse, error: responseError } = await supabase
        .from('module_test_responses')
        .select('id')
        .eq('test_id', postTest.id)
        .eq('user_id', userId)
        .maybeSingle();

      if (!responseError && !postTestResponse) {
        console.log(`${colors.green}[TEST] ✅ Next item is post-test for current module: ${postTest.title}${colors.reset}`);
        return {
          nextItemType: 'post_test',
          nextItemSlug: currentLesson.module_id,
          nextModuleId: currentLesson.module_id,
          isLastItem: false,
          navigationUrl: `/course/${currentModule.course_id}/module/${currentLesson.module_id}?type=post_test`
        };
      } else {
        console.log(`${colors.yellow}[TEST] Post-test exists but already completed${colors.reset}`);
      }
    }

    // STEP 3: Look for next module
    const { data: nextModule, error: nextModuleError } = await supabase
      .from('modules')
      .select('id, module_number, title')
      .eq('course_id', currentModule.course_id)
      .gt('module_number', currentModule.module_number)
      .order('module_number', { ascending: true })
      .limit(1)
      .maybeSingle();

    if (nextModuleError || !nextModule) {
      console.log(`${colors.yellow}[TEST] ⚠️ No next module found - this is the last item${colors.reset}`);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    console.log(`${colors.cyan}[TEST] Found next module: ${nextModule.title}${colors.reset}`);

    // STEP 4: Check for pre-test in next module
    const { data: preTest, error: preTestError } = await supabase
      .from('module_tests')
      .select('id, title')
      .eq('module_id', nextModule.id)
      .eq('type', 'pre_test')
      .maybeSingle();

    if (!preTestError && preTest) {
      // Check if pre-test is completed
      const { data: preTestResponse, error: preResponseError } = await supabase
        .from('module_test_responses')
        .select('id')
        .eq('test_id', preTest.id)
        .eq('user_id', userId)
        .maybeSingle();

      if (!preResponseError && !preTestResponse) {
        console.log(`${colors.green}[TEST] ✅ Next item is pre-test for next module: ${preTest.title}${colors.reset}`);
        return {
          nextItemType: 'pre_test',
          nextItemSlug: nextModule.id,
          nextModuleId: nextModule.id,
          isLastItem: false,
          navigationUrl: `/course/${currentModule.course_id}/module/${nextModule.id}?type=pre_test`
        };
      }
    }

    // STEP 5: Get first lesson of next module
    const { data: firstLessonInNextModule, error: firstLessonError } = await supabase
      .from('lessons')
      .select('slug, lesson_number, title')
      .eq('module_id', nextModule.id)
      .order('lesson_number', { ascending: true })
      .limit(1)
      .maybeSingle();

    if (firstLessonError || !firstLessonInNextModule) {
      console.log(`${colors.yellow}[TEST] ⚠️ No lessons found in next module${colors.reset}`);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    console.log(`${colors.green}[TEST] ✅ Next item is first lesson in next module: ${firstLessonInNextModule.slug}${colors.reset}`);
    return {
      nextItemType: 'lesson',
      nextItemSlug: firstLessonInNextModule.slug,
      isLastItem: false,
      navigationUrl: `/course/${currentModule.course_id}/lesson/${firstLessonInNextModule.slug}`
    };

  } catch (error) {
    console.error(`${colors.red}[TEST] Error in navigation logic:${colors.reset}`, error);
    return { nextItemType: null, nextItemSlug: null, isLastItem: true };
  }
}

async function testIntelligentNavigation() {
  console.log(`${colors.white}🧪 Testing Intelligent Navigation Functionality${colors.reset}\n`);

  try {
    // 1. Get a course with modules and lessons
    console.log(`${colors.yellow}1. Fetching course data...${colors.reset}`);
    
    const { data: courses, error: courseError } = await supabase
      .from('courses')
      .select(`
        id, title,
        modules:modules(
          id, title, module_number,
          lessons:lessons(id, slug, title, lesson_number)
        )
      `)
      .limit(1);

    if (courseError || !courses || courses.length === 0) {
      console.log(`${colors.red}❌ No courses found or error: ${courseError?.message}${colors.reset}`);
      return;
    }

    const course = courses[0];
    console.log(`${colors.green}✅ Found course: "${course.title}"${colors.reset}`);

    if (!course.modules || course.modules.length === 0) {
      console.log(`${colors.red}❌ No modules found in course${colors.reset}`);
      return;
    }

    // 2. Test navigation for different scenarios
    console.log(`\n${colors.yellow}2. Testing navigation scenarios...${colors.reset}`);

    // Use a dummy user ID for testing
    const testUserId = 'test-user-id';

    let testCount = 0;
    let passedTests = 0;

    for (const module of course.modules) {
      if (!module.lessons || module.lessons.length === 0) continue;

      const sortedLessons = module.lessons.sort((a, b) => (a.lesson_number || 0) - (b.lesson_number || 0));

      for (let i = 0; i < sortedLessons.length; i++) {
        const lesson = sortedLessons[i];
        testCount++;

        console.log(`\n${colors.cyan}Test ${testCount}: Navigation from "${lesson.title}" (${lesson.slug})${colors.reset}`);

        const result = await findNextContentItemInModule(lesson.slug, testUserId);

        if (result.nextItemType) {
          console.log(`${colors.green}✅ Test passed - Found next item: ${result.nextItemType} (${result.nextItemSlug})${colors.reset}`);
          console.log(`${colors.blue}   Navigation URL: ${result.navigationUrl}${colors.reset}`);
          passedTests++;
        } else if (result.isLastItem) {
          console.log(`${colors.green}✅ Test passed - Correctly identified as last item${colors.reset}`);
          passedTests++;
        } else {
          console.log(`${colors.red}❌ Test failed - No next item found but not marked as last${colors.reset}`);
        }

        // Add a small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 3. Summary
    console.log(`\n${colors.white}📊 Test Summary:${colors.reset}`);
    console.log(`${colors.cyan}Total tests: ${testCount}${colors.reset}`);
    console.log(`${colors.green}Passed: ${passedTests}${colors.reset}`);
    console.log(`${colors.red}Failed: ${testCount - passedTests}${colors.reset}`);

    if (passedTests === testCount) {
      console.log(`\n${colors.green}🎉 All tests passed! Intelligent navigation is working correctly.${colors.reset}`);
    } else {
      console.log(`\n${colors.yellow}⚠️ Some tests failed. Please review the navigation logic.${colors.reset}`);
    }

  } catch (error) {
    console.error(`${colors.red}❌ Unexpected error during testing:${colors.reset}`, error);
  }
}

// Run the test
testIntelligentNavigation();
