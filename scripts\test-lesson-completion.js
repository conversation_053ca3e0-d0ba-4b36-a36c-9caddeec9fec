// Test script for lesson completion functionality
// Run with: node scripts/test-lesson-completion.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function testLessonCompletion() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Testing lesson completion functionality...\n');

    // 1. Get a test lesson and user
    console.log('1. Getting test data...');
    const { data: testData, error: testDataError } = await client
      .from('lessons')
      .select(`
        id,
        title,
        module_id,
        modules!inner(
          id,
          title
        )
      `)
      .limit(1);

    if (testDataError || !testData || testData.length === 0) {
      console.error('❌ No lessons found for testing:', testDataError);
      return;
    }

    const testLesson = testData[0];
    console.log(`✅ Found test lesson: "${testLesson.title}" (ID: ${testLesson.id})`);

    // Get a test user (teacher)
    const { data: testUser, error: userError } = await client
      .from('user_roles')
      .select('user_id')
      .eq('role', 'teacher')
      .limit(1);

    if (userError || !testUser || testUser.length === 0) {
      console.error('❌ No teacher found for testing:', userError);
      return;
    }

    const userId = testUser[0].user_id;
    console.log(`✅ Using test user: ${userId}\n`);

    // 2. Clear any existing progress for this lesson
    console.log('2. Clearing existing progress...');
    const { error: clearError } = await client
      .from('user_lesson_progress')
      .delete()
      .eq('user_id', userId)
      .eq('lesson_id', testLesson.id);

    if (clearError) {
      console.log('⚠️ Could not clear existing progress (may not exist):', clearError.message);
    } else {
      console.log('✅ Cleared existing progress');
    }

    // 3. Test the RPC function
    console.log('\n3. Testing mark_lesson_completed RPC function...');
    const { data: rpcResult, error: rpcError } = await client.rpc('mark_lesson_completed', {
      p_user_id: userId,
      p_lesson_id: testLesson.id
    });

    if (rpcError) {
      console.error('❌ RPC function failed:', rpcError);

      // 4. Test fallback method
      console.log('\n4. Testing fallback method...');
      const now = new Date().toISOString();
      const { error: fallbackError } = await client
        .from('user_lesson_progress')
        .upsert({
          user_id: userId,
          lesson_id: testLesson.id,
          is_completed: true,
          completed_at: now,
          updated_at: now
        }, {
          onConflict: 'user_id,lesson_id'
        });

      if (fallbackError) {
        console.error('❌ Fallback method also failed:', fallbackError);
        return;
      } else {
        console.log('✅ Fallback method succeeded');
      }
    } else {
      console.log('✅ RPC function succeeded:', rpcResult);
    }

    // Wait a moment for the database to process
    await new Promise(resolve => setTimeout(resolve, 500));

    // 5. Verify the lesson was marked as completed
    console.log('\n5. Verifying lesson completion...');
    const { data: progressData, error: progressError } = await client
      .from('user_lesson_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('lesson_id', testLesson.id)
      .maybeSingle();

    if (progressError) {
      console.error('❌ Could not verify lesson completion:', progressError);
      return;
    }

    if (progressData && progressData.is_completed) {
      console.log('✅ Lesson successfully marked as completed');
      console.log(`   Completed at: ${progressData.completed_at}`);
    } else if (progressData) {
      console.log('❌ Lesson was not marked as completed');
    } else {
      console.log('❌ No lesson progress record found');
    }

    // 6. Check module completion
    console.log('\n6. Checking module completion...');
    const { data: moduleProgress, error: moduleError } = await client
      .from('user_module_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('module_id', testLesson.module_id)
      .maybeSingle();

    if (moduleError) {
      console.log('⚠️ No module progress found (this is normal if not all lessons are completed)');
    } else if (moduleProgress) {
      console.log(`✅ Module progress found: ${moduleProgress.is_completed ? 'Completed' : 'In Progress'}`);
    } else {
      console.log('⚠️ No module progress record found');
    }

    console.log('\n🎉 Lesson completion test completed successfully!');

  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

// Run the test
testLessonCompletion();
