/**
 * Test Markdown Video Functionality Script
 * 
 * This script tests the video functionality in the markdown editor by:
 * 1. Creating a lesson with markdown content that includes YouTube videos
 * 2. Testing the TipTap YouTube extension
 * 3. Verifying video display in lesson content
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

/**
 * Create a test lesson with markdown content including YouTube videos
 */
async function createMarkdownVideoLesson() {
  console.log(`${colors.cyan}${colors.bold}🎥 Creating Markdown Video Lesson${colors.reset}\n`);
  
  try {
    // Get a module to add the lesson to
    const { data: modules, error: moduleError } = await supabase
      .from('modules')
      .select('id, title')
      .limit(1);
    
    if (moduleError || !modules?.length) {
      console.error(`${colors.red}❌ No modules found: ${moduleError?.message}${colors.reset}`);
      return false;
    }
    
    const module = modules[0];
    console.log(`${colors.blue}📚 Using module: ${module.title}${colors.reset}`);
    
    // Create markdown content with embedded YouTube video
    // This simulates what the TipTap YouTube extension would generate
    const markdownContent = `# Markdown Video Integration Test

This lesson demonstrates how videos are embedded directly in markdown content using the TipTap editor.

## Introduction

The TipTap editor allows users to embed YouTube videos directly into the content using the YouTube button in the toolbar.

## Video Content

<div data-youtube-video>
  <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
</div>

## How It Works

1. **Click the YouTube button** in the markdown editor toolbar
2. **Paste the YouTube URL** in the dialog
3. **The video is embedded** directly in the content
4. **No separate video fields** needed in the form

## Benefits

- ✅ Videos are part of the content flow
- ✅ Multiple videos per lesson supported
- ✅ Standard markdown processing
- ✅ No complex JSON structure needed

## Another Video Example

<div data-youtube-video>
  <iframe src="https://www.youtube.com/embed/oHg5SJYRHA0" frameborder="0" allowfullscreen></iframe>
</div>

## Summary

This approach integrates videos seamlessly into the lesson content using the markdown editor's built-in functionality.`;
    
    // Create the lesson with plain markdown content
    const lessonData = {
      module_id: module.id,
      title: 'Markdown Video Integration Test',
      slug: 'markdown-video-integration-test',
      duration: '12:00',
      type: 'lesson',
      content: markdownContent,
      lesson_number: 998, // High number to put it at the end
      requirement: null
    };
    
    console.log(`${colors.yellow}📝 Creating lesson with markdown video content...${colors.reset}`);
    
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .insert([lessonData])
      .select()
      .single();
    
    if (lessonError) {
      console.error(`${colors.red}❌ Error creating lesson: ${lessonError.message}${colors.reset}`);
      return false;
    }
    
    console.log(`${colors.green}✅ Successfully created markdown video lesson!${colors.reset}`);
    console.log(`   Lesson ID: ${lesson.id}`);
    console.log(`   Lesson Slug: ${lesson.slug}`);
    console.log(`   Module: ${module.title}`);
    console.log(`   Content Type: Plain Markdown (no JSON)`);
    
    return lesson;
  } catch (error) {
    console.error(`${colors.red}❌ Error creating test lesson: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test current lesson content structure
 */
async function testCurrentLessons() {
  console.log(`${colors.cyan}${colors.bold}🔍 Testing Current Lesson Structure${colors.reset}\n`);
  
  try {
    const { data: lessons, error } = await supabase
      .from('lessons')
      .select('id, title, content, type')
      .limit(5);
    
    if (error) {
      console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
      return;
    }
    
    console.log(`${colors.green}✅ Found ${lessons?.length || 0} lessons${colors.reset}\n`);
    
    lessons?.forEach((lesson, index) => {
      console.log(`${colors.bold}${index + 1}. ${lesson.title}${colors.reset}`);
      console.log(`   Type: ${lesson.type}`);
      
      if (lesson.content) {
        const isJson = lesson.content.startsWith('{');
        console.log(`   Content Format: ${isJson ? colors.yellow + 'JSON (Legacy)' : colors.green + 'Markdown'}${colors.reset}`);
        
        if (isJson) {
          try {
            const parsed = JSON.parse(lesson.content);
            console.log(`   Has videoUrl: ${parsed.videoUrl ? colors.yellow + 'Yes (Legacy)' : colors.green + 'No'}${colors.reset}`);
          } catch (e) {
            console.log(`   ${colors.red}JSON Parse Error${colors.reset}`);
          }
        } else {
          // Check for YouTube embeds in markdown
          const hasYouTubeEmbed = lesson.content.includes('youtube.com/embed') || 
                                  lesson.content.includes('data-youtube-video');
          console.log(`   Has YouTube Embed: ${hasYouTubeEmbed ? colors.green + 'Yes' : 'No'}${colors.reset}`);
        }
        
        console.log(`   Content Length: ${lesson.content.length} characters`);
      } else {
        console.log(`   Content: ${colors.yellow}Empty${colors.reset}`);
      }
      
      console.log('');
    });
  } catch (error) {
    console.error(`${colors.red}❌ Error testing lessons: ${error.message}${colors.reset}`);
  }
}

/**
 * Show instructions for using markdown video functionality
 */
function showMarkdownVideoInstructions() {
  console.log(`${colors.cyan}${colors.bold}📋 How to Add Videos Using Markdown Editor${colors.reset}\n`);
  
  console.log(`${colors.bold}Step-by-Step Instructions:${colors.reset}`);
  console.log(`1. ${colors.blue}Open Lesson Editor${colors.reset}`);
  console.log(`   • Go to Admin Panel → Lessons`);
  console.log(`   • Click "Edit" or "Create New Lesson"`);
  console.log('');
  
  console.log(`2. ${colors.blue}Go to Content Tab${colors.reset}`);
  console.log(`   • Click on "Content" tab in the lesson editor`);
  console.log(`   • You'll see the markdown editor with toolbar`);
  console.log('');
  
  console.log(`3. ${colors.blue}Add YouTube Video${colors.reset}`);
  console.log(`   • Click the YouTube icon (📺) in the toolbar`);
  console.log(`   • Paste your YouTube URL in the dialog`);
  console.log(`   • Click "Insert Video"`);
  console.log(`   • Video will be embedded in the content`);
  console.log('');
  
  console.log(`4. ${colors.blue}Save Lesson${colors.reset}`);
  console.log(`   • Click "Update Lesson" or "Create Lesson"`);
  console.log(`   • Video is now part of the markdown content`);
  console.log('');
  
  console.log(`${colors.bold}Benefits of This Approach:${colors.reset}`);
  console.log(`• ${colors.green}✅ Videos integrated in content flow${colors.reset}`);
  console.log(`• ${colors.green}✅ Multiple videos per lesson${colors.reset}`);
  console.log(`• ${colors.green}✅ Standard markdown processing${colors.reset}`);
  console.log(`• ${colors.green}✅ No complex form fields${colors.reset}`);
  console.log(`• ${colors.green}✅ Better content organization${colors.reset}`);
  console.log('');
  
  console.log(`${colors.bold}Supported Video Platforms:${colors.reset}`);
  console.log(`• ${colors.green}✅ YouTube${colors.reset} (via TipTap YouTube extension)`);
  console.log(`• ${colors.yellow}⚠️ Other platforms${colors.reset} (can be added as HTML/iframe)`);
  console.log('');
}

/**
 * Clean up test lessons
 */
async function cleanupTestLessons() {
  console.log(`${colors.yellow}🧹 Cleaning up test lessons...${colors.reset}`);
  
  try {
    const { data: deletedLessons, error } = await supabase
      .from('lessons')
      .delete()
      .like('title', '%Markdown Video Integration Test%')
      .select();
    
    if (error) {
      console.error(`${colors.red}❌ Error deleting test lessons: ${error.message}${colors.reset}`);
      return;
    }
    
    console.log(`${colors.green}✅ Deleted ${deletedLessons?.length || 0} test lessons${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}❌ Error during cleanup: ${error.message}${colors.reset}`);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const action = process.argv[2];
    
    if (action === 'clean' || action === 'cleanup') {
      await cleanupTestLessons();
    } else if (action === 'test') {
      await testCurrentLessons();
    } else {
      console.log(`${colors.cyan}${colors.bold}🎬 Markdown Video Functionality Test${colors.reset}\n`);
      
      // Test current lessons
      await testCurrentLessons();
      
      // Create test lesson
      const lesson = await createMarkdownVideoLesson();
      
      // Show instructions
      showMarkdownVideoInstructions();
      
      if (lesson) {
        console.log(`${colors.cyan}${colors.bold}🎉 Test completed successfully!${colors.reset}`);
        console.log(`\n${colors.blue}Visit the lesson:${colors.reset} /lesson/${lesson.slug}`);
        console.log(`${colors.blue}Full URL:${colors.reset} http://localhost:5173/lesson/${lesson.slug}`);
        console.log(`\n${colors.yellow}💡 Run 'node scripts/test-markdown-video-functionality.js clean' to remove test lessons${colors.reset}`);
      }
    }
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main();
