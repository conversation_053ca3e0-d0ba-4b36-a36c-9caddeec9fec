// Test script to verify the module test page is working
// Run with: node scripts/test-module-test-page.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testModuleTestPage() {
  console.log('🧪 Testing Module Test Page functionality...\n');
  
  try {
    // 1. Test module query (the one that was failing)
    console.log('1. Testing module query...');
    
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, title, course_id, module_number')
      .limit(1);
    
    if (modulesError || !modules || modules.length === 0) {
      console.log('❌ No modules found for testing');
      return;
    }
    
    const module = modules[0];
    console.log(`✅ Found module: "${module.title}" (ID: ${module.id})`);
    
    // Test the fixed query that the ModuleTestPage uses
    const { data: singleModule, error: singleError } = await supabase
      .from('modules')
      .select('id, title, module_number')
      .eq('id', module.id)
      .single();
    
    if (singleError) {
      console.log('❌ Module query failed:', singleError.message);
      return;
    } else {
      console.log('✅ Module query successful');
    }
    
    // 2. Test module tests queries
    console.log('\n2. Testing module tests queries...');
    
    // Check for pre-test
    const { data: preTest, error: preTestError } = await supabase
      .from('module_tests')
      .select('*')
      .eq('module_id', module.id)
      .eq('type', 'pre_test')
      .single();
    
    if (preTestError && preTestError.code !== 'PGRST116') {
      console.log('⚠️ Error checking pre-test:', preTestError.message);
    } else if (preTest) {
      console.log(`✅ Found pre-test: "${preTest.title}"`);
    } else {
      console.log('ℹ️ No pre-test found for this module');
    }
    
    // Check for post-test
    const { data: postTest, error: postTestError } = await supabase
      .from('module_tests')
      .select('*')
      .eq('module_id', module.id)
      .eq('type', 'post_test')
      .single();
    
    if (postTestError && postTestError.code !== 'PGRST116') {
      console.log('⚠️ Error checking post-test:', postTestError.message);
    } else if (postTest) {
      console.log(`✅ Found post-test: "${postTest.title}"`);
    } else {
      console.log('ℹ️ No post-test found for this module');
    }
    
    // 3. Generate test URLs
    console.log('\n3. Test URLs:');
    const baseUrl = 'http://localhost:5173';
    const moduleTestUrl = `${baseUrl}/course/${module.course_id}/module/${module.id}`;
    const courseUrl = `${baseUrl}/course/${module.course_id}`;
    
    console.log(`📍 Module test page: ${moduleTestUrl}`);
    console.log(`📍 Course page: ${courseUrl}`);
    
    // 4. Summary
    console.log('\n4. Summary:');
    console.log('✅ Module query fixed (removed non-existent description column)');
    console.log('✅ Module test queries working');
    console.log('✅ URLs generated correctly');
    
    if (preTest || postTest) {
      console.log('\n🎉 Module test page should now work correctly!');
      console.log('\n📝 To test:');
      console.log('1. Go to the course page');
      console.log('2. Expand a module');
      console.log('3. Click on a pre-test or post-test button');
      console.log('4. Verify you see the module test page instead of "Module Not Found"');
    } else {
      console.log('\n⚠️ No tests found for this module');
      console.log('The page should still load and show "No Tests Available" message');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

// Run the test
testModuleTestPage();
