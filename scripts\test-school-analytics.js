/**
 * Test script to verify the school-grouped demographic analytics functionality
 * This script tests the new API functions and data structures
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test the school-grouped analytics function
 */
async function testSchoolAnalytics() {
  console.log('🧪 Testing School-Grouped Demographic Analytics...\n');

  try {
    // Test 1: Get all demographic responses
    console.log('📊 Test 1: Fetching all demographic responses...');
    const { data: responses, error: responsesError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .order('completed_at', { ascending: false });

    if (responsesError) {
      console.error('❌ Error fetching responses:', responsesError);
      return;
    }

    // Get profiles separately if we have responses
    let responsesWithProfiles = responses;
    if (responses && responses.length > 0) {
      const userIds = responses.map(r => r.user_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name')
        .in('id', userIds);

      if (profilesError) {
        console.warn('⚠️ Warning fetching profiles:', profilesError);
      }

      // Create a map of profiles for easy lookup
      const profilesMap = new Map();
      profiles?.forEach(profile => {
        profilesMap.set(profile.id, profile);
      });

      // Attach profiles to responses
      responsesWithProfiles = responses.map(response => ({
        ...response,
        profiles: profilesMap.get(response.user_id) || null
      }));
    }

    console.log(`✅ Found ${responsesWithProfiles?.length || 0} total responses`);

    if (!responsesWithProfiles || responsesWithProfiles.length === 0) {
      console.log('ℹ️  No demographic responses found. The analytics will show empty data.');
      return;
    }

    // Test 2: Group responses by university
    console.log('\n📚 Test 2: Grouping responses by university...');
    const schoolGroups = {};

    responsesWithProfiles.forEach(response => {
      const university = response.responses.university || 'Unknown/Not Specified';
      if (!schoolGroups[university]) {
        schoolGroups[university] = [];
      }
      schoolGroups[university].push(response);
    });

    console.log(`✅ Found ${Object.keys(schoolGroups).length} different schools/universities:`);
    Object.entries(schoolGroups).forEach(([school, schoolResponses]) => {
      console.log(`   - ${school}: ${schoolResponses.length} responses`);
    });

    // Test 3: Calculate breakdowns for each school
    console.log('\n📈 Test 3: Calculating breakdowns for each school...');
    Object.entries(schoolGroups).forEach(([schoolName, schoolResponses]) => {
      console.log(`\n🏫 ${schoolName}:`);
      
      // Country breakdown
      const byCountry = {};
      const byGender = {};
      const byRole = {};
      const byAge = {};

      schoolResponses.forEach(response => {
        const data = response.responses;

        // Country
        const country = data.country || 'Unknown';
        byCountry[country] = (byCountry[country] || 0) + 1;

        // Gender
        const gender = data.gender || 'Unknown';
        byGender[gender] = (byGender[gender] || 0) + 1;

        // Role
        const role = data.role_type || 'Unknown';
        byRole[role] = (byRole[role] || 0) + 1;

        // Age groups
        const age = data.age;
        let ageGroup = 'Unknown';
        if (age) {
          const ageNum = typeof age === 'number' ? age : parseInt(String(age));
          if (!isNaN(ageNum)) {
            if (ageNum < 20) ageGroup = 'Under 20';
            else if (ageNum < 25) ageGroup = '20-24';
            else if (ageNum < 30) ageGroup = '25-29';
            else if (ageNum < 35) ageGroup = '30-34';
            else if (ageNum < 40) ageGroup = '35-39';
            else ageGroup = '40+';
          }
        }
        byAge[ageGroup] = (byAge[ageGroup] || 0) + 1;
      });

      console.log(`   Countries: ${JSON.stringify(byCountry)}`);
      console.log(`   Gender: ${JSON.stringify(byGender)}`);
      console.log(`   Roles: ${JSON.stringify(byRole)}`);
      console.log(`   Age Groups: ${JSON.stringify(byAge)}`);
    });

    // Test 4: Test CSV export data structure
    console.log('\n📄 Test 4: Testing CSV export data structure...');
    const testSchool = Object.entries(schoolGroups)[0];
    if (testSchool) {
      const [schoolName, schoolResponses] = testSchool;
      console.log(`✅ Testing CSV export for "${schoolName}" with ${schoolResponses.length} responses`);
      
      // Simulate CSV headers
      const demographicFields = [
        'consent', 'country', 'gender', 'age', 'formal_training', 'role_type',
        'student_level', 'university', 'undergraduate_program', 'undergraduate_year',
        'postgraduate_program', 'practitioner_work', 'workplace', 'location', 'experience_years'
      ];

      console.log('   CSV Headers would include:');
      console.log('   - Student ID, Student Name, Completion Date');
      console.log(`   - ${demographicFields.length} demographic fields`);
      
      // Show sample data
      const sampleResponse = schoolResponses[0];
      console.log('\n   Sample response data:');
      console.log(`   - Student: ${sampleResponse.profiles?.first_name || 'Unknown'} ${sampleResponse.profiles?.last_name || ''}`);
      console.log(`   - Completed: ${new Date(sampleResponse.completed_at).toLocaleDateString()}`);
      console.log(`   - University: ${sampleResponse.responses.university || 'Not specified'}`);
      console.log(`   - Country: ${sampleResponse.responses.country || 'Not specified'}`);
      console.log(`   - Role: ${sampleResponse.responses.role_type || 'Not specified'}`);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Total responses: ${responsesWithProfiles.length}`);
    console.log(`   - Schools represented: ${Object.keys(schoolGroups).length}`);
    console.log(`   - Ready for school-grouped analytics display`);
    console.log(`   - CSV export functionality verified`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
console.log('🚀 Starting test script...');
testSchoolAnalytics().then(() => {
  console.log('\n✨ Test script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test script failed:', error);
  console.error('Error details:', error);
  process.exit(1);
});
