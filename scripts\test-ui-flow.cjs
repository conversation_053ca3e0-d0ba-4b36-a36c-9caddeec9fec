/**
 * Test UI Flow for Course Completion
 * This script tests the actual UI flow and identifies UX issues
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function checkCourseData() {
  console.log('🔧 Checking course data quality...');
  
  try {
    // Check courses
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title, description')
      .limit(5);
    
    if (coursesError) {
      console.error(`❌ Courses error: ${coursesError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${courses?.length || 0} courses`);
    
    if (courses && courses.length > 0) {
      courses.forEach(course => {
        console.log(`   📚 "${course.title || 'Untitled'}" (${course.id})`);
        if (!course.title || course.title.trim() === '') {
          console.log('   ⚠️  Course has empty title - this may confuse users');
        }
      });
    }
    
    // Check modules
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, title, course_id')
      .limit(10);
    
    if (modulesError) {
      console.error(`❌ Modules error: ${modulesError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${modules?.length || 0} modules`);
    
    // Check lessons
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, title, module_id')
      .limit(10);
    
    if (lessonsError) {
      console.error(`❌ Lessons error: ${lessonsError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${lessons?.length || 0} lessons`);
    
    return { courses, modules, lessons };
    
  } catch (err) {
    console.error(`❌ Course data check error: ${err.message}`);
    return false;
  }
}

async function checkUserExperience() {
  console.log('\n🔧 Checking user experience flow...');
  
  try {
    // Check if there are any existing users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error(`❌ Users error: ${usersError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${users?.users?.length || 0} users in the system`);
    
    // Check if there are any enrollments
    const { data: enrollments, error: enrollmentsError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .limit(10);
    
    if (enrollmentsError) {
      console.error(`❌ Enrollments error: ${enrollmentsError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${enrollments?.length || 0} course enrollments`);
    
    if (enrollments && enrollments.length > 0) {
      const statusCounts = enrollments.reduce((acc, enrollment) => {
        acc[enrollment.status] = (acc[enrollment.status] || 0) + 1;
        return acc;
      }, {});
      
      console.log('   📊 Enrollment status breakdown:', statusCounts);
    }
    
    // Check module progress
    const { data: moduleProgress, error: moduleProgressError } = await supabase
      .from('user_module_progress')
      .select('*')
      .limit(10);
    
    if (moduleProgressError) {
      console.error(`❌ Module progress error: ${moduleProgressError.message}`);
      return false;
    }
    
    console.log(`✅ Found ${moduleProgress?.length || 0} module progress records`);
    
    if (moduleProgress && moduleProgress.length > 0) {
      const completedCount = moduleProgress.filter(p => p.is_completed).length;
      console.log(`   📊 ${completedCount}/${moduleProgress.length} modules marked as completed`);
    }
    
    return true;
    
  } catch (err) {
    console.error(`❌ User experience check error: ${err.message}`);
    return false;
  }
}

async function identifyUIIssues() {
  console.log('\n🔧 Identifying potential UI issues...');
  
  const issues = [];
  
  try {
    // Check for courses with empty titles
    const { data: coursesWithEmptyTitles, error: emptyTitleError } = await supabase
      .from('courses')
      .select('id, title')
      .or('title.is.null,title.eq.');
    
    if (!emptyTitleError && coursesWithEmptyTitles && coursesWithEmptyTitles.length > 0) {
      issues.push(`${coursesWithEmptyTitles.length} courses have empty titles - users won't know what they're completing`);
    }
    
    // Check for modules without lessons
    const { data: modulesWithoutLessons, error: modulesError } = await supabase
      .from('modules')
      .select(`
        id, 
        title,
        lessons:lessons(count)
      `);
    
    if (!modulesError && modulesWithoutLessons) {
      const emptyModules = modulesWithoutLessons.filter(m => !m.lessons || m.lessons.length === 0);
      if (emptyModules.length > 0) {
        issues.push(`${emptyModules.length} modules have no lessons - users can't complete them`);
      }
    }
    
    // Check for orphaned modules (modules without courses)
    const { data: orphanedModules, error: orphanError } = await supabase
      .from('modules')
      .select('id, title, course_id')
      .is('course_id', null);
    
    if (!orphanError && orphanedModules && orphanedModules.length > 0) {
      issues.push(`${orphanedModules.length} modules are not linked to any course`);
    }
    
    return issues;
    
  } catch (err) {
    console.error(`❌ UI issues check error: ${err.message}`);
    return ['Error checking for UI issues'];
  }
}

async function checkFinishButtonLogic() {
  console.log('\n🔧 Testing FinishCourseButton logic...');
  
  try {
    // Get a course with modules
    const { data: courseWithModules, error: courseError } = await supabase
      .from('courses')
      .select(`
        id,
        title,
        modules:modules(id, title)
      `)
      .limit(1);
    
    if (courseError || !courseWithModules || courseWithModules.length === 0) {
      console.error('❌ No courses with modules found for testing');
      return false;
    }
    
    const course = courseWithModules[0];
    console.log(`✅ Testing with course: "${course.title}" (${course.modules?.length || 0} modules)`);
    
    if (!course.modules || course.modules.length === 0) {
      console.error('❌ Course has no modules - users cannot complete it');
      return false;
    }
    
    // Create a test user for this check
    const testUserId = '00000000-0000-0000-0000-000000000000';
    
    // Simulate the FinishCourseButton logic
    console.log('   🔧 Simulating module completion check...');
    
    const { data: moduleProgress, error: progressError } = await supabase
      .from('user_module_progress')
      .select('module_id')
      .eq('user_id', testUserId)
      .eq('is_completed', true)
      .in('module_id', course.modules.map(m => m.id));
    
    if (progressError) {
      console.error(`❌ Module progress check failed: ${progressError.message}`);
      return false;
    }
    
    const completedModules = moduleProgress?.length || 0;
    const totalModules = course.modules.length;
    const allModulesCompleted = completedModules === totalModules;
    
    console.log(`   📊 Modules completed: ${completedModules}/${totalModules}`);
    console.log(`   🎯 All modules completed: ${allModulesCompleted}`);
    
    if (!allModulesCompleted) {
      console.log('   ℹ️  Button would be disabled (expected for test user)');
    }
    
    // Check enrollment status
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_course_enrollment')
      .select('status')
      .eq('user_id', testUserId)
      .eq('course_id', course.id)
      .single();
    
    if (enrollmentError && !enrollmentError.message.includes('No rows')) {
      console.error(`❌ Enrollment check failed: ${enrollmentError.message}`);
      return false;
    }
    
    const isEnrolled = !!enrollment;
    console.log(`   📝 User enrolled: ${isEnrolled}`);
    
    if (!isEnrolled) {
      console.log('   ℹ️  User would need to enroll first');
    }
    
    return true;
    
  } catch (err) {
    console.error(`❌ FinishButton logic test error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing UI Flow for Course Completion...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;
  const recommendations = [];

  // Check 1: Course data quality
  const courseData = await checkCourseData();
  if (!courseData) {
    allSuccess = false;
    recommendations.push('Fix course data issues');
  }

  // Check 2: User experience flow
  if (!await checkUserExperience()) {
    allSuccess = false;
    recommendations.push('Fix user experience flow issues');
  }

  // Check 3: UI issues
  const uiIssues = await identifyUIIssues();
  if (uiIssues.length > 0) {
    allSuccess = false;
    recommendations.push('Fix UI data issues');
  }

  // Check 4: FinishButton logic
  if (!await checkFinishButtonLogic()) {
    allSuccess = false;
    recommendations.push('Fix FinishCourseButton logic');
  }

  console.log('\n' + '='.repeat(60));
  if (allSuccess && uiIssues.length === 0) {
    console.log('🎉 UI flow for course completion looks good!');
    console.log('✅ No major issues found');
    console.log('');
    console.log('📋 The course completion system should work properly for users who:');
    console.log('  1. Create an account and log in');
    console.log('  2. Enroll in a course');
    console.log('  3. Complete all modules in the course');
    console.log('  4. Click the "Finish Course" button');
  } else {
    console.log('⚠️  UI flow has some issues that may prevent course completion:');
    
    if (uiIssues.length > 0) {
      console.log('\n🔍 Data Quality Issues:');
      uiIssues.forEach(issue => console.log(`  ❌ ${issue}`));
    }
    
    if (recommendations.length > 0) {
      console.log('\n🔧 Recommendations:');
      recommendations.forEach(rec => console.log(`  📝 ${rec}`));
    }
  }
  console.log('='.repeat(60));
  
  return allSuccess && uiIssues.length === 0;
}

main().catch(console.error);
