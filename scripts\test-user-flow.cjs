/**
 * Test User Flow for Course Completion
 * This script tests the actual user flow to identify real-world issues
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function createTestUser() {
  console.log('🔧 Creating test user...');
  
  try {
    // Create a test user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true
    });
    
    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('✅ Test user already exists, getting existing user...');
        
        // Get existing user
        const { data: users, error: getUserError } = await supabase.auth.admin.listUsers();
        if (getUserError) {
          console.error('❌ Error getting users:', getUserError.message);
          return null;
        }
        
        const testUser = users.users.find(u => u.email === '<EMAIL>');
        if (testUser) {
          console.log('✅ Found existing test user:', testUser.id);
          return testUser;
        }
      } else {
        console.error('❌ Error creating test user:', authError.message);
        return null;
      }
    }
    
    if (authData?.user) {
      console.log('✅ Test user created:', authData.user.id);
      return authData.user;
    }
    
    return null;
    
  } catch (err) {
    console.error(`❌ Test user creation error: ${err.message}`);
    return null;
  }
}

async function testCourseEnrollment(userId, courseId) {
  console.log('\n🔧 Testing course enrollment...');
  
  try {
    const now = new Date().toISOString();
    
    // Try to enroll the user in the course
    const { data: enrollmentData, error: enrollmentError } = await supabase
      .from('user_course_enrollment')
      .upsert({
        user_id: userId,
        course_id: courseId,
        status: 'in_progress',
        enrolled_at: now,
        updated_at: now
      }, {
        onConflict: 'user_id,course_id'
      })
      .select();
    
    if (enrollmentError) {
      console.error(`❌ Enrollment error: ${enrollmentError.message}`);
      return false;
    }
    
    console.log('✅ User enrolled in course successfully');
    return true;
    
  } catch (err) {
    console.error(`❌ Enrollment test error: ${err.message}`);
    return false;
  }
}

async function testModuleCompletion(userId, courseId) {
  console.log('\n🔧 Testing module completion...');
  
  try {
    // Get modules for the course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, title')
      .eq('course_id', courseId);
    
    if (modulesError) {
      console.error(`❌ Modules query error: ${modulesError.message}`);
      return false;
    }
    
    if (!modules || modules.length === 0) {
      console.error('❌ No modules found for course');
      return false;
    }
    
    console.log(`✅ Found ${modules.length} modules for course`);
    
    // Mark all modules as completed
    const moduleCompletions = modules.map(module => ({
      user_id: userId,
      module_id: module.id,
      is_completed: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    const { data: progressData, error: progressError } = await supabase
      .from('user_module_progress')
      .upsert(moduleCompletions, {
        onConflict: 'user_id,module_id'
      })
      .select();
    
    if (progressError) {
      console.error(`❌ Module completion error: ${progressError.message}`);
      return false;
    }
    
    console.log(`✅ Marked ${progressData?.length || 0} modules as completed`);
    return true;
    
  } catch (err) {
    console.error(`❌ Module completion test error: ${err.message}`);
    return false;
  }
}

async function testCourseCompletion(userId, courseId) {
  console.log('\n🔧 Testing course completion...');
  
  try {
    // Test the complete_course RPC function
    const { data: rpcData, error: rpcError } = await supabase.rpc('complete_course', {
      p_user_id: userId,
      p_course_id: courseId
    });
    
    if (rpcError) {
      console.error(`❌ Course completion RPC error: ${rpcError.message}`);
      return false;
    }
    
    console.log('✅ Course completion RPC successful:', rpcData);
    
    // Verify the completion was recorded
    const { data: enrollment, error: verifyError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .single();
    
    if (verifyError) {
      console.error(`❌ Verification error: ${verifyError.message}`);
      return false;
    }
    
    if (enrollment.status === 'completed' && enrollment.completed_at) {
      console.log('✅ Course completion verified in database');
      console.log(`   Status: ${enrollment.status}`);
      console.log(`   Completed at: ${enrollment.completed_at}`);
      return true;
    } else {
      console.error('❌ Course completion not properly recorded');
      console.log('   Enrollment data:', enrollment);
      return false;
    }
    
  } catch (err) {
    console.error(`❌ Course completion test error: ${err.message}`);
    return false;
  }
}

async function cleanupTestData(userId, courseId) {
  console.log('\n🔧 Cleaning up test data...');
  
  try {
    // Remove test enrollment
    await supabase
      .from('user_course_enrollment')
      .delete()
      .eq('user_id', userId)
      .eq('course_id', courseId);
    
    // Remove test module progress
    await supabase
      .from('user_module_progress')
      .delete()
      .eq('user_id', userId);
    
    // Remove test course progress
    await supabase
      .from('user_course_progress')
      .delete()
      .eq('user_id', userId)
      .eq('course_id', courseId);
    
    console.log('✅ Test data cleaned up');
    
  } catch (err) {
    console.error(`❌ Cleanup error: ${err.message}`);
  }
}

async function main() {
  console.log('🚀 Testing Complete User Flow for Course Completion...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;
  const issues = [];

  // Get a course to test with
  const { data: courses, error: coursesError } = await supabase
    .from('courses')
    .select('id, title')
    .limit(1);
  
  if (coursesError || !courses || courses.length === 0) {
    console.error('❌ No courses available for testing');
    process.exit(1);
  }
  
  const testCourse = courses[0];
  console.log(`📚 Testing with course: "${testCourse.title}" (${testCourse.id})`);
  
  // Step 1: Create test user
  const testUser = await createTestUser();
  if (!testUser) {
    console.error('❌ Failed to create test user');
    process.exit(1);
  }
  
  try {
    // Step 2: Test enrollment
    if (!await testCourseEnrollment(testUser.id, testCourse.id)) {
      allSuccess = false;
      issues.push('Course enrollment failed');
    }

    // Step 3: Test module completion
    if (!await testModuleCompletion(testUser.id, testCourse.id)) {
      allSuccess = false;
      issues.push('Module completion failed');
    }

    // Step 4: Test course completion
    if (!await testCourseCompletion(testUser.id, testCourse.id)) {
      allSuccess = false;
      issues.push('Course completion failed');
    }

    console.log('\n' + '='.repeat(60));
    if (allSuccess) {
      console.log('🎉 Complete user flow for course completion is working!');
      console.log('✅ All tests passed');
      console.log('');
      console.log('📋 Flow Status:');
      console.log('  ✅ User creation/authentication working');
      console.log('  ✅ Course enrollment working');
      console.log('  ✅ Module completion tracking working');
      console.log('  ✅ Course completion working');
      console.log('  ✅ Database persistence working');
    } else {
      console.log('⚠️  Course completion flow has issues:');
      issues.forEach(issue => console.log(`  ❌ ${issue}`));
    }
    console.log('='.repeat(60));
    
  } finally {
    // Always cleanup test data
    await cleanupTestData(testUser.id, testCourse.id);
  }
  
  return allSuccess;
}

main().catch(console.error);
