/**
 * Test Video Display Fix Script
 * 
 * This script tests if the video display issue has been resolved by:
 * 1. Checking lesson content with videos
 * 2. Testing content security settings
 * 3. Verifying iframe handling
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

/**
 * Check if a specific lesson exists by UUID
 */
async function checkLessonByUuid(uuid) {
  console.log(`${colors.cyan}🔍 Checking lesson with UUID: ${uuid}${colors.reset}\n`);
  
  try {
    const { data: lesson, error } = await supabase
      .from('lessons')
      .select('id, title, slug, content, type')
      .eq('id', uuid)
      .single();
    
    if (error) {
      console.error(`${colors.red}❌ Error fetching lesson: ${error.message}${colors.reset}`);
      return null;
    }
    
    if (!lesson) {
      console.log(`${colors.yellow}⚠️ No lesson found with UUID: ${uuid}${colors.reset}`);
      return null;
    }
    
    console.log(`${colors.green}✅ Found lesson:${colors.reset}`);
    console.log(`   Title: ${lesson.title}`);
    console.log(`   Slug: ${lesson.slug}`);
    console.log(`   Type: ${lesson.type}`);
    
    // Check content structure
    if (lesson.content) {
      const isJson = lesson.content.startsWith('{');
      console.log(`   Content Format: ${isJson ? colors.yellow + 'JSON (Legacy)' : colors.green + 'Markdown'}${colors.reset}`);
      
      if (isJson) {
        try {
          const parsed = JSON.parse(lesson.content);
          console.log(`   Has videoUrl: ${parsed.videoUrl ? colors.green + 'Yes' : 'No'}${colors.reset}`);
          if (parsed.videoUrl) {
            console.log(`   Video URL: ${parsed.videoUrl}`);
          }
        } catch (e) {
          console.log(`   ${colors.red}JSON Parse Error${colors.reset}`);
        }
      } else {
        // Check for YouTube embeds in markdown
        const hasYouTubeEmbed = lesson.content.includes('youtube.com/embed') || 
                                lesson.content.includes('data-youtube-video');
        console.log(`   Has YouTube Embed: ${hasYouTubeEmbed ? colors.green + 'Yes' : 'No'}${colors.reset}`);
        
        if (hasYouTubeEmbed) {
          // Extract video URLs
          const videoMatches = lesson.content.match(/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/g);
          if (videoMatches) {
            console.log(`   Video URLs found: ${videoMatches.length}`);
            videoMatches.forEach((match, i) => {
              console.log(`     ${i + 1}. ${match}`);
            });
          }
        }
      }
      
      console.log(`   Content Length: ${lesson.content.length} characters`);
    } else {
      console.log(`   Content: ${colors.yellow}Empty${colors.reset}`);
    }
    
    return lesson;
  } catch (error) {
    console.error(`${colors.red}❌ Error checking lesson: ${error.message}${colors.reset}`);
    return null;
  }
}

/**
 * Test all lessons with video content
 */
async function testAllVideoLessons() {
  console.log(`${colors.cyan}${colors.bold}🎥 Testing All Video Lessons${colors.reset}\n`);
  
  try {
    const { data: lessons, error } = await supabase
      .from('lessons')
      .select('id, title, slug, content, type')
      .or('content.like.%youtube%,content.like.%data-youtube-video%')
      .order('created_at');
    
    if (error) {
      console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
      return;
    }
    
    if (!lessons || lessons.length === 0) {
      console.log(`${colors.yellow}⚠️ No lessons with video content found${colors.reset}`);
      return;
    }
    
    console.log(`${colors.green}✅ Found ${lessons.length} lessons with video content:${colors.reset}\n`);
    
    lessons.forEach((lesson, index) => {
      console.log(`${colors.bold}${index + 1}. ${lesson.title}${colors.reset}`);
      console.log(`   ID: ${lesson.id}`);
      console.log(`   Slug: ${lesson.slug}`);
      console.log(`   Type: ${lesson.type}`);
      
      if (lesson.content) {
        const isJson = lesson.content.startsWith('{');
        console.log(`   Content Format: ${isJson ? colors.yellow + 'JSON (Legacy)' : colors.green + 'Markdown'}${colors.reset}`);
        
        if (isJson) {
          try {
            const parsed = JSON.parse(lesson.content);
            if (parsed.videoUrl) {
              console.log(`   ${colors.green}✅ Legacy Video URL: ${parsed.videoUrl}${colors.reset}`);
            }
          } catch (e) {
            console.log(`   ${colors.red}❌ JSON Parse Error${colors.reset}`);
          }
        } else {
          // Check for YouTube embeds in markdown
          const hasYouTubeEmbed = lesson.content.includes('youtube.com/embed') || 
                                  lesson.content.includes('data-youtube-video');
          if (hasYouTubeEmbed) {
            console.log(`   ${colors.green}✅ Markdown Video Embed Found${colors.reset}`);
            
            // Extract video URLs
            const videoMatches = lesson.content.match(/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/g);
            if (videoMatches) {
              videoMatches.forEach((match, i) => {
                console.log(`     Video ${i + 1}: https://${match}`);
              });
            }
          }
        }
      }
      
      console.log(`   ${colors.blue}Access URL: /lesson/${lesson.slug}${colors.reset}`);
      console.log('');
    });
    
    return lessons;
  } catch (error) {
    console.error(`${colors.red}❌ Error testing video lessons: ${error.message}${colors.reset}`);
  }
}

/**
 * Show fix status
 */
function showFixStatus() {
  console.log(`${colors.cyan}${colors.bold}🔧 Video Display Fix Status${colors.reset}\n`);
  
  console.log(`${colors.bold}Changes Made:${colors.reset}`);
  console.log(`1. ${colors.green}✅ Updated LessonContent component${colors.reset}`);
  console.log(`   • Changed MarkdownPreview securityLevel to "full"`);
  console.log(`   • Allows iframe tags for video embeds`);
  console.log('');
  
  console.log(`2. ${colors.green}✅ Updated Content Security configuration${colors.reset}`);
  console.log(`   • Added iframe to extended security level`);
  console.log(`   • Added iframe attributes (frameborder, allowfullscreen, allow)`);
  console.log('');
  
  console.log(`${colors.bold}Expected Results:${colors.reset}`);
  console.log(`• ${colors.green}✅ Videos should now display in lesson content${colors.reset}`);
  console.log(`• ${colors.green}✅ Both legacy JSON and new markdown videos supported${colors.reset}`);
  console.log(`• ${colors.green}✅ YouTube iframes properly rendered${colors.reset}`);
  console.log('');
  
  console.log(`${colors.bold}How to Test:${colors.reset}`);
  console.log(`1. Visit a lesson with video content`);
  console.log(`2. Check if YouTube videos are displayed`);
  console.log(`3. Verify videos are responsive and functional`);
  console.log('');
}

/**
 * Main function
 */
async function main() {
  try {
    const uuid = process.argv[2];
    
    if (uuid) {
      // Check specific lesson by UUID
      const lesson = await checkLessonByUuid(uuid);
      
      if (lesson) {
        console.log(`\n${colors.blue}Test this lesson at: http://localhost:5173/lesson/${lesson.slug}${colors.reset}`);
      }
    } else {
      // Test all video lessons
      await testAllVideoLessons();
    }
    
    // Show fix status
    showFixStatus();
    
    console.log(`${colors.cyan}${colors.bold}🎉 Video Display Fix Applied!${colors.reset}`);
    console.log(`\n${colors.yellow}💡 To test a specific lesson, run:${colors.reset}`);
    console.log(`   node scripts/test-video-display-fix.js LESSON_UUID`);
    
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main();
