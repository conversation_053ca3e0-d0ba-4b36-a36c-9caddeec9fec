#!/usr/bin/env node

/**
 * Final test script to verify video functionality is working
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

/**
 * Test video functionality
 */
async function testVideoFunctionality() {
  console.log(`${colors.cyan}${colors.bold}🎥 Final Video Functionality Test${colors.reset}\n`);
  
  // Get lessons with video content
  const { data: lessons, error } = await supabase
    .from('lessons')
    .select('id, title, slug, content, type')
    .or('content.like.%youtube%,content.like.%data-youtube-video%,content.like.%videoUrl%')
    .order('created_at');
  
  if (error) {
    console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
    return;
  }
  
  if (!lessons || lessons.length === 0) {
    console.log(`${colors.yellow}⚠️ No lessons with video content found${colors.reset}`);
    return;
  }
  
  console.log(`${colors.green}✅ Found ${lessons.length} lessons with video content:${colors.reset}\n`);
  
  lessons.forEach((lesson, index) => {
    console.log(`${index + 1}. ${colors.blue}${lesson.title}${colors.reset}`);
    console.log(`   Slug: ${lesson.slug}`);
    console.log(`   Type: ${lesson.type}`);
    
    // Check content format
    let hasVideo = false;
    let videoFormat = 'None';
    let videoUrl = null;
    
    if (lesson.content) {
      // Check for JSON format (legacy)
      try {
        const parsed = JSON.parse(lesson.content);
        if (parsed.videoUrl) {
          hasVideo = true;
          videoFormat = 'JSON (Legacy)';
          videoUrl = parsed.videoUrl;
        }
      } catch {
        // Check for markdown format (TipTap)
        if (lesson.content.includes('youtube.com/embed') || 
            lesson.content.includes('data-youtube-video') ||
            lesson.content.includes('<iframe')) {
          hasVideo = true;
          videoFormat = 'Markdown (TipTap)';
          
          // Extract video URL from iframe
          const iframeMatch = lesson.content.match(/src="([^"]*youtube\.com\/embed\/[^"]*)"/);
          if (iframeMatch) {
            videoUrl = iframeMatch[1];
          }
        }
      }
    }
    
    console.log(`   Video: ${hasVideo ? colors.green + 'Yes' : colors.yellow + 'No'}${colors.reset}`);
    console.log(`   Format: ${videoFormat}`);
    if (videoUrl) {
      console.log(`   URL: ${videoUrl}`);
    }
    console.log(`   Test URL: ${colors.cyan}http://localhost:5173/lesson/${lesson.slug}${colors.reset}`);
    console.log('');
  });
  
  console.log(`${colors.green}🎯 Test Instructions:${colors.reset}`);
  console.log(`1. Open each test URL in your browser`);
  console.log(`2. Verify that videos are displayed correctly`);
  console.log(`3. Check that videos are responsive and functional`);
  console.log(`4. Ensure no console errors appear`);
  console.log(`\n${colors.yellow}💡 Both JSON (legacy) and Markdown (TipTap) formats should work!${colors.reset}`);
}

/**
 * Main function
 */
async function main() {
  await testVideoFunctionality();
}

main().catch(console.error);
