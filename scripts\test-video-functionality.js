/**
 * Test Video Functionality Script
 * 
 * This script tests the video functionality by:
 * 1. Creating a test lesson with video content
 * 2. Testing video URL processing
 * 3. Verifying video display in lesson content
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

/**
 * Process video URL to proper embed format
 */
function processVideoUrl(url) {
  if (!url) return null;
  
  // Handle YouTube URLs
  if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
    const videoId = url.includes('youtu.be') 
      ? url.split('/').pop()?.split('?')[0]
      : new URL(url).searchParams.get('v');
    
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}`;
    }
  }
  
  // Handle Vimeo URLs
  if (url.includes('vimeo.com')) {
    const vimeoId = url.split('/').pop()?.split('?')[0];
    if (vimeoId && !isNaN(vimeoId)) {
      return `https://player.vimeo.com/video/${vimeoId}`;
    }
  }
  
  return url;
}

/**
 * Test video URL processing
 */
function testVideoUrlProcessing() {
  console.log(`${colors.cyan}🧪 Testing Video URL Processing${colors.reset}\n`);
  
  const testUrls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ',
    'https://vimeo.com/123456789',
    'https://www.youtube.com/embed/dQw4w9WgXcQ',
    'https://player.vimeo.com/video/123456789'
  ];
  
  testUrls.forEach((url, index) => {
    const processed = processVideoUrl(url);
    console.log(`${index + 1}. Original: ${url}`);
    console.log(`   Processed: ${processed}`);
    console.log(`   ${processed !== url ? colors.green + '✅ Converted' : colors.blue + '✅ Already embedded'}${colors.reset}`);
    console.log('');
  });
}

/**
 * Create a test lesson with video content
 */
async function createTestVideoLesson() {
  console.log(`${colors.cyan}🎥 Creating Test Video Lesson${colors.reset}\n`);
  
  try {
    // First, get a module to add the lesson to
    const { data: modules, error: moduleError } = await supabase
      .from('modules')
      .select('id, title')
      .limit(1);
    
    if (moduleError || !modules?.length) {
      console.error(`${colors.red}❌ No modules found: ${moduleError?.message}${colors.reset}`);
      return false;
    }
    
    const module = modules[0];
    console.log(`${colors.blue}📚 Using module: ${module.title}${colors.reset}`);
    
    // Create rich content with video
    const videoUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    const processedVideoUrl = processVideoUrl(videoUrl);
    
    const richContent = {
      content: `# Test Video Lesson

This is a test lesson to demonstrate video functionality in the LMS.

## Learning Objectives

- Understand how videos are integrated into lessons
- See how video URLs are processed and embedded
- Test the video player functionality

## Video Content

The video above demonstrates the concepts covered in this lesson. Please watch it carefully and take notes.

## Summary

This lesson shows how videos can be seamlessly integrated into lesson content using JSON-based rich content structure.`,
      videoUrl: processedVideoUrl,
      videoTitle: 'Test Educational Video',
      videoDescription: 'A sample video to test the video functionality'
    };
    
    // Create the lesson
    const lessonData = {
      module_id: module.id,
      title: 'Test Video Lesson - Video Integration Demo',
      slug: 'test-video-lesson-demo',
      duration: '10:00',
      type: 'lesson',
      content: JSON.stringify(richContent),
      lesson_number: 999, // High number to put it at the end
      requirement: null
    };
    
    console.log(`${colors.yellow}📝 Creating lesson with video content...${colors.reset}`);
    console.log(`   Video URL: ${videoUrl}`);
    console.log(`   Processed URL: ${processedVideoUrl}`);
    
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .insert([lessonData])
      .select()
      .single();
    
    if (lessonError) {
      console.error(`${colors.red}❌ Error creating lesson: ${lessonError.message}${colors.reset}`);
      return false;
    }
    
    console.log(`${colors.green}✅ Successfully created test video lesson!${colors.reset}`);
    console.log(`   Lesson ID: ${lesson.id}`);
    console.log(`   Lesson Slug: ${lesson.slug}`);
    console.log(`   Module: ${module.title}`);
    
    return lesson;
  } catch (error) {
    console.error(`${colors.red}❌ Error creating test lesson: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test lesson content parsing
 */
async function testLessonContentParsing() {
  console.log(`${colors.cyan}🔍 Testing Lesson Content Parsing${colors.reset}\n`);
  
  try {
    // Find lessons with JSON content
    const { data: lessons, error } = await supabase
      .from('lessons')
      .select('id, title, content')
      .like('content', '{%')
      .limit(5);
    
    if (error) {
      console.error(`${colors.red}❌ Error fetching lessons: ${error.message}${colors.reset}`);
      return;
    }
    
    if (!lessons?.length) {
      console.log(`${colors.yellow}⚠️ No lessons with JSON content found${colors.reset}`);
      return;
    }
    
    console.log(`${colors.green}✅ Found ${lessons.length} lessons with JSON content${colors.reset}\n`);
    
    lessons.forEach((lesson, index) => {
      console.log(`${index + 1}. ${lesson.title}`);
      
      try {
        const parsed = JSON.parse(lesson.content);
        console.log(`   ${colors.green}✅ Valid JSON content${colors.reset}`);
        console.log(`   Has video: ${parsed.videoUrl ? colors.green + 'Yes' : colors.yellow + 'No'}${colors.reset}`);
        
        if (parsed.videoUrl) {
          console.log(`   Video URL: ${parsed.videoUrl}`);
          console.log(`   Video Title: ${parsed.videoTitle || 'Not specified'}`);
        }
        
        console.log(`   Content length: ${parsed.content?.length || 0} characters`);
      } catch (parseError) {
        console.log(`   ${colors.red}❌ JSON parse error: ${parseError.message}${colors.reset}`);
      }
      
      console.log('');
    });
  } catch (error) {
    console.error(`${colors.red}❌ Error testing content parsing: ${error.message}${colors.reset}`);
  }
}

/**
 * Clean up test lessons
 */
async function cleanupTestLessons() {
  console.log(`${colors.yellow}🧹 Cleaning up test lessons...${colors.reset}`);
  
  try {
    const { data: deletedLessons, error } = await supabase
      .from('lessons')
      .delete()
      .like('title', '%Test Video Lesson%')
      .select();
    
    if (error) {
      console.error(`${colors.red}❌ Error deleting test lessons: ${error.message}${colors.reset}`);
      return;
    }
    
    console.log(`${colors.green}✅ Deleted ${deletedLessons?.length || 0} test lessons${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}❌ Error during cleanup: ${error.message}${colors.reset}`);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const action = process.argv[2];
    
    if (action === 'clean' || action === 'cleanup') {
      await cleanupTestLessons();
    } else if (action === 'parse') {
      await testLessonContentParsing();
    } else {
      // Run full test
      testVideoUrlProcessing();
      
      const lesson = await createTestVideoLesson();
      
      if (lesson) {
        console.log(`\n${colors.cyan}🎉 Test completed successfully!${colors.reset}`);
        console.log(`\n${colors.cyan}💡 Next steps:${colors.reset}`);
        console.log(`   1. Visit the lesson page to see the video in action`);
        console.log(`   2. Check the admin panel to edit the lesson`);
        console.log(`   3. Test adding videos through the lesson editor`);
        console.log(`   4. Run 'node scripts/test-video-functionality.js clean' to remove test lessons`);
        
        await testLessonContentParsing();
      }
    }
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main();
