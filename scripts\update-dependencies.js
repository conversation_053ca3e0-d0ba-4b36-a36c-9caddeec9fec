// This script updates the package.json with new dependencies
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Updating dependencies...');

// Function to read a file
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return null;
  }
}

// Function to write a file
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error.message);
    return false;
  }
}

// Function to run a command
function runCommand(command) {
  try {
    console.log(`Running command: ${command}`);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Error running command: ${command}`, error.message);
    return false;
  }
}

// Update package.json
function updatePackageJson() {
  const filePath = path.join(__dirname, '..', 'package.json');
  let content = readFile(filePath);
  if (!content) return false;

  // Parse the package.json
  const packageJson = JSON.parse(content);

  // Add the new scripts
  packageJson.scripts = {
    ...packageJson.scripts,
    "fix:connection": "node scripts/setup-health-check.js",
  };

  // Write the updated package.json
  return writeFile(filePath, JSON.stringify(packageJson, null, 2));
}

// Install required dependencies
function installDependencies() {
  return runCommand('npm install --save-dev @supabase/supabase-js');
}

// Run the updates
async function runUpdates() {
  console.log('Running dependency updates...');
  
  const results = {
    updatePackageJson: updatePackageJson(),
    installDependencies: installDependencies()
  };
  
  console.log('\n--- Update Results ---');
  for (const [update, result] of Object.entries(results)) {
    console.log(`${update}: ${result ? 'Success' : 'Failed'}`);
  }
  
  if (Object.values(results).every(result => result)) {
    console.log('\nAll updates applied successfully!');
    console.log('\nTo fix connection issues, run:');
    console.log('npm run fix:connection');
  } else {
    console.log('\nSome updates failed. Please check the logs above for details.');
  }
}

// Run the updates
runUpdates();
