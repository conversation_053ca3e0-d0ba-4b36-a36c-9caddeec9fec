import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// List of countries with Ghana as first option
const countries = [
  "Ghana",
  "Afghanistan", "Albania", "Algeria", "Argentina", "Armenia", "Australia",
  "Austria", "Azerbaijan", "Bahrain", "Bangladesh", "Belarus", "Belgium",
  "Bolivia", "Bosnia and Herzegovina", "Brazil", "Bulgaria", "Cambodia",
  "Cameroon", "Canada", "Chile", "China", "Colombia", "Costa Rica",
  "Croatia", "Czech Republic", "Denmark", "Ecuador", "Egypt", "Estonia",
  "Ethiopia", "Finland", "France", "Georgia", "Germany", "Greece",
  "Guatemala", "Honduras", "Hungary", "Iceland", "India", "Indonesia",
  "Iran", "Iraq", "Ireland", "Israel", "Italy", "Japan", "Jordan",
  "Kazakhstan", "Kenya", "Kuwait", "Latvia", "Lebanon", "Lithuania",
  "Luxembourg", "Malaysia", "Mexico", "Morocco", "Netherlands", "New Zealand",
  "Nigeria", "Norway", "Pakistan", "Peru", "Philippines", "Poland",
  "Portugal", "Qatar", "Romania", "Russia", "Saudi Arabia", "Singapore",
  "Slovakia", "Slovenia", "South Africa", "South Korea", "Spain", "Sri Lanka",
  "Sweden", "Switzerland", "Thailand", "Turkey", "Ukraine", "United Arab Emirates",
  "United Kingdom", "United States", "Uruguay", "Venezuela", "Vietnam", "Other"
];

async function updateQuestionnaireDropdowns() {
  try {
    console.log('🔄 Updating questionnaire to use dropdowns...\n');

    // Get current questionnaire
    const { data: questionnaire, error: fetchError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true)
      .single();

    if (fetchError) {
      console.error('Error fetching questionnaire:', fetchError);
      return;
    }

    console.log('✅ Current questionnaire loaded');

    // Update questions with dropdown types and country options
    const updatedQuestions = questionnaire.questions.map(question => {
      switch (question.id) {
        case 'country':
          return {
            ...question,
            type: 'dropdown',
            options: countries,
            default: 'Ghana'
          };

        case 'university':
          return {
            ...question,
            type: 'dropdown' // 8 options - good for dropdown
          };

        case 'location':
          return {
            ...question,
            type: 'dropdown' // 16 options - definitely needs dropdown
          };

        case 'undergraduate_year':
          return {
            ...question,
            type: 'dropdown' // 6 options - good for dropdown
          };

        default:
          return question;
      }
    });

    console.log('📝 Questions updated:');
    console.log('   • Country: text → dropdown (with Ghana default)');
    console.log('   • University: single_choice → dropdown');
    console.log('   • Location: single_choice → dropdown');
    console.log('   • Undergraduate Year: single_choice → dropdown');

    // Update the questionnaire in database
    const { error: updateError } = await supabase
      .from('demographic_questionnaires')
      .update({
        questions: updatedQuestions,
        updated_at: new Date().toISOString()
      })
      .eq('id', questionnaire.id);

    if (updateError) {
      console.error('Error updating questionnaire:', updateError);
      return;
    }

    console.log('\n✅ Questionnaire updated successfully!');

    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('demographic_questionnaires')
      .select('questions')
      .eq('is_active', true)
      .single();

    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }

    const dropdownQuestions = verifyData.questions.filter(q => q.type === 'dropdown');
    console.log('\n📊 Verification:');
    console.log(`   • Total questions: ${verifyData.questions.length}`);
    console.log(`   • Dropdown questions: ${dropdownQuestions.length}`);
    console.log('   • Dropdown question IDs:', dropdownQuestions.map(q => q.id).join(', '));

    // Check country options
    const countryQuestion = verifyData.questions.find(q => q.id === 'country');
    if (countryQuestion) {
      console.log(`   • Country options: ${countryQuestion.options.length} countries`);
      console.log(`   • Default country: ${countryQuestion.default || 'Ghana'}`);
      console.log(`   • First 5 countries: ${countryQuestion.options.slice(0, 5).join(', ')}`);
    }

    console.log('\n🎉 Dropdown update completed successfully!');

  } catch (error) {
    console.error('❌ Error updating questionnaire:', error);
  }
}

updateQuestionnaireDropdowns();
