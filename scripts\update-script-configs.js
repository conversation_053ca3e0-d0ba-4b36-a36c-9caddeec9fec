/**
 * Update Script Configs
 * 
 * This script updates all other scripts to use the centralized configuration
 * instead of hardcoded Supabase credentials.
 */

const fs = require('fs');
const path = require('path');

// Directory containing the scripts
const scriptsDir = __dirname;

// Files to exclude from processing
const excludeFiles = [
  'config.js',
  'update-script-configs.js',
  'package.json',
  'README.md',
  'node_modules'
];

// Pattern to match hardcoded Supabase URL and keys
const supabaseUrlPattern = /const SUPABASE_URL\s*=\s*["']https:\/\/jibspqwieubavucdtccv\.supabase\.co["'];/g;
const supabaseKeyPattern = /const SUPABASE_ANON_KEY\s*=\s*["']eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\..*["'];/g;
const supabaseServiceKeyPattern = /const SUPABASE_SERVICE_KEY\s*=\s*process\.env\.SUPABASE_SERVICE_KEY\s*\|\|\s*["'].*["'];/g;

// Replacement text
const configImport = "const { SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY } = require('./config');";

// Process a single file
function processFile(filePath) {
  // Read the file
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file already imports the config
  if (content.includes("require('./config')")) {
    console.log(`${path.basename(filePath)} already uses the config module.`);
    return;
  }

  // Replace hardcoded values
  if (supabaseUrlPattern.test(content) || supabaseKeyPattern.test(content)) {
    // Add the config import after the last require statement
    const lastRequireIndex = content.lastIndexOf('require(');
    if (lastRequireIndex !== -1) {
      const endOfLineIndex = content.indexOf('\n', lastRequireIndex);
      if (endOfLineIndex !== -1) {
        content = content.substring(0, endOfLineIndex + 1) + 
                 configImport + '\n' + 
                 content.substring(endOfLineIndex + 1);
        modified = true;
      }
    } else {
      // If no require statements, add at the top after any comments
      const lines = content.split('\n');
      let insertIndex = 0;
      
      // Skip past initial comments
      while (insertIndex < lines.length && 
             (lines[insertIndex].trim().startsWith('//') || 
              lines[insertIndex].trim().startsWith('/*') ||
              lines[insertIndex].trim() === '')) {
        insertIndex++;
      }
      
      lines.splice(insertIndex, 0, configImport);
      content = lines.join('\n');
      modified = true;
    }

    // Replace the hardcoded values
    content = content.replace(supabaseUrlPattern, '// Using centralized config for SUPABASE_URL');
    content = content.replace(supabaseKeyPattern, '// Using centralized config for SUPABASE_ANON_KEY');
    content = content.replace(supabaseServiceKeyPattern, '// Using centralized config for SUPABASE_SERVICE_ROLE_KEY');
    
    modified = true;
  }

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`Updated ${path.basename(filePath)}`);
  } else {
    console.log(`No changes needed for ${path.basename(filePath)}`);
  }
}

// Process all JavaScript files in the scripts directory
function processAllFiles() {
  const files = fs.readdirSync(scriptsDir);
  
  for (const file of files) {
    if (excludeFiles.includes(file)) continue;
    
    const filePath = path.join(scriptsDir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isFile() && file.endsWith('.js')) {
      processFile(filePath);
    }
  }
}

// Run the script
console.log('Updating script configurations...');
processAllFiles();
console.log('Done!');
