import React, { useState, useEffect } from 'react';
import { ArrowRight, Video, BookOpen } from 'lucide-react';
import ProgressBar from './ProgressBar';
import { Link, useNavigate } from 'react-router-dom';
import { getCoursePlaceholderImage, isDataUrl, getCourseImageSource } from '@/utils/imageUtils';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { ResponsiveImage } from '@/components/ui/responsive-image';
import { useAuth } from '@/context/AuthContext';
import { enrollInCourse } from '@/services/course/enrollmentApi';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface CourseCardProps {
  id?: string;
  title?: string;
  description?: string;
  startDate?: string;
  progress?: number;
  image?: string;
  hasVideo?: boolean;
  isEnrolled?: boolean;
  // Allow passing a course object directly (for backward compatibility)
  course?: {
    id: string;
    title?: string;
    description?: string;
    startDate?: string;
    progress?: number;
    image?: string;
    hasVideo?: boolean;
    isEnrolled?: boolean;
    status?: string;
  };
}

const CourseCard: React.FC<CourseCardProps> = (props) => {
  // Handle both direct props and course object
  const {
    id: propId,
    title: propTitle = '',
    description: propDescription,
    startDate: propStartDate,
    progress: propProgress,
    image: propImage,
    hasVideo: propHasVideo = false,
    isEnrolled: propIsEnrolled = false,
    course
  } = props;

  // Use course object properties if provided, otherwise use direct props
  const id = course?.id || propId;
  const title = course?.title || propTitle || '';
  const description = course?.description || propDescription;
  const startDate = course?.startDate || propStartDate;
  const progress = course?.progress !== undefined ? course.progress : propProgress;
  const image = course?.image || propImage;
  const hasVideo = course?.hasVideo !== undefined ? course.hasVideo : propHasVideo;
  const isEnrolled = course?.isEnrolled !== undefined ? course.isEnrolled : propIsEnrolled;

  // Log which mode we're using
  console.log(`CourseCard for "${title}" using ${course ? 'course object' : 'direct props'} mode`);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [imageError, setImageError] = useState(false);
  const [renderError, setRenderError] = useState<Error | null>(null);
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Enrollment mutation
  const enrollMutation = useMutation({
    mutationFn: async () => {
      console.log('[COURSE CARD] Enrollment mutation triggered with:', {
        courseId: id,
        userId: user?.id,
        userObject: user,
        userEmail: user?.email
      });

      if (!id) {
        throw new Error('Course ID is missing');
      }

      if (!user?.id) {
        throw new Error('User ID is missing or user not authenticated');
      }

      // Double-check authentication by getting current session
      const { data: { session } } = await supabase.auth.getSession();
      console.log('[COURSE CARD] Current session check:', {
        hasSession: !!session,
        sessionUserId: session?.user?.id,
        contextUserId: user.id
      });

      if (!session?.user) {
        throw new Error('No active session found');
      }

      if (session.user.id !== user.id) {
        console.warn('[COURSE CARD] Session user ID mismatch:', {
          sessionUserId: session.user.id,
          contextUserId: user.id
        });
      }

      return enrollInCourse(id, user.id, 'in_progress');
    },
    onSuccess: () => {
      console.log('[COURSE CARD] Enrollment successful');
      toast.success('Successfully enrolled in course!');
      // Refresh dashboard data
      queryClient.invalidateQueries({
        queryKey: ['dashboard-courses', user?.id],
      });
      // Navigate to modules page
      navigate(`/course/${id}/modules`);
    },
    onError: (error) => {
      console.error('[COURSE CARD] Error enrolling in course:', error);
      toast.error(`Failed to enroll in course: ${error.message || 'Please try again.'}`);
    }
  });

  const handleCourseClick = () => {
    console.log('[COURSE CARD] Course click handler triggered with:', {
      courseId: id,
      userId: user?.id,
      isEnrolled,
      userAuthenticated: !!user
    });

    if (!id) {
      console.error('[COURSE CARD] Course ID is missing');
      toast.error('Unable to open course: ID is missing');
      return;
    }

    if (!user) {
      console.error('[COURSE CARD] User not authenticated');
      toast.error('Please log in to access this course');
      return;
    }

    // If user is not enrolled, automatically enroll them
    if (!isEnrolled && user?.id) {
      console.log('[COURSE CARD] User not enrolled, enrolling automatically...');
      enrollMutation.mutate();
    } else if (isEnrolled) {
      // If enrolled, navigate to modules page
      console.log('[COURSE CARD] User enrolled, navigating to modules');
      navigate(`/course/${id}/modules`);
    } else {
      // Fallback to course detail page
      console.log('[COURSE CARD] Fallback navigation to course detail');
      navigate(`/course/${id}`);
    }
  };

  // Determine the status badge style
  const getBadgeStyle = () => {
    if (startDate === 'Active') {
      return 'bg-primary/20 text-primary dark:bg-primary/30 dark:text-primary-foreground';
    } else if (startDate === 'Completed' && isEnrolled) {
      return 'bg-primary/30 text-primary dark:bg-primary/40 dark:text-primary-foreground';
    } else {
      return 'bg-muted text-muted-foreground';
    }
  };

  // Get appropriate image source using the utility function
  const getImageSource = () => {
    try {
      // Use the utility function to handle all image types
      return getCourseImageSource(image, title);
    } catch (error) {
      console.error('Error getting image source:', error);
      // Return placeholder image in case of error
      return getCoursePlaceholderImage(title);
    }
  };

  // Log image information on mount
  useEffect(() => {
    console.log(`CourseCard "${title}" (id: ${id}) has image:`, !!image);
    console.log(`CourseCard props:`, { id, title, description, startDate, progress, hasVideo, isEnrolled });
    if (image && image.startsWith('data:')) {
      console.log(`Course "${title}" has a data URL image of length:`, image.length);
    }
  }, [id, title, description, startDate, progress, image, hasVideo, isEnrolled]);

  const getActionText = () => {
    if (startDate === 'Active') {
      return 'Continue';
    } else if (startDate === 'Completed' && isEnrolled) {
      return 'Review';
    } else {
      return 'Start';
    }
  };

  try {
    return (
      <div
        className={cn(
          "group flex flex-col sm:flex-row overflow-hidden rounded-2xl border border-border/30 shadow-sm",
          "bg-gradient-to-br from-white via-white to-gray-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800/50",
          "transition-all duration-500 cursor-pointer backdrop-blur-sm",
          !isMobile && "hover:-translate-y-2 hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 dark:hover:border-primary/40"
        )}
        onClick={handleCourseClick}
      >
        {/* Image section - Enhanced with better gradients */}
        <div className="relative w-full sm:w-[320px] h-[200px] xs:h-[220px] sm:h-auto bg-gradient-to-br from-primary/8 via-primary/5 to-primary/12 dark:from-primary/15 dark:via-primary/8 dark:to-primary/20">
          {getImageSource() && !imageError ? (
            <div className="absolute inset-0">
              <ResponsiveImage
                src={getImageSource() || ''}
                alt={title || 'Course'}
                className="transition-all duration-700 group-hover:scale-110"
                objectFit="cover"
                aspectRatio={isMobile ? "16/9" : "4/3"}
                loadingStrategy="eager"
                placeholderColor="rgba(0,0,0,0.05)"
                containerClassName="w-full h-full"
                fallback={getCoursePlaceholderImage(title)}
                onError={() => setImageError(true)}
              />
            </div>
          ) : (
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/40 dark:from-primary/30 dark:to-primary/50 flex items-center justify-center mb-3 shadow-lg">
                <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 text-primary dark:text-primary-foreground" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-primary dark:text-primary-foreground">
                {title?.charAt(0).toUpperCase() || 'C'}
              </div>
            </div>
          )}

          {/* Video badge - Enhanced design */}
          {hasVideo && (
            <div className="absolute top-3 right-3 sm:top-4 sm:right-4 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground p-2 sm:p-2.5 rounded-xl shadow-lg backdrop-blur-sm">
              <Video className="w-4 h-4 sm:w-5 sm:h-5" />
            </div>
          )}

          {/* Enhanced gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-60" />
          <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/40 via-black/10 to-transparent" />
        </div>

        {/* Content section - Enhanced with better spacing and typography */}
        <div className="flex flex-col flex-1 p-4 sm:p-5 md:p-7">
          <div className="flex justify-between items-start gap-3 sm:gap-4 mb-3 sm:mb-5">
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold line-clamp-2 font-poppins bg-gradient-to-r from-red-500 via-red-600 to-red-700 bg-clip-text text-transparent leading-tight">
              {title || 'Untitled Course'}
            </h3>
            {startDate && (
              <span className={cn(
                "px-3 sm:px-4 py-1 sm:py-1.5 text-xs font-semibold rounded-full whitespace-nowrap shadow-sm",
                getBadgeStyle()
              )}>
                {startDate}
              </span>
            )}
          </div>

          <p className="text-sm sm:text-base text-muted-foreground/80 line-clamp-2 sm:line-clamp-3 mb-4 sm:mb-6 flex-1 leading-relaxed">
            {description || 'Discover comprehensive learning content designed to enhance your knowledge and skills.'}
          </p>

          {/* Enhanced Progress section */}
          <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Learning Progress</span>
              <span className="text-sm font-bold text-primary bg-primary/10 px-2 py-1 rounded-md">
                {typeof progress === 'number' ? Math.round(progress) : 0}%
              </span>
            </div>
            <div className="relative h-2 sm:h-2.5 w-full bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden shadow-inner">
              <div
                className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full transition-all duration-500 ease-out shadow-sm"
                style={{ width: `${typeof progress === 'number' ? Math.min(100, Math.max(0, progress)) : 0}%` }}
              />
              {/* Progress glow effect */}
              <div
                className="absolute top-0 h-full bg-gradient-to-r from-primary/50 to-transparent rounded-full blur-sm opacity-60"
                style={{ width: `${typeof progress === 'number' ? Math.min(100, Math.max(0, progress)) : 0}%` }}
              />
            </div>
          </div>

          {/* Enhanced Footer */}
          <div className="flex items-center justify-between pt-4 sm:pt-5 border-t border-gray-200/60 dark:border-gray-700/60">
            <div className="flex items-center gap-2">
              {isEnrolled && progress >= 100 && (
                <span className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-sm">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Completed
                </span>
              )}
              {!isEnrolled && (
                <span className="text-xs text-muted-foreground/70 font-medium">
                  Ready to start learning?
                </span>
              )}
            </div>

            <button
              onClick={(e) => {
                e.stopPropagation();
                console.log('[COURSE CARD] Button click handler triggered with:', {
                  courseId: id,
                  userId: user?.id,
                  isEnrolled,
                  userAuthenticated: !!user
                });

                if (!id) {
                  console.error('[COURSE CARD] Course ID is missing');
                  toast.error('Unable to open course: ID is missing');
                  return;
                }

                if (!user) {
                  console.error('[COURSE CARD] User not authenticated');
                  toast.error('Please log in to access this course');
                  return;
                }

                // Use the same logic as the card click
                if (!isEnrolled && user?.id) {
                  console.log('[COURSE CARD] User not enrolled, enrolling automatically...');
                  enrollMutation.mutate();
                } else if (isEnrolled) {
                  // If enrolled, navigate to modules page
                  console.log('[COURSE CARD] User enrolled, navigating to modules');
                  navigate(`/course/${id}/modules`);
                } else {
                  // Fallback to course detail page
                  console.log('[COURSE CARD] Fallback navigation to course detail');
                  navigate(`/course/${id}`);
                }
              }}
              disabled={enrollMutation.isPending}
              className={cn(
                "group relative flex items-center justify-center gap-2 sm:gap-3 px-5 sm:px-7 py-2.5 sm:py-3.5",
                "text-sm sm:text-base font-semibold rounded-xl shadow-lg",
                "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700",
                "text-white transition-all duration-300 transform",
                "hover:scale-105 hover:shadow-xl hover:shadow-red-500/25",
                "active:scale-95 min-w-[130px] sm:min-w-[150px]",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              )}
            >
              <span className="relative z-10 font-medium tracking-wide">
                {enrollMutation.isPending ? 'Enrolling...' : getActionText()}
              </span>
              <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300 relative z-10" />

              {/* Button glow effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-red-400 to-red-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl" />
            </button>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // Log the error
    console.error('Error rendering CourseCard:', error);

    // Set the error state so we can show a fallback UI
    if (error instanceof Error && !renderError) {
      setRenderError(error);
    }

    // Return a fallback UI
    return (
      <div className="modern-card p-5">
        <div className="text-primary">
          <h3 className="font-medium">Error displaying course</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">There was an error displaying this course card.</p>
          {id && (
            <button
              onClick={() => navigate(`/course/${id}`)}
              className="mt-3 text-sm text-primary hover:text-primary/80 dark:text-primary-foreground dark:hover:text-primary-foreground/90 hover:underline flex items-center"
            >
              View Course <ArrowRight className="ml-1 h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }
};

export default CourseCard;
