import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface GoogleFormPromptProps {
  onComplete: () => void;
}

export function GoogleFormPrompt({ onComplete }: GoogleFormPromptProps) {
  const [loading, setLoading] = useState(false);
  const { user, setUser } = useAuth();
  const googleFormUrl = 'https://docs.google.com/forms/d/e/1FAIpQLSfedXFY9CJpKBomWHvEPJ7I_ByHyL81OOHoHqhGteNxs4DuMw/viewform?usp=header';
  const [formWindow, setFormWindow] = useState<Window | null>(null);

  useEffect(() => {
    // Function to check if the form window is closed
    const checkFormCompletion = () => {
      if (formWindow && formWindow.closed) {
        // When the form window is closed, check with Google Forms API if the form is submitted
        // For now, we'll show a prompt asking if they've completed it
        const hasCompleted = window.confirm('Have you completed the questionnaire? Click OK only if you have submitted the form.');
        if (hasCompleted) {
          handleFormCompletion();
        } else {
          toast.error('Please complete the questionnaire before continuing.');
        }
      }
    };

    // Check every second if the form window is closed
    const interval = setInterval(checkFormCompletion, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [formWindow]);

  const handleOpenForm = () => {
    const newWindow = window.open(googleFormUrl, '_blank');
    setFormWindow(newWindow);
  };

  const handleFormCompletion = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Update user metadata to mark the form as completed
      const { data, error } = await supabase.auth.updateUser({
        data: {
          google_form_completed: true
        }
      });

      if (error) {
        console.error('Error updating user:', error);
        toast.error('Failed to update your profile. Please try again.');
      } else if (data.user) {
        // Also update the user_preferences table
        const { error: prefError } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: user.id,
            google_form_completed: true
          });

        if (prefError) {
          console.error('Error updating preferences:', prefError);
        }

        // Update local user state
        setUser?.(data.user);
        toast.success('Thank you for completing the questionnaire!');
        onComplete();
      }
    } catch (error) {
      console.error('Error marking form as completed:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Complete the Questionnaire</CardTitle>
        <CardDescription>
          Before accessing courses, please complete our questionnaire to help us understand your learning needs better.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Required Step</AlertTitle>
          <AlertDescription>
            You need to complete this questionnaire before you can access any courses on the platform.
          </AlertDescription>
        </Alert>
        
        <p>
          The questionnaire will take approximately 5-10 minutes to complete. Your responses will help us tailor our 
          content to better meet your educational needs.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 mt-6">
          <Button onClick={handleOpenForm} className="flex-1">
            <ExternalLink className="mr-2 h-4 w-4" />
            Open Questionnaire
          </Button>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center border-t pt-4">
        <p className="text-sm text-muted-foreground text-center">
          After completing the questionnaire, please submit the form and close the questionnaire window to proceed.
        </p>
      </CardFooter>
    </Card>
  );
}
