import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import Sidebar from './Sidebar';
import { PageTransition } from '@/components/ui/page-transition';
import NotificationsIndicator from './notifications/NotificationsIndicator';
import MainNavigation from './MainNavigation';
import OfflineQueueStatus from './OfflineQueueStatus';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScreenSize } from '@/hooks/use-mobile';
import '@/styles/desktop-enhancements.css';
import '@/styles/module-text-overrides.css';
import '@/styles/professional-ui.css';
import { toast } from 'sonner';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation();
  const { user } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const isMobile = useIsMobile(768); // Ensure consistent breakpoint
  const screenSize = useScreenSize();
  const isLargeScreen = screenSize === 'lg' || screenSize === 'xl' || screenSize === '2xl';
  const isNotDashboard = location.pathname !== '/dashboard';

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Open sidebar by default on large screens
  useEffect(() => {
    if (!isMobile) {
      setSidebarOpen(true);
    } else {
      setSidebarOpen(false);
    }
  }, [isMobile]);

  // Toggle sidebar function for mobile
  const toggleSidebar = () => {
    console.log("Layout: Toggling sidebar, current state:", sidebarOpen);
    setSidebarOpen(prev => !prev);
  };

  // Show notification about the UI change once
  /*
  useEffect(() => {
    if (!isMobile && !sessionStorage.getItem('nav-notification-shown')) {
      // Delay the notification a bit to let the UI render first
      const timer = setTimeout(() => {
        toast.info('Navigation has moved to the sidebar', {
          description: 'The top navigation bar is now only visible on mobile devices.',
          duration: 5000,
        });
        sessionStorage.setItem('nav-notification-shown', 'true');
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [isMobile]);
  */

  return (
    <div className={cn(
      "flex min-h-screen bg-background",
      isMobile && "overflow-x-hidden" // Prevent horizontal scroll on mobile
    )}>
      {/* Floating Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-[60]",
        isMobile ? [
          "w-0", // No width on mobile when closed
          sidebarOpen && "w-72 pointer-events-auto"
        ] : "w-72" // Fixed width on desktop for floating sidebar
      )}>
        <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />
      </div>

      {/* Main Content */}
      <div className={cn(
        "flex-1 transition-all duration-300 flex flex-col min-h-screen",
        isMobile ? "ml-0 w-full" : "ml-72" // Adjusted for floating sidebar width
      )}>
        {/* Mobile Header - Only displayed on mobile */}
        {isMobile && (
          <header className={cn(
            "sticky top-0 z-[55] w-full",
            "transition-all duration-300",
            isScrolled ? "bg-background/80 backdrop-blur-md shadow-sm border-b border-border/40" : ""
          )}>
            <div className="flex h-16 items-center px-4" style={{touchAction: 'manipulation'}}>
              {/* Back button on non-dashboard pages */}
              <div className="flex items-center gap-2 w-auto mr-2">
                {isNotDashboard && (
                  <button
                    onClick={() => window.history.back()}
                    className="rounded-full hover:bg-primary/5 text-muted-foreground hover:text-primary transition-all duration-200 p-1.5 z-[56]"
                    aria-label="Go back"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </button>
                )}
              </div>

              {/* Navigation with hamburger menu on mobile */}
              <div className="flex justify-center flex-1 z-[56]">
                <MainNavigation sidebarOpen={sidebarOpen} toggleSidebar={toggleSidebar} />
              </div>

              <div className="flex items-center justify-end ml-auto space-x-2 w-auto z-[56]">
                <OfflineQueueStatus />
              </div>
            </div>
          </header>
        )}

        {/* Main Content */}
        <main className={cn(
          "flex-1 min-h-0", // min-h-0 for proper flex behavior
          // Consistent spacing that accounts for floating sidebar - REDUCED for better design
          isMobile
            ? "px-4 py-4" // Mobile: standard padding
            : "px-4 py-4 pl-4" // Desktop: reduced left padding (pl-4 instead of pl-8) for tighter spacing
        )}>
          <PageTransition>
            {children}
          </PageTransition>
        </main>
      </div>
    </div>
  );
};

export default Layout;
