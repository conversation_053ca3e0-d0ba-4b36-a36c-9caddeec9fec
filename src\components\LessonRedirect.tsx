import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { PageLoadingSpinner } from '@/components/ui/loading-spinner';

const LessonRedirect: React.FC = () => {
  const { lessonSlug } = useParams<{ lessonSlug: string }>();
  const navigate = useNavigate();

  // Fetch lesson and course information
  const { data: lessonData, isLoading, error } = useQuery({
    queryKey: ['lesson-redirect', lessonSlug],
    queryFn: async () => {
      if (!lessonSlug) throw new Error('No lesson slug provided');

      const { data, error } = await supabase
        .from('lessons')
        .select(`
          slug,
          modules!inner (
            courses!inner (
              id
            )
          )
        `)
        .eq('slug', lessonSlug)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!lessonSlug,
  });

  useEffect(() => {
    if (lessonData && lessonData.modules?.courses?.id) {
      const courseId = lessonData.modules.courses.id;
      navigate(`/course/${courseId}/lesson/${lessonSlug}`, { replace: true });
    }
  }, [lessonData, lessonSlug, navigate]);

  if (isLoading) {
    return <PageLoadingSpinner text="Redirecting to lesson..." />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-2">Lesson Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The lesson "{lessonSlug}" could not be found.
          </p>
          <button 
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return <PageLoadingSpinner text="Redirecting..." />;
};

export default LessonRedirect;
