
import React from 'react';

interface ProgressBarProps {
  value: number;
  maxValue?: number;
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'default' | 'blue' | 'green' | 'amber';
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  maxValue = 100,
  className = '',
  showLabel = false,
  size = 'md',
  color = 'default'
}) => {
  const percentage = Math.min(Math.max(0, (value / maxValue) * 100), 100);

  const getHeight = () => {
    switch (size) {
      case 'sm': return 'h-1';
      case 'lg': return 'h-3';
      default: return 'h-2';
    }
  };

  const getColor = () => {
    switch (color) {
      case 'blue': return 'bg-primary';
      case 'green': return 'bg-primary';
      case 'amber': return 'bg-primary';
      default: return 'bg-primary';
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="relative w-full bg-secondary rounded-full overflow-hidden">
        <div
          className={`${getHeight()} ${getColor()} rounded-full progress-animation`}
          style={{ width: `${percentage}%` }}
        />
      </div>

      {showLabel && (
        <div className="mt-1 text-xs text-muted-foreground">
          {percentage.toFixed(0)}% Complete
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
