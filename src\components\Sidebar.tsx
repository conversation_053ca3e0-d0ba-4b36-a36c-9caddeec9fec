import React, { useEffect, useState } from 'react';
import { NavLink, useLocation, Link } from 'react-router-dom';
import {
  LayoutDashboard,
  Settings,
  BookOpen,
  GraduationCap,
  X,
  Home,
  LogOut,
  Award,
  Moon,
  Sun,
  ChevronRight
} from 'lucide-react';
import { useUserRole } from '@/hooks/useUserRole';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { getProfile } from '@/lib/profile';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { useTheme } from '@/components/theme/enhanced-theme-provider';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  updated_at: string | null;
}

// Define navigation items
function getNavItems(isTeacher: boolean) {
  const items = [
    {
      path: '/dashboard',
      label: 'Programmes',
      icon: <LayoutDashboard className="h-5 w-5" />
    },
    {
      path: '/my-courses',
      label: 'Modules',
      icon: <BookOpen className="h-5 w-5" />
    },
    {
      path: '/achievements',
      label: 'Certificates',
      icon: <Award className="h-5 w-5" />
    }
  ];

  if (isTeacher) {
    items.push({
      path: '/admin',
      label: 'Admin',
      icon: <Settings className="h-5 w-5" />
    });
  }

  return items;
}

interface SidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ open, setOpen }) => {
  const location = useLocation();
  const { user, signOut } = useAuth();
  const { isTeacher } = useUserRole();
  const isMobile = useIsMobile(768); // Ensure consistent breakpoint
  const [profile, setProfile] = useState<Profile | null>(null);
  const { theme, setTheme } = useTheme();
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [touchEndX, setTouchEndX] = useState<number | null>(null);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isMobile) {
      setOpen(false);
    }
  }, [location.pathname, isMobile, setOpen]);

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isMobile && open) {
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    } else {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    };
  }, [isMobile, open]);

  // Ensure sidebar state is tracked in localStorage
  useEffect(() => {
    // Save sidebar state to localStorage when it changes
    if (isMobile) {
      localStorage.setItem('sidebar-open', open ? 'true' : 'false');
    }
  }, [open, isMobile]);

  // Fetch user profile
  useEffect(() => {
    const loadProfile = async () => {
      if (user) {
        try {
          const profileData = await getProfile(user.id);
          setProfile(profileData as unknown as Profile);
        } catch (error) {
          console.error('Error loading profile:', error);
        }
      }
    };
    loadProfile();
  }, [user]);

  // Handle swipe to close on mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEndX(e.touches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStartX || !touchEndX) return;

    const distance = touchStartX - touchEndX;
    const isLeftSwipe = distance > 50; // Minimum swipe distance of 50px

    if (isLeftSwipe && isMobile) {
      setOpen(false);
    }

    // Reset touch coordinates
    setTouchStartX(null);
    setTouchEndX(null);
  };

  // Handle clicks outside the sidebar on mobile
  useEffect(() => {
    if (!isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      // Check if sidebar is open and click is outside the sidebar
      if (open && event.target instanceof Element) {
        const sidebarElement = document.querySelector('aside');
        if (sidebarElement && !sidebarElement.contains(event.target)) {
          setOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobile, open, setOpen]);

  const displayName = profile?.first_name
    ? `${profile.first_name} ${profile.last_name || ''}`
    : user?.user_metadata?.name || user?.email?.split('@')[0] || 'User';
  const avatarFallback = displayName.substring(0, 2).toUpperCase();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out');
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // Log when sidebar open state changes
  console.log('Sidebar open state:', open);

  return (
    <>
      {/* Mobile backdrop/overlay when sidebar is open */}
      {isMobile && open && (
        <div
          className="fixed inset-0 bg-black/30 dark:bg-black/40 backdrop-blur-md z-40 md:hidden touch-none modern-sidebar-backdrop"
          onClick={() => setOpen(false)}
          onTouchStart={(e) => e.preventDefault()}
          aria-hidden="true"
          style={{ touchAction: 'none' }}
        />
      )}

      <aside
        className={cn(
          "h-full flex flex-col z-50 modern-sidebar floating-sidebar",
          "bg-[#e63946]/95 backdrop-blur-xl text-white shadow-2xl border border-white/10",
          isMobile ? [
            "fixed inset-y-0 left-0 w-64",
            "transition-all duration-300 ease-out transform",
            "m-2 h-[calc(100vh-1rem)] rounded-2xl",
            open ? "translate-x-0" : "-translate-x-full"
          ] : "relative w-full translate-x-0 m-3 h-[calc(100vh-1.5rem)] rounded-2xl"
        )}
        style={{
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        data-state={open ? "open" : "closed"}
      >
        {/* Header with logo and mobile close button */}
        <div className={cn(
          "flex items-center justify-between border-b border-white/10 bg-white/5 backdrop-blur-sm header-section",
          isMobile ? "px-5 h-16" : "px-6 h-20"
        )}>
          <Link
            to="/dashboard"
            className="flex items-center gap-3 hover:opacity-80 transition-opacity group logo-container"
            aria-label="Go to dashboard - e4mi logo"
          >
            <div className={cn(
              "bg-white/10 rounded-xl group-hover:bg-white/15 transition-colors logo-icon",
              isMobile ? "p-1.5" : "p-2"
            )}>
              <GraduationCap className={cn(isMobile ? "h-5 w-5" : "h-6 w-6")} aria-hidden="true" />
            </div>
            <span className={cn(
              "font-bold tracking-tight",
              isMobile ? "text-lg" : "text-xl"
            )}>e4mi</span>
          </Link>

          {isMobile && (
            <button
              className="p-2 rounded-xl hover:bg-white/10 active:bg-white/15 transition-all duration-200 active:scale-95"
              onClick={() => setOpen(false)}
              aria-label="Close navigation menu"
              type="button"
            >
              <X className="h-4 w-4" aria-hidden="true" />
            </button>
          )}
        </div>

        {/* User profile section */}
        <div className={cn(
          "border-b border-white/10 bg-white/5 profile-section",
          isMobile ? "px-5 py-4" : "px-6 py-5"
        )}>
          <div className={cn("flex items-center", isMobile ? "gap-3" : "gap-4")}>
            <Avatar className={cn(
              "border-2 border-white/20 shadow-lg profile-avatar",
              isMobile ? "h-10 w-10" : "h-12 w-12"
            )}>
              {profile?.avatar_url ? (
                <AvatarImage src={profile.avatar_url} alt={displayName} />
              ) : (
                <AvatarFallback className={cn(
                  "bg-white/15 text-white font-semibold",
                  isMobile ? "text-sm" : "text-base"
                )}>{avatarFallback}</AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className={cn(
                "font-semibold truncate leading-tight",
                isMobile ? "text-sm" : "text-base"
              )}>{displayName}</div>
              <div className={cn(
                "truncate text-white/80 mt-0.5",
                isMobile ? "text-xs" : "text-sm"
              )}>{user?.email}</div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className={cn(
          "flex-1 overflow-y-auto",
          isMobile ? "px-3 py-5" : "px-4 py-6"
        )} role="navigation" aria-label="Sidebar navigation">
          <div className={cn(
            "mb-4 px-2 font-semibold uppercase tracking-wider text-white/70 section-header",
            isMobile ? "text-xs" : "text-xs"
          )}>
            Navigation
          </div>
          <ul className={cn("role-list", isMobile ? "space-y-1" : "space-y-2")}>
            {getNavItems(isTeacher).map((item) => (
              <li key={item.path} role="listitem">
                <NavLink
                  to={item.path}
                  className={({ isActive }) => cn(
                    "flex items-center rounded-xl font-medium transition-all text-white focus:outline-none focus:ring-2 focus:ring-white/50 group relative overflow-hidden nav-item",
                    isMobile ? "gap-2.5 px-3 py-2.5" : "gap-3 px-4 py-3",
                    isActive
                      ? "bg-white/20 shadow-lg backdrop-blur-sm active"
                      : "hover:bg-white/10 hover:translate-x-1"
                  )}
                  onClick={() => isMobile && setOpen(false)}
                >
                  {({ isActive }) => (
                    <>
                      <span aria-hidden="true" className="relative z-10 nav-icon">{item.icon}</span>
                      <span className={cn(
                        "relative z-10",
                        isMobile ? "text-sm" : "text-base"
                      )}>{item.label}</span>
                      {!isActive && (
                        <ChevronRight className={cn(
                          "ml-auto opacity-0 group-hover:opacity-50 transition-opacity relative z-10",
                          isMobile ? "h-3.5 w-3.5" : "h-4 w-4"
                        )} />
                      )}
                    </>
                  )}
                </NavLink>
              </li>
            ))}
          </ul>


        </nav>

        {/* Footer with theme toggle and sign out */}
        <div className="mt-auto border-t border-white/10 bg-white/5 footer-section">
          <div className={cn(
            "space-y-3",
            isMobile ? "px-5 py-4" : "px-6 py-5"
          )}>
            {/* Simple theme toggle */}
            <button
              onClick={toggleTheme}
              className={cn(
                "flex items-center justify-between w-full text-white hover:bg-white/10 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-white/50 group theme-toggle",
                isMobile ? "px-3 py-2.5" : "px-4 py-3"
              )}
              aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
              type="button"
            >
              <div className={cn("flex items-center", isMobile ? "gap-2.5" : "gap-3")}>
                {theme === 'dark' ? (
                  <Moon className={cn(isMobile ? "h-4 w-4" : "h-5 w-5")} aria-hidden="true" />
                ) : (
                  <Sun className={cn(isMobile ? "h-4 w-4" : "h-5 w-5")} aria-hidden="true" />
                )}
                <span className={cn(
                  "font-medium",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  {theme === 'dark' ? 'Dark Mode' : 'Light Mode'}
                </span>
              </div>
              <div
                className={cn(
                  "rounded-full bg-white/20 relative flex items-center shadow-inner theme-toggle-switch",
                  isMobile ? "h-5 w-9" : "h-6 w-11"
                )}
                role="switch"
                aria-checked={theme === 'dark'}
              >
                <div
                  className={cn(
                    "rounded-full bg-white absolute transition-all duration-300 shadow-sm theme-toggle-thumb",
                    isMobile ? "h-4 w-4" : "h-5 w-5",
                    theme === 'dark' ? "right-0.5" : "left-0.5"
                  )}
                />
              </div>
            </button>

            <button
              onClick={handleSignOut}
              className={cn(
                "flex items-center w-full text-white hover:bg-white/10 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-white/50 group sign-out-btn",
                isMobile ? "gap-2.5 px-3 py-2.5" : "gap-3 px-4 py-3"
              )}
              aria-label="Sign out of your account"
              type="button"
            >
              <LogOut className={cn(isMobile ? "h-4 w-4" : "h-5 w-5")} aria-hidden="true" />
              <span className={cn(
                "font-medium",
                isMobile ? "text-sm" : "text-base"
              )}>Sign Out</span>
            </button>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
