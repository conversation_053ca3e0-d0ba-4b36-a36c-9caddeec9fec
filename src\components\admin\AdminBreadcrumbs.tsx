import React from 'react';
import { Link } from 'react-router-dom';
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Book, BookOpen, FileText, Home } from 'lucide-react';

export interface BreadcrumbData {
  id?: string;
  title?: string;
  type: 'course' | 'module' | 'lesson';
  parentId?: string;
  parentTitle?: string;
  parentType?: 'course' | 'module';
}

interface AdminBreadcrumbsProps {
  data?: BreadcrumbData;
  className?: string;

  // Legacy props from AdminBreadcrumb.tsx
  selectedCourseId?: string | null;
  selectedModuleId?: string | null;
  selectedLessonId?: string | null;
  selectedCourse?: any;
  selectedModule?: any;
  handleBackToCoursesList?: () => void;
  handleBackToCourse?: () => void;
  setSelectedLessonId?: (id: string | null) => void;
}

export function AdminBreadcrumbs({
  data,
  className,
  // Legacy props
  selectedCourseId,
  selectedModuleId,
  selectedLessonId,
  selectedCourse,
  selectedModule,
  handleBackToCoursesList,
  handleBackToCourse,
  setSelectedLessonId
}: AdminBreadcrumbsProps) {
  // Determine the icon based on content type
  const getIcon = (type: string) => {
    switch (type) {
      case 'course':
        return <Book className="h-4 w-4 mr-1 inline" />;
      case 'module':
        return <BookOpen className="h-4 w-4 mr-1 inline" />;
      case 'lesson':
        return <FileText className="h-4 w-4 mr-1 inline" />;
      default:
        return <Home className="h-4 w-4 mr-1 inline" />;
    }
  };

  // Helper function to render breadcrumbs with either standard or legacy behavior
  const renderBreadcrumbs = (breadcrumbData: BreadcrumbData, customClassName?: string, isLegacy = false) => {
    return (
      <Breadcrumb className={customClassName || className || 'mb-4'}>
        <BreadcrumbList>
          <BreadcrumbItem>
            {isLegacy && handleBackToCoursesList ? (
              <BreadcrumbLink onClick={handleBackToCoursesList} className="cursor-pointer">
                All Courses
              </BreadcrumbLink>
            ) : (
              <BreadcrumbLink asChild>
                <Link to={`/admin?tab=courses`}>
                  {getIcon(breadcrumbData.parentType || 'home')}
                  All Courses
                </Link>
              </BreadcrumbLink>
            )}
          </BreadcrumbItem>

          {breadcrumbData.parentId && breadcrumbData.parentTitle && breadcrumbData.parentType && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                {isLegacy && handleBackToCourse ? (
                  <BreadcrumbLink onClick={handleBackToCourse} className="cursor-pointer">
                    {breadcrumbData.parentTitle}
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={`/admin?tab=${breadcrumbData.parentType === 'course' ? 'courses' : 'modules'}&${breadcrumbData.parentType}Id=${breadcrumbData.parentId}`}>
                      {getIcon(breadcrumbData.parentType)}
                      {breadcrumbData.parentTitle}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </>
          )}

          {breadcrumbData.title && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                {isLegacy && breadcrumbData.type === 'module' && setSelectedLessonId ? (
                  <BreadcrumbLink onClick={() => setSelectedLessonId(null)} className="cursor-pointer">
                    {breadcrumbData.title}
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>
                    {getIcon(breadcrumbData.type)}
                    {breadcrumbData.title}
                  </BreadcrumbPage>
                )}
              </BreadcrumbItem>
            </>
          )}
        </BreadcrumbList>
      </Breadcrumb>
    );
  };

  // Check if we're using legacy props
  const isLegacyMode = selectedCourseId !== undefined;

  // If using legacy mode, convert legacy props to new data format
  if (isLegacyMode) {
    // Only proceed with legacy mode if we have the required props
    if (selectedCourseId || selectedModuleId || selectedLessonId) {
      // Create breadcrumb data from legacy props
      let legacyData: BreadcrumbData | undefined;

      if (selectedLessonId) {
        // Lesson level
        legacyData = {
          id: selectedLessonId,
          title: selectedLessonId === 'new' ? 'New Lesson' : 'Lesson',
          type: 'lesson',
          parentId: selectedModuleId || undefined,
          parentTitle: selectedModule?.title || 'Module',
          parentType: 'module'
        };
      } else if (selectedModuleId) {
        // Module level
        legacyData = {
          id: selectedModuleId,
          title: selectedModuleId === 'new' ? 'New Module' : (selectedModule?.title || 'Module'),
          type: 'module',
          parentId: selectedCourseId || undefined,
          parentTitle: selectedCourse?.title || 'Course',
          parentType: 'course'
        };
      } else if (selectedCourseId) {
        // Course level
        legacyData = {
          id: selectedCourseId,
          title: selectedCourseId === 'new' ? 'New Course' : (selectedCourse?.title || 'Course'),
          type: 'course'
        };
      }

      // Render with the legacy data
      if (legacyData) {
        return renderBreadcrumbs(legacyData, className, true);
      }
    }

    // If no legacy data could be created, return null
    return null;
  }

  // Standard mode (no legacy props)
  if (!data) {
    return (
      <Breadcrumb className={className}>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link to="/admin">
                <Home className="h-4 w-4 mr-1 inline" />
                Admin
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Render with the provided data
  return renderBreadcrumbs(data);
}

export default AdminBreadcrumbs;
