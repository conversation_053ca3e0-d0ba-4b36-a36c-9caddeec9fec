import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { assignTeacherRole } from '@/services/course/adminApi';
import { Loader2 } from 'lucide-react';

const AssignTeacherRole: React.FC = () => {
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleAssignRole = async () => {
    if (!userId) return;

    setIsLoading(true);
    setResult(null);

    try {
      const success = await assignTeacherRole(userId);

      if (success) {
        setResult({
          success: true,
          message: `Teacher role successfully assigned to user ${userId}`
        });
      } else {
        setResult({
          success: false,
          message: 'Failed to assign teacher role. Please check the user ID and try again.'
        });
      }
    } catch (error: any) {
      setResult({
        success: false,
        message: `Error: ${error.message || 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Assign Teacher Role</CardTitle>
        <CardDescription>
          Enter a user ID to assign the teacher role. This will give the user permission to manage course content.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="User ID"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            disabled={isLoading}
          />
          <Button onClick={handleAssignRole} disabled={!userId || isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Assign Role
          </Button>
        </div>

        {result && (
          <div className={`mt-4 p-3 rounded-md ${result.success ? 'bg-red-50 text-red-800' : 'bg-red-50 text-red-800'}`}>
            {result.message}
          </div>
        )}
      </CardContent>
      <CardFooter className="text-sm text-gray-500">
        <p>
          You can find a user's ID in the Supabase dashboard under Authentication {'>'} Users.
        </p>
      </CardFooter>
    </Card>
  );
};

export default AssignTeacherRole;
