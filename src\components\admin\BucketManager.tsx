import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, X, Database, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { SUPABASE_CONFIG } from '@/config/supabase';
import { useToast } from '@/hooks/use-toast';

interface BucketStatus {
  name: string;
  exists: boolean;
  isChecking: boolean;
  isCreating: boolean;
}

export function BucketManager() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [buckets, setBuckets] = useState<BucketStatus[]>([
    { name: SUPABASE_CONFIG.STORAGE.COURSE_IMAGES, exists: false, isChecking: false, isCreating: false },
    { name: SUPABASE_CONFIG.STORAGE.AVATARS, exists: false, isChecking: false, isCreating: false },
    { name: SUPABASE_CONFIG.STORAGE.APP_UPLOADS, exists: false, isChecking: false, isCreating: false },
    { name: 'uploads', exists: false, isChecking: false, isCreating: false } // Additional bucket
  ]);

  // Check if buckets exist
  const checkBuckets = async () => {
    setIsLoading(true);

    try {
      const { data: existingBuckets, error } = await supabase.storage.listBuckets();

      if (error) {
        console.error('Error listing buckets:', error);
        toast({
          title: 'Error',
          description: `Failed to list buckets: ${error.message}`,
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }

      const existingBucketNames = existingBuckets?.map(b => b.name) || [];

      setBuckets(prev => prev.map(bucket => ({
        ...bucket,
        exists: existingBucketNames.includes(bucket.name)
      })));

      toast({
        title: 'Buckets Checked',
        description: `Found ${existingBucketNames.length} buckets.`
      });
    } catch (error: any) {
      console.error('Error checking buckets:', error);
      toast({
        title: 'Error',
        description: `An unexpected error occurred: ${error.message}`,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a bucket
  const createBucket = async (bucketName: string) => {
    // Update bucket status
    setBuckets(prev => prev.map(bucket =>
      bucket.name === bucketName
        ? { ...bucket, isCreating: true }
        : bucket
    ));

    try {
      const { data, error } = await supabase.storage.createBucket(bucketName, {
        public: true
      });

      if (error) {
        console.error(`Error creating bucket '${bucketName}':`, error);
        toast({
          title: 'Error',
          description: `Failed to create bucket '${bucketName}': ${error.message}`,
          variant: 'destructive'
        });

        // Update bucket status
        setBuckets(prev => prev.map(bucket =>
          bucket.name === bucketName
            ? { ...bucket, isCreating: false }
            : bucket
        ));

        return;
      }

      // Update bucket status
      setBuckets(prev => prev.map(bucket =>
        bucket.name === bucketName
          ? { ...bucket, exists: true, isCreating: false }
          : bucket
      ));

      toast({
        title: 'Bucket Created',
        description: `Successfully created bucket '${bucketName}'.`
      });
    } catch (error: any) {
      console.error(`Error creating bucket '${bucketName}':`, error);
      toast({
        title: 'Error',
        description: `An unexpected error occurred: ${error.message}`,
        variant: 'destructive'
      });

      // Update bucket status
      setBuckets(prev => prev.map(bucket =>
        bucket.name === bucketName
          ? { ...bucket, isCreating: false }
          : bucket
      ));
    }
  };

  // Create all missing buckets
  const createAllMissingBuckets = async () => {
    setIsLoading(true);

    try {
      const missingBuckets = buckets.filter(bucket => !bucket.exists);

      for (const bucket of missingBuckets) {
        await createBucket(bucket.name);
      }

      toast({
        title: 'Buckets Created',
        description: `Attempted to create ${missingBuckets.length} missing buckets.`
      });
    } catch (error: any) {
      console.error('Error creating buckets:', error);
      toast({
        title: 'Error',
        description: `An unexpected error occurred: ${error.message}`,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" /> Storage Bucket Manager
        </CardTitle>
        <CardDescription>
          Manage Supabase storage buckets required for the application
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-sm font-medium">Storage Buckets</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={checkBuckets}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Check Buckets
            </Button>
          </div>

          <div className="space-y-2">
            {buckets.map(bucket => (
              <div
                key={bucket.name}
                className="flex justify-between items-center p-3 rounded-md border"
              >
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm">{bucket.name}</span>
                  {bucket.exists ? (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <Check className="h-3 w-3 mr-1" /> Exists
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                      <X className="h-3 w-3 mr-1" /> Missing
                    </Badge>
                  )}
                </div>

                {!bucket.exists && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => createBucket(bucket.name)}
                    disabled={bucket.isCreating}
                  >
                    {bucket.isCreating ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Database className="h-3 w-3 mr-1" />
                    )}
                    Create
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={createAllMissingBuckets}
          disabled={isLoading || buckets.every(b => b.exists)}
          className="w-full"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Database className="h-4 w-4 mr-2" />
          )}
          Create All Missing Buckets
        </Button>
      </CardFooter>
    </Card>
  );
}
