import React from 'react';
import { AlertCircle, Image, FileImage, Check, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from '@/components/ui/button';

export function ImageUploadGuide() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="mt-2">
          <FileImage className="h-4 w-4 mr-2" />
          Image Upload Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Image className="h-5 w-5 mr-2 text-primary" />
            Course Image Guidelines
          </DialogTitle>
          <DialogDescription>
            Follow these guidelines for the best results when uploading course images.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Recommended Image Specifications</h3>
            <ul className="text-sm space-y-2">
              <li className="flex items-start">
                <Check className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span><strong>Size:</strong> Under 2MB (smaller is better)</span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span><strong>Dimensions:</strong> 800×600 pixels or 16:9 aspect ratio</span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span><strong>Format:</strong> JPEG or PNG (JPEG recommended)</span>
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">What to Avoid</h3>
            <ul className="text-sm space-y-2">
              <li className="flex items-start">
                <X className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span>Very large images (over 5MB)</span>
              </li>
              <li className="flex items-start">
                <X className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span>Extremely high resolution images (e.g., 4K or higher)</span>
              </li>
              <li className="flex items-start">
                <X className="h-4 w-4 mr-2 text-red-600 mt-0.5" />
                <span>Complex images with lots of details</span>
              </li>
            </ul>
          </div>

          <div className="bg-amber-50 p-3 rounded-md text-sm text-amber-800 flex items-start">
            <AlertCircle className="h-4 w-4 mr-2 text-amber-600 mt-0.5" />
            <div>
              <p className="font-medium">Important Note</p>
              <p className="mt-1">
                Images are stored directly in the database and will be automatically compressed if they're too large.
                For best quality, start with appropriately sized images.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
