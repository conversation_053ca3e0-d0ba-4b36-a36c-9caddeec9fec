import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';

// Define the form schema
const lessonFormSchema = z.object({
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  content: z.string().min(10, { message: 'Content must be at least 10 characters' }),
  courseId: z.string({ required_error: 'Course is required' }),
  moduleId: z.string().optional(),
  order: z.number().int().positive().optional(),
});

type LessonFormValues = z.infer<typeof lessonFormSchema>;

interface LessonFormProps {
  initialData?: LessonFormValues & { id?: string };
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function LessonForm({ initialData, onSuccess, onCancel }: LessonFormProps) {
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [courses, setCourses] = useState<any[]>([]);
  const [modules, setModules] = useState<any[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState<string | undefined>(initialData?.courseId);
  const [editorContent, setEditorContent] = useState(initialData?.content || '');

  // Initialize form with default values
  const form = useForm<LessonFormValues>({
    resolver: zodResolver(lessonFormSchema),
    defaultValues: initialData || {
      title: '',
      description: '',
      content: '',
      courseId: '',
      moduleId: '',
      order: 1,
    },
  });

  // Fetch courses on component mount
  useEffect(() => {
    const fetchCourses = async () => {
      const { data, error } = await supabase
        .from('courses')
        .select('id, title')
        .order('title');

      if (error) {
        console.error('Error loading courses:', error);
        toast.error('Failed to load courses');
        return;
      }

      setCourses(data || []);
    };

    fetchCourses();
  }, []);

  // Fetch modules when course is selected
  useEffect(() => {
    if (!selectedCourseId) {
      setModules([]);
      return;
    }

    const fetchModules = async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('id, title')
        .eq('course_id', selectedCourseId)
        .order('order');

      if (error) {
        toast.error('Failed to load modules');
        return;
      }

      setModules(data || []);
    };

    fetchModules();
  }, [selectedCourseId]);

  // Handle course selection change
  const handleCourseChange = (courseId: string) => {
    setSelectedCourseId(courseId);
    form.setValue('courseId', courseId);
    form.setValue('moduleId', ''); // Reset module when course changes
  };

  // Handle editor content change
  const handleEditorChange = (content: string) => {
    setEditorContent(content);
    form.setValue('content', content);
  };

  // Handle form submission
  const onSubmit = async (data: LessonFormValues) => {
    setIsSubmitting(true);

    try {
      // Prepare lesson data
      const lessonData = {
        title: data.title,
        description: data.description,
        content: editorContent,
        course_id: data.courseId,
        module_id: data.moduleId || null,
        order: data.order || 1,
      };

      // Update or create lesson
      if (initialData?.id) {
        // Update existing lesson
        const { error } = await supabase
          .from('lessons')
          .update(lessonData)
          .eq('id', initialData.id);

        if (error) throw error;

        toast.success('Lesson updated');
      } else {
        // Create new lesson
        const { error } = await supabase
          .from('lessons')
          .insert([lessonData]);

        if (error) throw error;

        toast.success('Lesson created');
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['lessons'] });

      // Call success callback
      if (onSuccess) onSuccess();

    } catch (error: any) {
      toast.error(error.message || 'An error occurred while saving the lesson.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="basic">Basic Information</TabsTrigger>
          <TabsTrigger value="content">Lesson Content</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          {/* Course Selection */}
          <div className="space-y-2">
            <Label htmlFor="courseId">
              Course <span className="text-destructive">*</span>
            </Label>
            <Select
              value={form.getValues('courseId')}
              onValueChange={handleCourseChange}
            >
              <SelectTrigger id="courseId">
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                {courses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.courseId && (
              <p className="text-sm text-destructive">{form.formState.errors.courseId.message}</p>
            )}
          </div>

          {/* Module Selection (optional) */}
          <div className="space-y-2">
            <Label htmlFor="moduleId">Module (Optional)</Label>
            <Select
              value={form.getValues('moduleId')}
              onValueChange={(value) => form.setValue('moduleId', value)}
              disabled={!selectedCourseId || modules.length === 0}
            >
              <SelectTrigger id="moduleId">
                <SelectValue placeholder="Select a module" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">None (Top Level)</SelectItem>
                {modules.map((module) => (
                  <SelectItem key={module.id} value={module.id}>
                    {module.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Lesson Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Lesson Title <span className="text-destructive">*</span>
            </Label>
            <Input
              id="title"
              {...form.register('title')}
              placeholder="Enter lesson title"
            />
            {form.formState.errors.title && (
              <p className="text-sm text-destructive">{form.formState.errors.title.message}</p>
            )}
          </div>

          {/* Lesson Description */}
          <div className="space-y-2">
            <Label htmlFor="description">
              Description <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="description"
              {...form.register('description')}
              placeholder="Enter lesson description"
              rows={3}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
            )}
          </div>

          {/* Lesson Order */}
          <div className="space-y-2">
            <Label htmlFor="order">Order</Label>
            <Input
              id="order"
              type="number"
              min="1"
              {...form.register('order', { valueAsNumber: true })}
              placeholder="Enter lesson order"
            />
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          {/* Lesson Content */}
          <div className="space-y-2">
            <Label htmlFor="content">
              Lesson Content <span className="text-destructive">*</span>
            </Label>
            <div className="min-h-[400px]">
              <SimpleMarkdownEditor
                initialContent={editorContent}
                onChange={handleEditorChange}
                minHeight={400}
                placeholder="Write your lesson content here..."
                courseId={form.getValues('courseId')}
                moduleId={form.getValues('moduleId')}
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Rich text editor with Markdown output. Use the toolbar for formatting or switch to preview mode to see the rendered content.
            </p>
            {form.formState.errors.content && (
              <p className="text-sm text-destructive">{form.formState.errors.content.message}</p>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Form Actions */}
      <div className="flex justify-end gap-2 pt-4 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            initialData?.id ? 'Update Lesson' : 'Create Lesson'
          )}
        </Button>
      </div>
    </form>
  );
}
