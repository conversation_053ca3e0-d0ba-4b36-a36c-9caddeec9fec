import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, Trash2, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { createDefaultModuleTest, createDefaultRatingQuestion, ModuleTest, RatingQuestion } from '@/types/module-test';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { getModuleTestById, createModuleTest, updateModuleTest } from '@/services/module-test/moduleTestService';

interface ModuleTestEditorProps {
  moduleId: string;
  testId?: string;
  testType?: 'pre_test' | 'post_test';
  onClose: () => void;
}

const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  type: z.enum(['pre_test', 'post_test']),
  questions: z.array(
    z.object({
      id: z.string(),
      type: z.literal('rating'),
      question: z.string().min(1, 'Question text is required'),
      questionNumber: z.number(),
      minRating: z.number(),
      maxRating: z.number(),
      minLabel: z.string(),
      maxLabel: z.string()
    })
  )
});

type FormValues = z.infer<typeof formSchema>;

const ModuleTestEditor: React.FC<ModuleTestEditorProps> = ({ moduleId, testId, testType = 'pre_test', onClose }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [questions, setQuestions] = useState<RatingQuestion[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get default values for the form
  const getDefaultValues = () => {
    if (!testId) {
      const defaultTest = createDefaultModuleTest(moduleId, testType);
      return {
        title: defaultTest.title || '',
        description: defaultTest.description || '',
        type: testType,
        questions: []
      };
    }
    return {
      title: '',
      description: '',
      type: testType,
      questions: []
    };
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues()
  });

  // Fetch existing test if editing
  const { isLoading: isLoadingTest } = useQuery({
    queryKey: ['module-test', testId],
    queryFn: async () => {
      if (!testId) return null;

      try {
        const test = await getModuleTestById(testId);
        
        // Ensure questions are of type RatingQuestion with all required properties
        const typedQuestions: RatingQuestion[] = test.questions.map(q => ({
          id: q.id,
          type: 'rating',
          question: q.question,
          questionNumber: q.questionNumber,
          minRating: q.minRating,
          maxRating: q.maxRating,
          minLabel: q.minLabel,
          maxLabel: q.maxLabel
        }));
        
        // Set form values
        form.reset({
          title: test.title,
          description: test.description || '',
          type: test.type,
          questions: typedQuestions
        });

        // Update local questions state
        setQuestions(typedQuestions);

        return test;
      } catch (error) {
        console.error('Error fetching module test:', error);
        toast({
          title: "Error",
          description: "Failed to load test data",
          variant: "destructive"
        });
        return null;
      }
    },
    enabled: !!testId
  });

  // Create a new test
  const createTestMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      return createModuleTest({
        moduleId: moduleId,
        title: values.title,
        type: values.type,
        description: values.description,
        questions: values.questions
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Test created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['module-tests', moduleId] });
      onClose();
    },
    onError: (error: any) => {
      console.error('Error creating test:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create test",
        variant: "destructive"
      });
    }
  });

  // Update an existing test
  const updateTestMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      if (!testId) throw new Error('Test ID is required for update');

      return updateModuleTest(testId, {
        title: values.title,
        description: values.description,
        questions: values.questions
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Test updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['module-tests', moduleId] });
      queryClient.invalidateQueries({ queryKey: ['module-test', testId] });
      onClose();
    },
    onError: (error: any) => {
      console.error('Error updating test:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update test",
        variant: "destructive"
      });
    }
  });

  // Initialize with a default question if creating a new test
  useEffect(() => {
    if (!testId && questions.length === 0) {
      // Initialize with default test values and a question
      const defaultTest = createDefaultModuleTest(moduleId, testType);
      form.reset({
        title: defaultTest.title || '',
        description: defaultTest.description || '',
        type: testType,
        questions: []
      });
      
      // Add a default question
      addQuestion();
    }
  }, [testId, questions.length]);

  // Add a new question
  const addQuestion = () => {
    const newQuestionNumber = questions.length > 0 
      ? Math.max(...questions.map(q => q.questionNumber)) + 1 
      : 1;
    
    const newQuestion = createDefaultRatingQuestion(newQuestionNumber);
    const updatedQuestions = [...questions, newQuestion];
    
    setQuestions(updatedQuestions);
    form.setValue('questions', updatedQuestions);
  };

  // Remove a question
  const removeQuestion = (index: number) => {
    const updatedQuestions = [...questions];
    updatedQuestions.splice(index, 1);
    
    // Renumber questions to maintain sequential order
    updatedQuestions.forEach((q, i) => {
      q.questionNumber = i + 1;
    });
    
    setQuestions(updatedQuestions);
    form.setValue('questions', updatedQuestions);
  };

  // Update a question
  const updateQuestion = (index: number, field: keyof RatingQuestion, value: any) => {
    const updatedQuestions = [...questions];
    (updatedQuestions[index] as any)[field] = value;
    
    setQuestions(updatedQuestions);
    form.setValue('questions', updatedQuestions);
  };

  // Handle form submission
  const onSubmit = (data: FormValues) => {
    // Make sure questions are included in the submission
    const formData = {
      ...data,
      questions: questions.map(q => ({
        id: q.id,
        type: 'rating' as const, // Use const assertion
        question: q.question,
        questionNumber: q.questionNumber,
        minRating: q.minRating,
        maxRating: q.maxRating,
        minLabel: q.minLabel,
        maxLabel: q.maxLabel
      }))
    };
    
    // Validate that there is at least one question
    if (questions.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please add at least one question to the test",
        variant: "destructive"
      });
      return;
    }
    
    // Validate that all questions have text
    const emptyQuestions = questions.filter(q => !q.question.trim());
    if (emptyQuestions.length > 0) {
      toast({
        title: "Validation Error",
        description: `Please fill in text for all questions (${emptyQuestions.length} empty)`,
        variant: "destructive"
      });
      return;
    }
    
    if (testId) {
      updateTestMutation.mutate(formData);
    } else {
      createTestMutation.mutate(formData);
    }
  };

  if (isLoadingTest) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {testId ? 'Edit Module Test' : 'Create Module Test'}
        </CardTitle>
        <CardDescription>
          {testType === 'pre_test'
            ? 'Pre-tests are shown before the first lesson in a module'
            : 'Post-tests are shown after the last lesson in a module'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="questions">Questions</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                {/* Test Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter test title" {...field} />
                      </FormControl>
                      <FormDescription>
                        Title shown to students taking the test
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Test Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter test description" 
                          {...field} 
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Instructions for students taking the test
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Test Type */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Test Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!!testId} // Cannot change test type once created
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select test type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pre_test">Pre-Test</SelectItem>
                          <SelectItem value="post_test">Post-Test</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Pre-tests are shown before first lesson, post-tests after last lesson
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="questions" className="space-y-6">
                <div className="mb-4 flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Test Questions</h3>
                  <Button 
                    type="button" 
                    onClick={addQuestion} 
                    variant="outline" 
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" /> Add Question
                  </Button>
                </div>

                {questions.length === 0 ? (
                  <div className="text-center py-8 border border-dashed rounded-lg">
                    <p className="text-muted-foreground">No questions added yet</p>
                    <Button 
                      type="button" 
                      onClick={addQuestion}
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                    >
                      <Plus className="w-4 h-4 mr-2" /> Add Question
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {questions.map((question, index) => (
                      <Card key={question.id} className="border border-border">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <CardTitle className="text-base">Question {question.questionNumber}</CardTitle>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button size="icon" variant="ghost" className="h-8 w-8">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This will permanently delete this question.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => removeQuestion(index)}>
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* Question Text */}
                          <div className="space-y-2">
                            <FormLabel htmlFor={`question-${index}`}>Question Text</FormLabel>
                            <Textarea
                              id={`question-${index}`}
                              value={question.question}
                              onChange={(e) => updateQuestion(index, 'question', e.target.value)}
                              placeholder="Enter question text"
                              className="min-h-20"
                            />
                            {question.question.trim() === '' && (
                              <p className="text-sm text-destructive">Question text is required</p>
                            )}
                          </div>

                          {/* Rating Scale Settings */}
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <FormLabel htmlFor={`minRating-${index}`}>Min Rating</FormLabel>
                              <Input
                                id={`minRating-${index}`}
                                type="number"
                                value={question.minRating}
                                onChange={(e) => updateQuestion(index, 'minRating', parseInt(e.target.value))}
                                min={1}
                                max={3}
                              />
                            </div>
                            <div className="space-y-2">
                              <FormLabel htmlFor={`maxRating-${index}`}>Max Rating</FormLabel>
                              <Input
                                id={`maxRating-${index}`}
                                type="number"
                                value={question.maxRating}
                                onChange={(e) => updateQuestion(index, 'maxRating', parseInt(e.target.value))}
                                min={2}
                                max={5}
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <FormLabel htmlFor={`minLabel-${index}`}>Min Label</FormLabel>
                              <Input
                                id={`minLabel-${index}`}
                                value={question.minLabel}
                                onChange={(e) => updateQuestion(index, 'minLabel', e.target.value)}
                                placeholder="e.g., Not familiar"
                              />
                            </div>
                            <div className="space-y-2">
                              <FormLabel htmlFor={`maxLabel-${index}`}>Max Label</FormLabel>
                              <Input
                                id={`maxLabel-${index}`}
                                value={question.maxLabel}
                                onChange={(e) => updateQuestion(index, 'maxLabel', e.target.value)}
                                placeholder="e.g., Very familiar"
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <div className="flex justify-end gap-3 mt-6">
              <Button 
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={createTestMutation.isPending || updateTestMutation.isPending}
              >
                {createTestMutation.isPending || updateTestMutation.isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {testId ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {testId ? 'Update' : 'Create'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default ModuleTestEditor; 