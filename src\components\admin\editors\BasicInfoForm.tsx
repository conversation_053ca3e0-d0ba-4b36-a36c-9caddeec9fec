
import React from 'react';
import { useForm } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UseFormReturn } from 'react-hook-form';

interface BasicInfoFormProps {
  form: UseFormReturn<any>;
  handleTitleChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  restrictType?: boolean; // If true, the type field will be disabled
}

const BasicInfoForm: React.FC<BasicInfoFormProps> = ({
  form,
  handleTitleChange,
  restrictType = false
}) => {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter title"
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  if (handleTitleChange) {
                    handleTitleChange(e);
                  }
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="slug"
        render={({ field }) => (
          <FormItem>
            <FormLabel>URL Slug</FormLabel>
            <FormControl>
              <Input
                placeholder="content-slug"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="duration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Duration (mm:ss)</FormLabel>
              <FormControl>
                <Input
                  placeholder="15:00"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content Type</FormLabel>
              {restrictType ? (
                <Input
                  value={field.value.charAt(0).toUpperCase() + field.value.slice(1)}
                  disabled
                  className="bg-muted"
                />
              ) : (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    if (value === 'quiz') {
                      form.setValue('isRichContent', true);
                    }
                  }}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a content type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="lesson">Lesson (Markdown)</SelectItem>
                    <SelectItem value="quiz">Quiz</SelectItem>
                    <SelectItem value="assignment">Assignment (Markdown)</SelectItem>
                  </SelectContent>
                </Select>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="requirement"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Requirements (Optional)</FormLabel>
            <FormControl>
              <Input
                placeholder="Any prerequisites or requirements"
                {...field}
                value={field.value || ''}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default BasicInfoForm;
