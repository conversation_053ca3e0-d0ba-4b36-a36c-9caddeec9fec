import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { UseFormReturn } from 'react-hook-form';
import QuizEditor from './QuizEditor';
import { QuizQuestion } from './QuizEditor';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';
import { MarkdownPreview } from '@/components/ui/markdown-preview';

interface ContentFormProps {
  form: UseFormReturn<any>;
  lessonType: string;
  content?: string;
  quizQuestions?: QuizQuestion[];
  setQuizQuestions?: React.Dispatch<React.SetStateAction<QuizQuestion[]>>;
  onSubmit?: () => void;
  isSubmitting?: boolean;
  courseId?: string;
  moduleId?: string;
}

const ContentForm: React.FC<ContentFormProps> = ({
  form,
  lessonType,
  content,
  quizQuestions,
  setQuizQuestions,
  onSubmit,
  isSubmitting = false,
  courseId,
  moduleId
}) => {
  const watchContent = form.watch('content');

  if (lessonType === 'quiz' && quizQuestions && setQuizQuestions) {
    return <QuizEditor
      quizQuestions={quizQuestions}
      setQuizQuestions={setQuizQuestions}
      onSubmit={onSubmit}
      isSubmitting={isSubmitting}
    />;
  }

  return (
    <div className="space-y-6">
      {!watchContent && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <h4 className="text-blue-800 font-medium mb-2">Creating a {lessonType === 'assignment' ? 'Assignment' : 'Lesson'}</h4>
          <p className="text-blue-700 text-sm mb-2">
            {lessonType === 'assignment'
              ? 'Assignments allow students to submit work for review. Provide clear instructions and requirements.'
              : 'Lessons can include text content, videos, and images. Use Markdown to format your content.'}
          </p>
          <p className="text-blue-700 text-sm">
            You can add videos and images to enhance the learning experience.
          </p>
        </div>
      )}

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <FormLabel>{lessonType === 'assignment' ? 'Assignment Details' : 'Lesson Content'}</FormLabel>
        </div>

        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium">Content</label>
          </div>

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <SimpleMarkdownEditor
                    initialContent={field.value || ''}
                    onChange={field.onChange}
                    placeholder={`Enter ${lessonType} content...`}
                    minHeight={400}
                    courseId={courseId}
                    moduleId={moduleId}
                  />
                </FormControl>
                <FormMessage />
                <p className="text-xs text-muted-foreground mt-1">
                  Rich text editor with Markdown output. Use the toolbar for formatting or switch to preview mode to see the rendered content.
                </p>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default ContentForm;
