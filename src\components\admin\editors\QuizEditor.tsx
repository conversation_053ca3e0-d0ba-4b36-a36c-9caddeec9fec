
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { PlusCircle, MinusCircle } from 'lucide-react';

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
}

interface QuizEditorProps {
  quizQuestions: QuizQuestion[];
  setQuizQuestions: React.Dispatch<React.SetStateAction<QuizQuestion[]>>;
  onSubmit?: () => void;
  isSubmitting?: boolean;
}

const QuizEditor: React.FC<QuizEditorProps> = ({ quizQuestions, setQuizQuestions, onSubmit, isSubmitting = false }) => {
  // Check if quiz is valid for submission
  const isQuizValid = React.useMemo(() => {
    if (quizQuestions.length === 0) return false;

    // Check if all questions have text and at least one option with text
    return quizQuestions.every(question => {
      const hasQuestionText = question.question.trim().length > 0;
      const hasValidOptions = question.options.some(option => option.trim().length > 0);
      return hasQuestionText && hasValidOptions;
    });
  }, [quizQuestions]);
  const addQuizQuestion = () => {
    setQuizQuestions([
      ...quizQuestions,
      {
        id: crypto.randomUUID(),
        question: '',
        options: ['', '', '', ''],
        correctAnswer: 0
      }
    ]);
  };

  const removeQuizQuestion = (index: number) => {
    const newQuestions = [...quizQuestions];
    newQuestions.splice(index, 1);
    setQuizQuestions(newQuestions);
  };

  const updateQuizQuestion = (index: number, field: string, value: any) => {
    const newQuestions = [...quizQuestions];
    (newQuestions[index] as any)[field] = value;
    setQuizQuestions(newQuestions);
  };

  const updateQuizOption = (questionIndex: number, optionIndex: number, value: string) => {
    const newQuestions = [...quizQuestions];
    newQuestions[questionIndex].options[optionIndex] = value;
    setQuizQuestions(newQuestions);
  };

  return (
    <div className="space-y-6">
      {quizQuestions.length === 0 && (
        <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-900 rounded-md p-4 mb-4">
          <h4 className="text-blue-800 dark:text-blue-300 font-medium mb-2">Creating a Quiz</h4>
          <p className="text-blue-700 dark:text-blue-400 text-sm mb-2">
            Quizzes are a great way to test student knowledge. Add questions with multiple-choice answers and mark the correct option.
          </p>
          <p className="text-blue-700 dark:text-blue-400 text-sm">
            Students will need to complete the quiz to progress through the course.
          </p>
        </div>
      )}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Quiz Questions</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addQuizQuestion}
        >
          <PlusCircle className="w-4 h-4 mr-1" /> Add Question
        </Button>
      </div>

      {quizQuestions.length === 0 ? (
        <div className="text-center py-8 border border-dashed border-border rounded-md bg-muted/30 dark:bg-muted/10">
          <p className="text-muted-foreground mb-3">No questions added yet</p>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addQuizQuestion}
          >
            <PlusCircle className="w-4 h-4 mr-1" /> Add Your First Question
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {quizQuestions.map((question, qIndex) => (
            <Card key={question.id} className="relative">
              <CardContent className="pt-6">
                <div className="absolute top-3 right-3">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeQuizQuestion(qIndex)}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                  >
                    <MinusCircle className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="mb-4">
                    <Label htmlFor={`question-${qIndex}`}>Question {qIndex + 1}</Label>
                    <Input
                      id={`question-${qIndex}`}
                      value={question.question}
                      onChange={(e) => updateQuizQuestion(qIndex, 'question', e.target.value)}
                      className="mt-1"
                      placeholder="Enter your question"
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Answer Options</Label>
                    {question.options.map((option, oIndex) => (
                      <div key={oIndex} className="flex items-center space-x-2">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <Input
                              value={option}
                              onChange={(e) => updateQuizOption(qIndex, oIndex, e.target.value)}
                              placeholder={`Option ${oIndex + 1}`}
                              className="flex-1"
                            />
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={question.correctAnswer === oIndex}
                                onCheckedChange={() => updateQuizQuestion(qIndex, 'correctAnswer', oIndex)}
                                aria-label={`Set as correct answer`}
                              />
                              <Label className="text-xs">Correct</Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Quiz validation message */}
      {quizQuestions.length > 0 && !isQuizValid && (
        <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md text-amber-800 dark:text-amber-300 text-sm">
          <p className="font-medium">Please complete your quiz before submitting:</p>
          <ul className="list-disc list-inside mt-1">
            {quizQuestions.some(q => !q.question.trim()) && (
              <li>All questions must have text</li>
            )}
            {quizQuestions.some(q => !q.options.some(o => o.trim().length > 0)) && (
              <li>Each question must have at least one answer option</li>
            )}
          </ul>
        </div>
      )}

      {/* Create Quiz button - only show when there are questions */}
      {quizQuestions.length > 0 && onSubmit && (
        <div className="mt-8 pt-4 border-t border-border flex justify-end">
          <Button
            type="button"
            onClick={onSubmit}
            disabled={isSubmitting || !isQuizValid}
            className="bg-primary hover:bg-primary/90 text-white"
          >
            {isSubmitting ? (
              <>
                <span className="mr-2">Creating Quiz</span>
                <span className="animate-spin">⟳</span>
              </>
            ) : (
              'Create Quiz'
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default QuizEditor;
