import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { Loader2, Plus, RefreshCw, Edit, Trash2, BookOpen, ArrowLeft, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import CourseEditor from '@/components/admin/CourseEditor';
import { SimpleCourseEditor } from '@/components/admin/SimpleCourseEditor';
import { BasicCourseEditor } from '@/components/admin/BasicCourseEditor';
import ModuleEditor from '@/components/admin/ModuleEditor';
import { getCourseEnrollmentCount } from '@/services/course/enrollmentApi';
import { executeWithRetry } from '@/lib/connection-manager';
import { AddFinalExamButton } from "@/components/admin/AddFinalExamButton";

interface CoursesTabProps {
  selectedCourseId: string | null;
  setSelectedCourseId: (id: string | null) => void;
  selectedModuleId: string | null;
  setSelectedModuleId: (id: string | null) => void;
  setSelectedLessonId: (id: string | null) => void;
  isAddingModule: boolean;
  setIsAddingModule: (isAdding: boolean) => void;
  setIsAddingLesson: (isAdding: boolean) => void;
  confirmDelete: (id: string, type: 'lesson' | 'module' | 'course') => void;
  handleBackToCoursesList: () => void;
  handleBackToCourse: () => void;
}

// Component to display enrollment count
const EnrollmentCountBadge: React.FC<{ courseId: string }> = ({ courseId }) => {
  const { data: count, isLoading } = useQuery({
    queryKey: ['course-enrollments', courseId],
    queryFn: async () => {
      try {
        const { count, error } = await executeWithRetry(async () => {
          return await supabase
            .from('user_course_enrollment')
            .select('*', { count: 'exact', head: true })
            .eq('course_id', courseId);
        });
        
        if (error) throw error;
        return count || 0;
      } catch (error) {
        console.error('Error fetching enrollment count:', error);
        return 0;
      }
    },
  });

  if (isLoading) return null;

  return (
    <div className="flex items-center text-sm text-gray-500">
      <Users size={14} className="mr-1" />
      {count || 0} enrolled
    </div>
  );
};

const CoursesTab: React.FC<CoursesTabProps> = ({
  selectedCourseId,
  setSelectedCourseId,
  selectedModuleId,
  setSelectedModuleId,
  setSelectedLessonId,
  isAddingModule,
  setIsAddingModule,
  setIsAddingLesson,
  confirmDelete,
  handleBackToCoursesList,
  handleBackToCourse,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data: courses, isLoading: isLoadingCourses, refetch: refetchCourses } = useQuery({
    queryKey: ['admin-courses'],
    queryFn: async () => {
      try {
        const { data, error } = await executeWithRetry(async () => {
          return await supabase
            .from('courses')
            .select('*')
            .order('created_at', { ascending: false });
        });

        if (error) {
          console.error('Error fetching courses:', error);
          toast({
            title: "Error fetching courses",
            description: error.message,
            variant: "destructive",
          });
          throw error;
        }
        return data;
      } catch (error: any) {
        toast({
          title: "Failed to fetch courses from database",
          description: error.message || "Please try again in a few moments",
          variant: "destructive",
        });
        throw error;
      }
    },
  });

  const { data: selectedCourse } = useQuery({
    queryKey: ['admin-course', selectedCourseId],
    queryFn: async () => {
      if (!selectedCourseId || selectedCourseId === 'new') return null;

      try {
        const { data, error } = await executeWithRetry(async () => {
          return await supabase
            .from('courses')
            .select('*')
            .eq('id', selectedCourseId)
            .single();
        });

        if (error) {
          console.error('Error fetching course details:', error);
          return null;
        }

        return data;
      } catch (error) {
        console.error('Error in fetchCourseDetails:', error);
        return null;
      }
    },
    enabled: !!selectedCourseId && selectedCourseId !== 'new',
  });

  const { data: courseModules } = useQuery({
    queryKey: ['admin-course-modules', selectedCourseId],
    queryFn: async () => {
      if (!selectedCourseId || selectedCourseId === 'new') return [];

      try {
        const { data, error } = await executeWithRetry(async () => {
          return await supabase
            .from('modules')
            .select('*')
            .eq('course_id', selectedCourseId)
            .order('module_number', { ascending: true });
        });

        if (error) {
          console.error('Error fetching course modules:', error);
          return [];
        }

        return data;
      } catch (error) {
        console.error('Error in fetchCourseModules:', error);
        return [];
      }
    },
    enabled: !!selectedCourseId && selectedCourseId !== 'new',
  });

  if (selectedCourseId) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToCoursesList}
              className="mr-2"
            >
              <ArrowLeft className="w-4 h-4 mr-1" /> Back
            </Button>
            <h2 className="text-xl font-semibold">
              {selectedCourseId === 'new' ? 'Create New Course' : `Editing: ${selectedCourse?.title || 'Course'}`}
            </h2>
          </div>

          {/* Delete button for existing courses */}
          {selectedCourseId !== 'new' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => confirmDelete(selectedCourseId, 'course')}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 border-red-200"
            >
              <Trash2 className="w-4 h-4 mr-1" /> Delete Course
            </Button>
          )}
        </div>

        <BasicCourseEditor
          courseId={selectedCourseId === 'new' ? undefined : selectedCourseId}
          onClose={handleBackToCoursesList}
          initialData={selectedCourse}
        />

        {selectedCourseId !== 'new' && (
          <div className="mt-8 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Course Modules</h3>
              <div className="flex gap-2">
                <AddFinalExamButton courseId={selectedCourseId} />
                <Button onClick={() => setIsAddingModule(true)} className="flex items-center">
                  <Plus className="w-4 h-4 mr-1" /> Add Module
                </Button>
              </div>
            </div>

            {isAddingModule ? (
              <ModuleEditor
                courseId={selectedCourseId}
                onClose={() => setIsAddingModule(false)}
                onAddLesson={(moduleId) => {
                  setSelectedModuleId(moduleId);
                  setIsAddingLesson(true);
                }}
              />
            ) : selectedModuleId && selectedModuleId !== 'new' ? (
              <div className="space-y-4">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedModuleId(null)}
                    className="mr-2"
                  >
                    <ArrowLeft className="w-4 h-4 mr-1" /> Back to Modules
                  </Button>
                  <h3 className="text-lg font-medium">Edit Module</h3>
                </div>
                <ModuleEditor
                  courseId={selectedCourseId}
                  moduleId={selectedModuleId}
                  onClose={() => setSelectedModuleId(null)}
                  onAddLesson={(moduleId) => {
                    setSelectedModuleId(moduleId);
                    setIsAddingLesson(true);
                  }}
                />
              </div>
            ) : (
              <div className="space-y-2">
                {!courseModules?.length && (
                  <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed">
                    <p className="text-gray-500 mb-2">This course doesn't have any modules yet</p>
                    <Button onClick={() => setIsAddingModule(true)} className="flex items-center mx-auto">
                      <Plus className="w-4 h-4 mr-1" /> Add First Module
                    </Button>
                  </div>
                )}

                {courseModules?.map((module) => (
                  <div
                    key={module.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">{module.title}</h4>
                        <p className="text-sm text-gray-500">Module {module.module_number}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedModuleId(module.id);
                            setIsAddingLesson(true);
                          }}
                        >
                          <BookOpen className="w-3 h-3 mr-1" /> Add Lesson
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedModuleId(module.id)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => confirmDelete(module.id, 'module')}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Course Management</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchCourses()}
            disabled={isLoadingCourses}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
          <Button onClick={() => setSelectedCourseId('new')} className="flex items-center">
            <Plus className="w-4 h-4 mr-1" /> New Course
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {isLoadingCourses ? (
          <div className="flex justify-center p-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            {courses?.map((course) => (
              <div
                key={course.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow relative group"
              >
                <div
                  className="cursor-pointer"
                  onClick={() => setSelectedCourseId(course.id)}
                >
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium">{course.title}</h3>
                    <EnrollmentCountBadge courseId={course.id} />
                  </div>
                  <p className="text-sm text-gray-500 line-clamp-2">{course.description}</p>
                  <div className="mt-2 text-xs text-gray-400">
                    Last updated: {new Date(course.updated_at).toLocaleDateString()}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="absolute top-3 right-3 flex gap-2 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/module-management/${course.id}`);
                    }}
                    className="h-8 w-8 p-0"
                    title="Manage Modules"
                  >
                    <BookOpen className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedCourseId(course.id);
                    }}
                    className="h-8 w-8 p-0"
                    title="Edit Course"
                  >
                    <Edit className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      confirmDelete(course.id, 'course');
                    }}
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                    title="Delete Course"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {courses?.length === 0 && (
              <div className="col-span-2 text-center py-12 bg-gray-50 rounded-lg border border-dashed">
                <p className="text-gray-500 mb-4">No courses yet</p>
                <Button onClick={() => setSelectedCourseId('new')} className="flex items-center mx-auto">
                  <Plus className="w-4 h-4 mr-1" /> Create Your First Course
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CoursesTab;
