import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { Loader2, Refresh<PERSON>w, Edit, Trash2, BookOpen, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import LessonEditor from '@/components/admin/LessonEditor';

interface LessonsTabProps {
  selectedModuleId: string | null;
  setSelectedModuleId: (id: string | null) => void;
  selectedLessonId: string | null;
  setSelectedLessonId: (id: string | null) => void;
  isAddingLesson: boolean;
  setIsAddingLesson: (isAdding: boolean) => void;
  confirmDelete: (id: string, type: 'lesson' | 'module' | 'course') => void;
}

const LessonsTab: React.FC<LessonsTabProps> = ({
  selectedModuleId,
  setSelectedModuleId,
  selectedLessonId,
  setSelectedLessonId,
  isAddingLesson,
  setIsAddingLesson,
  confirmDelete,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data: modules } = useQuery({
    queryKey: ['admin-modules'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('*, courses(title)')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching modules:', error);
        toast({
          title: "Error fetching modules",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
  });

  const { data: lessons, isLoading: isLoadingLessons, refetch: refetchLessons } = useQuery({
    queryKey: ['admin-lessons'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lessons')
        .select('*, modules(title, course_id, courses(title))')
        .not('type', 'eq', 'quiz') // Exclude quizzes from the lessons tab
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching lessons:', error);
        toast({
          title: "Error fetching lessons",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
  });

  // First filter out quizzes, then apply search filter
  const filteredLessons = lessons?.filter(lesson => {
    // Only show lessons and assignments in the Lessons tab
    if (lesson.type === 'quiz') return false;

    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return (
      lesson.title.toLowerCase().includes(searchLower) ||
      lesson.type.toLowerCase().includes(searchLower) ||
      (lesson.modules as any)?.title?.toLowerCase().includes(searchLower) ||
      (lesson.modules as any)?.courses?.title?.toLowerCase().includes(searchLower)
    );
  });

  const handleViewLesson = (courseId: string, lessonSlug: string) => {
    navigate(`/course/${courseId}/lesson/${lessonSlug}`);
  };

  const handleCloseEditor = () => {
    setSelectedLessonId(null);
    setIsAddingLesson(false);
    setSelectedModuleId(null);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Lesson Management</h2>
        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search lessons..."
              className="pl-8 w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchLessons()}
            disabled={isLoadingLessons}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {selectedLessonId || isAddingLesson ? (
        <div className="bg-card text-card-foreground p-4 rounded-lg border border-border mb-4 shadow-sm">
          {selectedModuleId ? (
            <LessonEditor
              moduleId={selectedModuleId}
              lessonId={selectedLessonId || undefined}
              defaultType="lesson"
              restrictType={true}
              onClose={handleCloseEditor}
            />
          ) : (
            <div className="p-4 text-center">
              <p className="text-destructive">Please select a module first</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCloseEditor}
                className="mt-2"
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {modules?.map((module: any) => (
              <Button
                key={module.id}
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedModuleId(module.id);
                  setIsAddingLesson(true);
                }}
              >
                <BookOpen className="w-3 h-3 mr-1" />
                Add Lesson to {module.title}
              </Button>
            ))}
          </div>

          {isLoadingLessons ? (
            <div className="flex justify-center p-8">
              <Loader2 className="w-6 h-6 animate-spin text-primary" />
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Module</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLessons?.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        No lessons found
                      </TableCell>
                    </TableRow>
                  )}
                  {filteredLessons?.map((lesson: any) => (
                    <TableRow key={lesson.id}>
                      <TableCell className="font-medium">{lesson.title}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          lesson.type === 'quiz' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' :
                          lesson.type === 'assignment' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300' :
                          'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                        }`}>
                          {lesson.type.charAt(0).toUpperCase() + lesson.type.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell>{(lesson.modules as any)?.title}</TableCell>
                      <TableCell>{(lesson.modules as any)?.courses?.title}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewLesson((lesson.modules as any)?.course_id, lesson.slug)}
                            title="View lesson"
                          >
                            <BookOpen className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedModuleId(lesson.module_id);
                              setSelectedLessonId(lesson.id);
                            }}
                            title="Edit lesson"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => confirmDelete(lesson.id, 'lesson')}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            title="Delete lesson"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LessonsTab;
