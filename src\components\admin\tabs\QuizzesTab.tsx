import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { Loader2, Plus, RefreshCw, Edit, Trash2, BookOpen, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import LessonEditor from '@/components/admin/LessonEditor';
import { isQuizContent } from '@/services/course/utils';

interface QuizzesTabProps {
  selectedModuleId: string | null;
  setSelectedModuleId: (id: string | null) => void;
  selectedLessonId: string | null;
  setSelectedLessonId: (id: string | null) => void;
  isAddingQuiz: boolean;
  setIsAddingQuiz: (isAdding: boolean) => void;
  confirmDelete: (id: string, type: 'lesson' | 'module' | 'course') => void;
}

const QuizzesTab: React.FC<QuizzesTabProps> = ({
  selectedModuleId,
  setSelectedModuleId,
  selectedLessonId,
  setSelectedLessonId,
  isAddingQuiz,
  setIsAddingQuiz,
  confirmDelete,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data: modules } = useQuery({
    queryKey: ['admin-modules'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('*, courses(title)')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching modules:', error);
        toast({
          title: "Error fetching modules",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
  });

  const { data: lessons, isLoading: isLoadingLessons, refetch: refetchLessons } = useQuery({
    queryKey: ['admin-lessons'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lessons')
        .select('*, modules(title, course_id, courses(title))')
        .eq('type', 'quiz')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching quizzes:', error);
        toast({
          title: "Error fetching quizzes",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
  });

  const filteredLessons = lessons?.filter(lesson => {
    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return (
      lesson.title.toLowerCase().includes(searchLower) ||
      (lesson.modules as any)?.title?.toLowerCase().includes(searchLower) ||
      (lesson.modules as any)?.courses?.title?.toLowerCase().includes(searchLower)
    );
  });

  const handleViewLesson = (courseId: string, lessonSlug: string) => {
    navigate(`/course/${courseId}/lesson/${lessonSlug}`);
  };

  const handleCloseEditor = () => {
    setSelectedLessonId(null);
    setIsAddingQuiz(false);
    setSelectedModuleId(null);
  };

  return (
    <div>
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">New Feature Available!</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>We've launched an enhanced quiz management page with improved features including quiz type selection. Try it out by clicking the "Go to Enhanced Quiz Management" button.</p>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Quiz Management</h2>
        <div className="flex gap-2">
          <Button
            variant="default"
            onClick={() => navigate('/admin/quizzes')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Go to Enhanced Quiz Management
          </Button>
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search quizzes..."
              className="pl-8 w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchLessons()}
            disabled={isLoadingLessons}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {isAddingQuiz || selectedLessonId ? (
        <div className="bg-gray-50 p-4 rounded-lg border mb-4">
          {selectedModuleId ? (
            <LessonEditor
              moduleId={selectedModuleId}
              lessonId={selectedLessonId || undefined}
              defaultType="quiz"
              restrictType={true}
              onClose={handleCloseEditor}
            />
          ) : (
            <div className="p-4 text-center">
              <p className="text-red-500">Please select a module first</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCloseEditor}
                className="mt-2"
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {modules?.map((module: any) => (
              <Button
                key={module.id}
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedModuleId(module.id);
                  setIsAddingQuiz(true);
                }}
              >
                <Plus className="w-3 h-3 mr-1" />
                Add Quiz to {module.title}
              </Button>
            ))}
          </div>

          {isLoadingLessons ? (
            <div className="flex justify-center p-8">
              <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Module</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Questions</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLessons?.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                        No quizzes found
                      </TableCell>
                    </TableRow>
                  )}
                  {filteredLessons?.map((quiz: any) => {
                    let questionCount = 0;
                    try {
                      if (quiz.content && isQuizContent(quiz.content)) {
                        const parsed = JSON.parse(quiz.content);
                        questionCount = parsed.questions?.length || 0;
                      }
                    } catch (e) {}

                    return (
                      <TableRow key={quiz.id}>
                        <TableCell className="font-medium">{quiz.title}</TableCell>
                        <TableCell>{(quiz.modules as any)?.title}</TableCell>
                        <TableCell>{(quiz.modules as any)?.courses?.title}</TableCell>
                        <TableCell>{questionCount} questions</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewLesson((quiz.modules as any)?.course_id, quiz.slug)}
                              title="View quiz"
                            >
                              <BookOpen className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedModuleId(quiz.module_id);
                                setSelectedLessonId(quiz.id);
                              }}
                              title="Edit quiz"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => confirmDelete(quiz.id, 'lesson')}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              title="Delete quiz"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default QuizzesTab;
