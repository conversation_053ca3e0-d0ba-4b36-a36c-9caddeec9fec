
import React, { useState } from 'react';
import UserManager from '@/components/admin/UserManager';
import AssignTeacherRole from '@/components/admin/AssignTeacherRole';
import RoleRequestsManager from '@/components/admin/RoleRequestsManager';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { User, UserPlus, UserCheck } from 'lucide-react';

const UsersTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('users');

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">User Management</h2>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Assign Roles
          </TabsTrigger>
          <TabsTrigger value="requests" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            Role Requests
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <UserManager />
        </TabsContent>

        <TabsContent value="roles">
          <AssignTeacherRole />
        </TabsContent>

        <TabsContent value="requests">
          <RoleRequestsManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UsersTab;
