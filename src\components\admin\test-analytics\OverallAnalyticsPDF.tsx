import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { format } from 'date-fns';
import { OverallAnalyticsExport } from '@/types/test-analytics';
import { toast } from 'sonner';

export const generateOverallAnalyticsPDF = async (data: OverallAnalyticsExport): Promise<void> => {
  try {
    console.log('Generating overall analytics PDF...');

    // Create a temporary container for the PDF content
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '210mm';
    container.style.backgroundColor = '#ffffff';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.padding = '20mm';
    
    // Generate HTML content
    container.innerHTML = `
      <div style="max-width: 170mm; margin: 0 auto;">
        <!-- Header -->
        <div style="border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
          <h1 style="font-size: 28px; font-weight: bold; color: #333; margin: 0 0 10px 0;">Test Analytics Overview</h1>
          <h2 style="font-size: 18px; color: #666; margin: 0 0 15px 0;">Comprehensive Student Response Analysis</h2>
          <div style="font-size: 12px; color: #666;">
            <div><strong>Generated:</strong> ${format(new Date(data.generatedAt), 'MMM dd, yyyy HH:mm')}</div>
            <div><strong>Total Students:</strong> ${data.totalStudents} | <strong>Total Responses:</strong> ${data.totalResponses}</div>
          </div>
        </div>

        <!-- Summary Statistics -->
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
          <h3 style="font-size: 16px; font-weight: 600; color: #333; margin: 0 0 10px 0;">Summary Statistics</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; font-size: 14px;">
            <div><strong>Tests Analyzed:</strong> ${data.testSummaries.length}</div>
            <div><strong>Average Responses per Test:</strong> ${data.testSummaries.length > 0 ? Math.round(data.totalResponses / data.testSummaries.length) : 0}</div>
            <div><strong>Response Rate:</strong> ${data.totalStudents > 0 ? Math.round((data.totalResponses / (data.totalStudents * data.testSummaries.length)) * 100) : 0}%</div>
          </div>
        </div>

        <!-- Test Summaries -->
        ${data.testSummaries.map(test => `
          <div style="margin-bottom: 40px; page-break-inside: avoid;">
            <!-- Test Header -->
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="font-size: 18px; font-weight: 600; color: #1565c0; margin: 0 0 5px 0;">${test.testTitle}</h3>
              <div style="font-size: 14px; color: #666;">
                <div><strong>Course:</strong> ${test.courseTitle} | <strong>Module:</strong> ${test.moduleTitle}</div>
                <div><strong>Type:</strong> ${test.testType === 'pre_test' ? 'Pre-Test' : 'Post-Test'} | <strong>Total Responses:</strong> ${test.totalResponses}</div>
              </div>
            </div>

            <!-- Questions Analysis -->
            ${test.questions.map(question => `
              <div style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px;">
                <!-- Question Header -->
                <div style="margin-bottom: 15px;">
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <span style="background: #f5f5f5; color: #333; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                      Q${question.questionNumber}
                    </span>
                    <div style="flex: 1;">
                      <p style="font-weight: 500; color: #333; line-height: 1.4; margin: 0 0 8px 0; font-size: 14px;">
                        ${question.questionText}
                      </p>
                      <div style="font-size: 12px; color: #666;">
                        <strong>Responses:</strong> ${question.totalResponses} | <strong>Average Rating:</strong> ${question.averageRating}/4
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Response Breakdown -->
                <div style="background: #fafafa; padding: 12px; border-radius: 6px;">
                  <h4 style="font-size: 14px; font-weight: 600; color: #333; margin: 0 0 10px 0;">Response Distribution</h4>
                  ${question.optionBreakdown.map(option => `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 6px 0;">
                      <div style="flex: 1;">
                        <span style="font-weight: 500; color: #333;">${option.label}</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 100px; height: 12px; background: #e0e0e0; border-radius: 6px; overflow: hidden;">
                          <div style="width: ${option.percentage}%; height: 100%; background: ${
                            option.rating === 1 ? '#f44336' : 
                            option.rating === 2 ? '#ff9800' : 
                            option.rating === 3 ? '#4caf50' : '#2196f3'
                          }; transition: width 0.3s ease;"></div>
                        </div>
                        <span style="font-weight: 600; color: #333; min-width: 45px; text-align: right;">
                          ${option.count} (${option.percentage}%)
                        </span>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            `).join('')}
          </div>
        `).join('')}

        <!-- Footer -->
        <div style="border-top: 1px solid #ccc; padding-top: 15px; text-align: center; font-size: 11px; color: #666; margin-top: 30px;">
          <p style="margin: 0 0 5px 0;">This comprehensive analytics report was automatically generated by the LMS Test Analytics System</p>
          <p style="margin: 0;">Report generated on ${format(new Date(data.generatedAt), 'MMMM dd, yyyy \'at\' HH:mm')}</p>
        </div>
      </div>
    `;

    document.body.appendChild(container);

    // Generate canvas with higher quality for better text rendering
    const canvas = await html2canvas(container, {
      scale: 2,
      logging: false,
      useCORS: true,
      backgroundColor: '#ffffff',
      width: container.offsetWidth,
      height: container.offsetHeight,
      onclone: (clonedDoc) => {
        // Ensure fonts are loaded in the cloned document
        const clonedContainer = clonedDoc.querySelector('div');
        if (clonedContainer) {
          clonedContainer.style.fontFamily = 'Arial, sans-serif';
        }
      }
    });

    // Remove temporary container
    document.body.removeChild(container);

    // Create PDF with multiple pages if needed
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    const pageHeight = 297; // A4 height in mm
    let heightLeft = imgHeight;
    let position = 0;

    // Add first page
    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Generate filename with timestamp
    const filename = `Test_Analytics_Overview_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.pdf`;
    
    // Save PDF
    pdf.save(filename);

    toast.success('Overall analytics report has been downloaded successfully!');
  } catch (error) {
    console.error('Error generating overall analytics PDF:', error);
    toast.error('There was an error creating the analytics report. Please try again.');
    throw error;
  }
};
