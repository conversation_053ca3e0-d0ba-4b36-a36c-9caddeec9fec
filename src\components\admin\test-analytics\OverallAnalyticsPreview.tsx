import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { OverallAnalyticsExport, QuestionAnalytics } from '@/types/test-analytics';
import { format } from 'date-fns';

interface OverallAnalyticsPreviewProps {
  data: OverallAnalyticsExport;
}

const OverallAnalyticsPreview: React.FC<OverallAnalyticsPreviewProps> = ({ data }) => {
  const getOptionColor = (rating: number) => {
    switch (rating) {
      case 1: return 'bg-red-500';
      case 2: return 'bg-orange-500';
      case 3: return 'bg-green-500';
      case 4: return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getOptionTextColor = (rating: number) => {
    switch (rating) {
      case 1: return 'text-red-700';
      case 2: return 'text-orange-700';
      case 3: return 'text-green-700';
      case 4: return 'text-blue-700';
      default: return 'text-gray-700';
    }
  };

  return (
    <div className="space-y-6 max-h-[70vh] overflow-y-auto">
      {/* Header */}
      <div className="text-center border-b pb-4">
        <h2 className="text-2xl font-bold">Overall Test Analytics Preview</h2>
        <p className="text-muted-foreground">
          Generated on {format(new Date(data.generatedAt), 'MMM dd, yyyy HH:mm')}
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{data.totalStudents}</div>
            <div className="text-sm text-muted-foreground">Total Students</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{data.totalResponses}</div>
            <div className="text-sm text-muted-foreground">Total Responses</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{data.testSummaries.length}</div>
            <div className="text-sm text-muted-foreground">Tests Analyzed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {data.testSummaries.length > 0 ? Math.round(data.totalResponses / data.testSummaries.length) : 0}
            </div>
            <div className="text-sm text-muted-foreground">Avg per Test</div>
          </CardContent>
        </Card>
      </div>

      {/* Test Summaries */}
      <div className="space-y-6">
        {data.testSummaries.slice(0, 3).map((test, testIndex) => (
          <Card key={test.testId}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{test.testTitle}</CardTitle>
                  <CardDescription>
                    {test.courseTitle} • {test.moduleTitle}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={test.testType === 'pre_test' ? 'default' : 'secondary'}>
                    {test.testType === 'pre_test' ? 'Pre-Test' : 'Post-Test'}
                  </Badge>
                  <Badge variant="outline">{test.totalResponses} responses</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {test.questions.slice(0, 2).map((question, questionIndex) => (
                  <div key={question.questionId} className="border rounded-lg p-4">
                    <div className="flex items-start gap-2 mb-3">
                      <Badge variant="outline" className="mt-1">Q{question.questionNumber}</Badge>
                      <div className="flex-1">
                        <p className="font-medium text-sm leading-relaxed">
                          {question.questionText.length > 100 
                            ? `${question.questionText.substring(0, 100)}...` 
                            : question.questionText}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {question.totalResponses} responses • Avg: {question.averageRating}/4
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      {question.optionBreakdown.map((option) => (
                        <div key={option.rating} className="flex items-center gap-3">
                          <div className="w-24 text-xs font-medium">
                            {option.label}
                          </div>
                          <div className="flex-1">
                            <Progress 
                              value={option.percentage} 
                              className="h-2"
                            />
                          </div>
                          <div className={`text-xs font-medium min-w-[60px] text-right ${getOptionTextColor(option.rating)}`}>
                            {option.count} ({option.percentage}%)
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
                
                {test.questions.length > 2 && (
                  <div className="text-center text-sm text-muted-foreground">
                    ... and {test.questions.length - 2} more questions
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
        
        {data.testSummaries.length > 3 && (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-muted-foreground">
                ... and {data.testSummaries.length - 3} more tests will be included in the full report
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Footer Note */}
      <div className="text-center text-sm text-muted-foreground border-t pt-4">
        <p>This is a preview. The full PDF report will contain detailed analysis for all tests and questions.</p>
      </div>
    </div>
  );
};

export default OverallAnalyticsPreview;
