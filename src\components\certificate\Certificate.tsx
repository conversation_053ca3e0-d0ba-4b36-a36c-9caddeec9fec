import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Award, Download, Share2, Check, Smartphone, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { cn } from '@/lib/utils';

interface CertificateProps {
  courseName: string;
  completionDate: string;
  courseId: string;
}

const Certificate: React.FC<CertificateProps> = ({
  courseName,
  completionDate,
  courseId
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const certificateRef = useRef<HTMLDivElement>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [showShareSuccess, setShowShareSuccess] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const downloadCertificate = async () => {
    if (!certificateRef.current) return;

    setIsDownloading(true);

    try {
      // Create a canvas from the certificate element
      const canvas = await html2canvas(certificateRef.current, {
        scale: isMobile ? 1.5 : 2, // Lower scale on mobile for better performance
        logging: false,
        useCORS: true,
        backgroundColor: '#1e40af' // Blue background to match the certificate design
      });

      // For mobile, we'll create a PNG image first
      const imgData = canvas.toDataURL('image/png');

      if (isMobile) {
        // On mobile, create a temporary link to download the PNG directly
        const link = document.createElement('a');
        link.href = imgData;
        link.download = `${courseName.replace(/\s+/g, '_')}_Certificate.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: "Certificate Downloaded",
          description: "Your certificate has been saved as a PNG image."
        });
      } else {
        // On desktop, create a PDF
        const pdf = new jsPDF({
          orientation: 'landscape',
          unit: 'mm',
          format: 'a4'
        });

        const imgWidth = 297; // A4 width in mm (landscape)
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
        pdf.save(`${courseName.replace(/\s+/g, '_')}_Certificate.pdf`);

        toast({
          title: "Certificate Downloaded",
          description: "Your certificate has been saved as a PDF."
        });
      }
    } catch (error) {
      console.error('Error generating certificate:', error);
      toast({
        title: "Download Failed",
        description: "There was an error creating your certificate. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDownloading(false);
    }
  };

  // Function to share certificate
  const shareCertificate = async () => {
    if (!certificateRef.current) return;

    setIsSharing(true);

    try {
      // Create a canvas from the certificate element
      const canvas = await html2canvas(certificateRef.current, {
        scale: 1.5,
        logging: false,
        useCORS: true,
        backgroundColor: '#1e40af' // Blue background to match the certificate design
      });

      // Convert to blob for sharing
      const imgData = canvas.toDataURL('image/png');
      const blob = await (await fetch(imgData)).blob();

      // Check if Web Share API is available
      if (navigator.share) {
        await navigator.share({
          title: `${courseName} Certificate`,
          text: `I've completed the ${courseName} course!`,
          files: [new File([blob], `${courseName.replace(/\s+/g, '_')}_Certificate.png`, { type: 'image/png' })]
        });

        setShowShareSuccess(true);
        setTimeout(() => setShowShareSuccess(false), 3000);
      } else {
        // Fallback for browsers that don't support sharing files
        await navigator.share({
          title: `${courseName} Certificate`,
          text: `I've completed the ${courseName} course!`
        });

        setShowShareSuccess(true);
        setTimeout(() => setShowShareSuccess(false), 3000);
      }
    } catch (error) {
      console.error('Error sharing certificate:', error);
      if (error.name !== 'AbortError') { // Ignore if user cancelled
        toast({
          title: "Sharing Failed",
          description: "There was an error sharing your certificate. Please try again.",
          variant: "destructive"
        });
      }
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className="flex flex-col items-center">
      {/* Mobile instructions */}
      {isMobile && (
        <div className="w-full mb-6 p-4 bg-muted/50 rounded-lg border border-border">
          <div className="flex items-start gap-3">
            <Smartphone className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <h3 className="text-sm font-medium mb-1">Mobile Certificate Tips</h3>
              <p className="text-xs text-muted-foreground">
                For the best experience, rotate your device to landscape mode when viewing or downloading your certificate.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Certificate preview - I-can-IV Design */}
      <div
        ref={certificateRef}
        className={cn(
          "w-full relative overflow-hidden",
          isMobile ? "max-w-full" : "max-w-4xl"
        )}
        style={{ aspectRatio: '1.414/1' }} // A4 aspect ratio
      >
        {/* Blue gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800">
          {/* Decorative curved elements */}
          <div className="absolute top-0 right-0 w-1/2 h-full">
            <svg className="w-full h-full" viewBox="0 0 400 600" preserveAspectRatio="xMidYMid slice">
              <path
                d="M0,0 Q200,100 400,0 L400,200 Q200,300 0,200 Z"
                fill="rgba(255,255,255,0.1)"
              />
              <path
                d="M0,400 Q200,500 400,400 L400,600 L0,600 Z"
                fill="rgba(255,255,255,0.05)"
              />
            </svg>
          </div>

          {/* Additional decorative curves */}
          <div className="absolute bottom-0 left-0 w-full h-1/3">
            <svg className="w-full h-full" viewBox="0 0 800 200" preserveAspectRatio="none">
              <path
                d="M0,200 Q400,50 800,200 L800,200 L0,200 Z"
                fill="rgba(255,255,255,0.08)"
              />
            </svg>
          </div>
        </div>

        {/* White certificate area */}
        <div className={cn(
          "absolute bg-white rounded-lg shadow-2xl",
          isMobile
            ? "inset-4 sm:inset-6"
            : "inset-8"
        )}>
          {/* Certificate content */}
          <div className={cn(
            "h-full flex flex-col justify-between text-center relative",
            isMobile ? "p-4 sm:p-6" : "p-8 sm:p-12"
          )}>

            {/* Header */}
            <div className="flex-shrink-0">
              <h1 className={cn(
                "font-bold text-blue-900 mb-2",
                isMobile ? "text-2xl sm:text-3xl" : "text-4xl lg:text-5xl"
              )}>
                I-can-IV
              </h1>
              <p className={cn(
                "text-blue-700 tracking-wider",
                isMobile ? "text-sm sm:text-base" : "text-lg lg:text-xl"
              )}>
                eLearning Modules
              </p>
            </div>

            {/* Main content */}
            <div className="flex-grow flex flex-col justify-center">
              <p className={cn(
                "text-gray-700 mb-4 sm:mb-6 tracking-wide",
                isMobile ? "text-xs sm:text-sm" : "text-base"
              )}>
                THIS CERTIFICATE IS AWARDED TO
              </p>

              {/* Name line */}
              <div className="mb-6 sm:mb-8">
                <div className={cn(
                  "border-b-2 border-gray-400 mx-auto mb-2",
                  isMobile ? "w-48 sm:w-64" : "w-80"
                )}></div>
                <h2 className={cn(
                  "font-bold text-gray-800",
                  isMobile ? "text-lg sm:text-xl" : "text-2xl lg:text-3xl"
                )}>
                  {user?.user_metadata?.full_name || user?.email}
                </h2>
              </div>

              {/* Course completion text */}
              <div className={cn(
                "mb-6 sm:mb-8 leading-relaxed",
                isMobile ? "px-2" : "px-4"
              )}>
                <p className={cn(
                  "text-gray-700 mb-2",
                  isMobile ? "text-sm sm:text-base" : "text-lg"
                )}>
                  for successfully completing the {courseName} eLearning modules,
                </p>
                <p className={cn(
                  "text-gray-700",
                  isMobile ? "text-sm sm:text-base" : "text-lg"
                )}>
                  demonstrating dedication to professional development in medical imaging.
                </p>
              </div>

              {/* Platform description */}
              <p className={cn(
                "text-gray-600 font-medium mb-6 sm:mb-8",
                isMobile ? "text-xs sm:text-sm" : "text-sm lg:text-base"
              )}>
                e4mi - providing an evidence-based eLearning platform to train the medical imaging workforce
              </p>
            </div>

            {/* Footer with signatures and badge */}
            <div className="flex-shrink-0">
              <div className={cn(
                "flex items-end justify-between",
                isMobile ? "gap-4" : "gap-8"
              )}>

                {/* Badge and duration */}
                <div className="flex flex-col items-center">
                  <div className="relative mb-2">
                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center">
                      <Award className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 sm:w-8 sm:h-8 bg-blue-800 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <p className={cn(
                    "text-gray-600 font-medium",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    (40-45 mins)
                  </p>
                </div>

                {/* Signature */}
                <div className="text-center">
                  <div className="mb-2">
                    <div className={cn(
                      "font-signature text-gray-700 mb-1",
                      isMobile ? "text-lg" : "text-xl lg:text-2xl"
                    )}>
                      Dr. Andrew Donkor
                    </div>
                    <div className={cn(
                      "h-px bg-gray-400",
                      isMobile ? "w-24 sm:w-32" : "w-40"
                    )}></div>
                  </div>
                  <div className={cn(
                    "text-gray-600 leading-tight",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    <p className="font-medium">Dr. Andrew Donkor</p>
                    <p>Project Lead, e4mi Initiative</p>
                    <p>Lecturer, Department of Medical</p>
                    <p>Imaging, KNUST</p>
                  </div>
                </div>

                {/* Logo */}
                <div className="flex flex-col items-center">
                  <div className={cn(
                    "bg-red-600 text-white font-bold rounded",
                    isMobile ? "px-2 py-1 text-xs" : "px-3 py-2 text-sm"
                  )}>
                    <div>AD EDUCATION</div>
                    <div>& RESEARCH</div>
                    <div>GROUP</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="mt-6 flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-center">
        <motion.div
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="w-full sm:w-auto"
        >
          <Button
            onClick={downloadCertificate}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2 rounded-full w-full sm:w-auto"
            disabled={isDownloading}
          >
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download Certificate
              </>
            )}
          </Button>
        </motion.div>

        {/* Share button - only on mobile or devices that support Web Share API */}
        {(isMobile || (typeof navigator !== 'undefined' && 'share' in navigator)) && (
          <motion.div
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="w-full sm:w-auto"
          >
            <Button
              onClick={shareCertificate}
              variant="outline"
              className="border-primary/30 text-primary hover:bg-primary/5 px-6 py-2 rounded-full w-full sm:w-auto"
              disabled={isSharing || showShareSuccess}
            >
              {isSharing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Preparing...
                </>
              ) : showShareSuccess ? (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Shared!
                </>
              ) : (
                <>
                  <Share2 className="mr-2 h-4 w-4" />
                  Share Achievement
                </>
              )}
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Certificate;
