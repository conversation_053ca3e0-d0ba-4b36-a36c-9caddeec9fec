import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Too<PERSON><PERSON>,
  Legend,
  TooltipProps
} from 'recharts';
import { useTheme } from '@/components/theme/theme-provider';
import { cn } from '@/lib/utils';
import { useMotion } from '@/context/MotionContext';

interface CourseCompletionData {
  name: string;
  value: number;
  color: string;
}

interface CourseCompletionPieChartProps {
  data: CourseCompletionData[];
  className?: string;
  height?: number;
  title?: string;
}

export function CourseCompletionPieChart({ 
  data, 
  className, 
  height = 300,
  title = "Course Completion Status"
}: CourseCompletionPieChartProps) {
  const { theme } = useTheme();
  const { shouldReduceMotion } = useMotion();
  const isDark = theme === 'dark';

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card p-3 rounded-lg shadow-md border border-border/40 text-sm">
          <p className="font-medium text-foreground mb-1">{payload[0].name}</p>
          <p style={{ color: payload[0].payload.color }}>
            Count: {payload[0].value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    return (
      <ul className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <li key={`legend-${index}`} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-muted-foreground">{entry.value}</span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className={cn("w-full bg-card p-4 rounded-xl border border-border/40 shadow-sm", className)}>
      <h3 className="text-lg font-medium mb-4 text-foreground text-center">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            innerRadius={40}
            fill="#8884d8"
            dataKey="value"
            animationDuration={shouldReduceMotion ? 0 : 1000}
            animationBegin={shouldReduceMotion ? 0 : 200}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
