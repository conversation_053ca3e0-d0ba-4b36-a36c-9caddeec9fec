import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import Confetti from '@/components/ui/confetti';

interface CourseCompletionCelebrationProps {
  courseName: string;
  courseId: string;
  onClose: () => void;
  isVisible?: boolean;
}

const CourseCompletionCelebration: React.FC<CourseCompletionCelebrationProps> = ({
  courseName,
  courseId,
  onClose,
  isVisible = false
}) => {
  const navigate = useNavigate();

  const handleViewCertificate = () => {
    onClose();
    navigate(`/certificate/${courseId}`);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', damping: 15 }}
            className="relative w-full max-w-xs rounded-xl overflow-hidden shadow-2xl bg-gradient-to-br from-red-500 to-red-700"
          >
            <Confetti count={300} duration={6} active={true} />

            <div className="text-center p-6 pb-8 relative z-10">
              <motion.h2
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-3xl font-bold mb-6 text-white tracking-wide"
              >
                Congratulations!
              </motion.h2>

              <motion.div
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="mb-6"
              >
                <div className="mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center border-2 border-white/20">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
              </motion.div>

              <motion.p
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-white/90 text-center mb-8"
              >
                You've successfully completed<br />
                <span className="font-semibold">{courseName}</span>
              </motion.p>

              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <Button
                  onClick={handleViewCertificate}
                  className="bg-white hover:bg-white/90 text-red-600 font-medium rounded-full px-8 py-2 h-auto shadow-lg"
                >
                  View Certificate
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CourseCompletionCelebration;
