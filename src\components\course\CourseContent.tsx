import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ModuleSection from '../ModuleSection';
import { Module } from '../../services/courseService';
import { useUserRole } from '@/hooks/useUserRole';
import { useQueryClient } from '@tanstack/react-query';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScreenSize } from '@/hooks/use-mobile'; 
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { BookOpen, Clock, FileText } from 'lucide-react';
import '@/styles/course-content.css';
import '@/styles/mobile-modules.css';

interface CourseContentProps {
  modules: Module[];
  courseId?: string;
  isPreview?: boolean;
}

const CourseContent: React.FC<CourseContentProps> = ({
  modules,
  courseId = 'design-strategy',
  isPreview = false
}) => {
  const { isTeacher } = useUserRole();
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  const isVerySmall = screenSize === 'xs';
  const isLargeScreen = screenSize === 'lg' || screenSize === 'xl' || screenSize === '2xl';
  const navigate = useNavigate();
  const [editingModuleId, setEditingModuleId] = useState<string | null>(null);
  const [isAddingModule, setIsAddingModule] = useState(false);
  const queryClient = useQueryClient();

  const activeModuleId = modules.length > 0 ? modules[0].id : null;

  const handleCloseEditor = () => {
    setEditingModuleId(null);
    setIsAddingModule(false);
    if (courseId) {
      queryClient.invalidateQueries({
        queryKey: ['courseModules', courseId],
      });
    }
  };

  // Calculate total course duration
  const getTotalDuration = () => {
    return modules.reduce((acc, module) => {
      if (!module.lessons) return acc;
      
      return acc + module.lessons.reduce((lessonAcc, lesson) => {
        // Extract minutes from duration strings like "15:00 mins" or "25:00 mins"
        const minutes = parseInt(lesson.duration?.split(':')[0] || '0');
        return lessonAcc + (isNaN(minutes) ? 0 : minutes);
      }, 0);
    }, 0);
  };

  // Calculate total lessons
  const getTotalLessons = () => {
    return modules.reduce((acc, module) => acc + (module.lessons?.length || 0), 0);
  };

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
    >
      {/* Course Stats - Only show on desktop */}
      {!isMobile && !isPreview && (
        <div className={cn(
          "grid grid-cols-3 gap-4 p-4 mb-6 rounded-xl border border-gray-100 dark:border-gray-800",
          "bg-white/90 dark:bg-gray-800/90 shadow-sm"
        )}>
          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-50 dark:bg-red-900/20">
              <Clock className="w-5 h-5 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Duration</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{getTotalDuration()} minutes</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-50 dark:bg-red-900/20">
              <BookOpen className="w-5 h-5 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Modules</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{modules.length} total</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-50 dark:bg-red-900/20">
              <FileText className="w-5 h-5 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Lessons</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{getTotalLessons()} total</p>
            </div>
          </div>
        </div>
      )}

      {/* Course Modules */}
      <div className={cn(
        "space-y-2", /* Reduced spacing from space-y-4 to space-y-2 */
        isMobile && "px-0 space-y-2 mt-2" /* Reduced mobile spacing */
      )}>
        <AnimatePresence>
          {modules.map((module, index) => (
            <motion.div
              key={module.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.3,
                delay: index * 0.075,
                ease: [0.16, 1, 0.3, 1]
              }}
            >
              <ModuleSection
                moduleNumber={module.module_number}
                title={module.title}
                lessons={module.lessons || []}
                isLocked={module.is_locked}
                isCompleted={module.is_completed}
                isActive={module.id === activeModuleId}
                courseId={courseId}
                moduleId={module.id}
                isPreview={isPreview}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default CourseContent;
