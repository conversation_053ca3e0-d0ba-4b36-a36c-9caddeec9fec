import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, Trophy, LockIcon } from 'lucide-react';
import { toast } from 'sonner';
import { getEnrollmentStatus } from '@/services/course/enrollmentApi';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import CourseCompletionCelebration from './CourseCompletionCelebration';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { completeCourse, finishCourse } from '@/services/course/completionService';

interface FinishCourseButtonProps {
  courseId: string;
  userId: string;
  modules: any[];
  isEnrolled: boolean;
  courseName?: string;
}

const FinishCourseButton: React.FC<FinishCourseButtonProps> = ({
  courseId,
  userId,
  modules,
  isEnrolled,
  courseName = 'this course'
}) => {
  const [isCompleting, setIsCompleting] = useState(false);
  const [allModulesCompleted, setAllModulesCompleted] = useState(false);
  const [isCourseCompleted, setIsCourseCompleted] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Check if all modules are completed
  useEffect(() => {
    const checkModuleCompletion = async () => {
      try {
        if (!modules || modules.length === 0 || !userId) return;

        // Get all module progress records for this user
        const { data: moduleProgress, error: progressError } = await supabase
          .from('user_module_progress')
          .select('module_id')
          .eq('user_id', userId)
          .eq('is_completed', true)
          .in('module_id', modules.map(m => m.id));

        if (progressError) {
          console.error('Error checking module completion:', progressError);
          return;
        }

        const completed = moduleProgress && moduleProgress.length === modules.length;
        setAllModulesCompleted(completed);

        // Also check if the course is already completed
        const { data: enrollment } = await supabase
          .from('user_course_enrollment')
          .select('status')
          .eq('user_id', userId)
          .eq('course_id', courseId)
          .single();

        setIsCourseCompleted(enrollment?.status === 'completed');

        // Log the enrollment status for debugging
        console.log(`Course ${courseId} enrollment status for user ${userId}:`, enrollment?.status);
      } catch (error) {
        console.error('Error checking module completion:', error);
      }
    };

    checkModuleCompletion();
  }, [modules, userId, courseId]);

  // Listen for custom finish course event
  useEffect(() => {
    const handleFinishCourseEvent = (event: CustomEvent<{courseId: string}>) => {
      console.log('=== FINISH COURSE EVENT DETECTED ===');
      console.log('Event detail:', event.detail);
      console.log('Component courseId:', courseId);
      
      if (event.detail && event.detail.courseId === courseId) {
        console.log('Course IDs match, handling finish course event');
        handleFinishCourse();
      } else {
        console.log('Course IDs do not match or detail missing');
      }
    };

    // Add event listener
    document.addEventListener('finishCourse', handleFinishCourseEvent as EventListener);

    // Clean up event listener
    return () => {
      document.removeEventListener('finishCourse', handleFinishCourseEvent as EventListener);
    };
  }, [courseId, userId, isEnrolled, allModulesCompleted]); // Dependencies for handleFinishCourse

  const storeCompletionLocally = () => {
    try {
      // Get existing completed courses or initialize empty array
      const completedCoursesStr = localStorage.getItem('completedCourses') || '[]';
      const completedCourses = JSON.parse(completedCoursesStr);
      
      // Add this course if not already included
      if (!completedCourses.includes(courseId)) {
        completedCourses.push(courseId);
        localStorage.setItem('completedCourses', JSON.stringify(completedCourses));
      }
      
      // Store additional metadata
      localStorage.setItem(`course_${courseId}_completed_at`, new Date().toISOString());
      localStorage.setItem(`course_${courseId}_completed_by`, userId);
      
      console.log('Course completion saved to localStorage:', courseId);
      return true;
    } catch (storageError) {
      console.error('Error storing completion in localStorage:', storageError);
      return false;
    }
  };

  const handleFinishCourse = async () => {
    if (!courseId || !userId || !isEnrolled) {
      toast.error('You need to be enrolled in this course to complete it');
      return;
    }

    if (!allModulesCompleted && !isCourseCompleted) {
      toast.error('You need to complete all modules before finishing the course');
      return;
    }

    try {
      setIsCompleting(true);

      // Use the new finishCourse function that properly handles certificate generation
      const result = await finishCourse(courseId, userId);

      if (result.success) {
        // Show success message
        toast.success(`Congratulations! You've completed ${courseName}! Your certificate is ready.`);

        // Update UI state
        setIsCourseCompleted(true);
        setShowCelebration(true);

        // Refresh queries to update achievements and certificates
        await queryClient.invalidateQueries({ queryKey: ['courseProgress'] });
        await queryClient.invalidateQueries({ queryKey: ['courseEnrollment'] });
        await queryClient.invalidateQueries({ queryKey: ['certificates'] });
        await queryClient.invalidateQueries({ queryKey: ['user-achievements'] });
      } else {
        toast.error(result.error || 'Failed to complete course. Please try again.');
      }
    } catch (error) {
      console.error('Error finishing course:', error);
      toast.error('Failed to complete course. Please try again.');
    } finally {
      setIsCompleting(false);
    }
  };

  const handleCloseCelebration = () => {
    setShowCelebration(false);
    navigate(`/certificate/${courseId}`);
  };

  // Render the button based on module completion status
  const renderButton = () => {
    if (!allModulesCompleted) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                disabled={true}
                size={isMobile ? "default" : "lg"}
                className={cn(
                  "relative h-auto flex items-center gap-2 rounded-full shadow-md w-full md:w-auto",
                  isMobile ? "px-4 py-3 text-sm" : "px-6 py-6 text-base",
                  "font-medium",
                  "bg-muted/50 text-muted-foreground/60 cursor-not-allowed opacity-60 hover:bg-muted/50 hover:text-muted-foreground/60"
                )}
              >
                <LockIcon className={isMobile ? "h-4 w-4 mr-1" : "h-5 w-5 mr-1"} />
                <span className="line-through decoration-1">Finish Course</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Complete all modules to unlock</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <Button
        onClick={handleFinishCourse}
        disabled={isCompleting}
        size={isMobile ? "default" : "lg"}
        className={cn(
          "relative h-auto flex items-center gap-2 rounded-full shadow-md w-full md:w-auto",
          isMobile ? "px-4 py-3 text-sm" : "px-6 py-6 text-base",
          "font-medium",
          "bg-primary hover:bg-primary/90 text-primary-foreground"
        )}
      >
        {isCompleting ? (
          <>
            <span
              className="animate-spin border-2 border-current border-t-transparent rounded-full mr-2"
              style={{ width: isMobile ? '1rem' : '1.25rem', height: isMobile ? '1rem' : '1.25rem' }}
            />
            Completing...
          </>
        ) : (
          <>
            <Trophy className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
            Finish Course
          </>
        )}
      </Button>
    );
  };

  return (
    <>
      <CourseCompletionCelebration
        courseId={courseId}
        courseName={courseName}
        isVisible={showCelebration}
        onClose={handleCloseCelebration}
      />

      {!isCourseCompleted && (
        <div className={cn(
          "bg-card text-card-foreground rounded-xl shadow-sm border border-border mt-6 overflow-hidden relative",
          isMobile ? "p-4" : "p-6"
        )}>
          {/* Background pattern for visual interest */}
          <div className="absolute inset-0 opacity-5 pointer-events-none">
            <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5" />
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#grid)" />
            </svg>
          </div>

          <div className="relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-6">
              <div className="text-left flex-1">
                <h2 className={cn(
                  "font-semibold mb-2",
                  isMobile ? "text-lg" : "text-xl"
                )}>
                  {allModulesCompleted ? 'Ready to complete this course?' : 'Complete all modules to finish the course'}
                </h2>

                <p className={cn(
                  "text-muted-foreground mb-3 md:mb-4",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  {allModulesCompleted
                    ? 'Congratulations! You have completed all the modules in this course. Click the button to mark the course as completed.'
                    : `You have completed ${modules.filter(m => m.is_completed).length} out of ${modules.length} modules. The "Finish Course" button will be unlocked when all modules are completed.`
                  }
                </p>

                {/* Progress indicator */}
                <div className="w-full bg-muted rounded-full h-2 mb-2 overflow-hidden">
                  <div
                    className="h-full bg-primary transition-all duration-500 ease-out"
                    style={{ width: `${(modules.filter(m => m.is_completed).length / modules.length) * 100}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((modules.filter(m => m.is_completed).length / modules.length) * 100)}% Complete
                </p>
              </div>

              <div className="flex-shrink-0 w-full md:w-auto mt-4 md:mt-0">
                <motion.div
                  whileHover={{ scale: allModulesCompleted && !isMobile ? 1.03 : 1 }}
                  whileTap={{ scale: allModulesCompleted ? 0.97 : 1 }}
                  className="relative"
                >
                  {renderButton()}
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FinishCourseButton;
