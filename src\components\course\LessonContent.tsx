import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { motion } from 'framer-motion';
import '@/styles/unified-lesson-content.css';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { processVideoUrl } from '@/lib/file-upload';

interface LessonContentProps {
  content: string;
  className?: string;
}

interface ParsedContent {
  videoUrl: string | null;
  imageUrl: string | null;
  textContent: string;
  externalRedirectUrl: string | null;
  mainContent: string;
  referencesContent: string;
}

const LessonContent: React.FC<LessonContentProps> = React.memo(({ content, className = '' }) => {
  const isMountedRef = useRef(true);
  const redirectTimerRef = useRef<number | null>(null);

  // Cleanup to prevent memory leaks and race conditions
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Clear any pending timers
      if (redirectTimerRef.current) {
        clearTimeout(redirectTimerRef.current);
        redirectTimerRef.current = null;
      }
    };
  }, []);
  
  // Simplified content parsing with better error handling
  const parseContent = useCallback((rawContent: string): ParsedContent => {
    console.log('[LESSON CONTENT] Parsing content:', rawContent?.substring(0, 200) + '...');

    if (!rawContent) {
      console.log('[LESSON CONTENT] No content provided');
      return {
        videoUrl: null,
        imageUrl: null,
        textContent: '',
        externalRedirectUrl: null,
        mainContent: '',
        referencesContent: ''
      };
    }

    // Initialize with defaults
    let videoUrl: string | null = null;
    let imageUrl: string | null = null;
    let textContent = rawContent;
    let externalRedirectUrl: string | null = null;

    // Try to parse JSON content (for legacy rich media lessons)
    try {
      const parsed = JSON.parse(rawContent);
      if (parsed && typeof parsed === 'object') {
        videoUrl = parsed.videoUrl ? processVideoUrl(parsed.videoUrl) : null;
        imageUrl = parsed.imageUrl || null;
        textContent = parsed.content || rawContent;
        externalRedirectUrl = parsed.externalRedirectUrl || null;
      }
    } catch {
      // Not JSON, treat as plain markdown
      textContent = rawContent;
    }

    // Split content into main and references sections
    const referencesMatch = textContent.match(/(^|\n)(#+\s*References?\s*\n[\s\S]*)$/i);
    let mainContent = textContent;
    let referencesContent = '';

    if (referencesMatch) {
      const splitIndex = referencesMatch.index! + referencesMatch[1].length;
      mainContent = textContent.slice(0, splitIndex).trim();
      referencesContent = textContent.slice(splitIndex).trim();
    }

    console.log('[LESSON CONTENT] Parsed content sections:', {
      hasMainContent: !!mainContent,
      mainContentLength: mainContent.length,
      hasReferencesContent: !!referencesContent,
      hasVideoUrl: !!videoUrl,
      hasImageUrl: !!imageUrl,
      hasExternalRedirect: !!externalRedirectUrl
    });

    return {
      videoUrl,
      imageUrl,
      textContent,
      externalRedirectUrl,
      mainContent,
      referencesContent
    };
  }, []);

  // Memoize parsed content
  const parsedContent = useMemo(() => parseContent(content), [content, parseContent]);

  // Handle external redirect with proper cleanup
  useEffect(() => {
    const { externalRedirectUrl } = parsedContent;
    if (!externalRedirectUrl) return;

    // Redirect after a short delay to allow the page to render
    redirectTimerRef.current = window.setTimeout(() => {
      if (isMountedRef.current) {
        window.open(externalRedirectUrl, '_blank');
      }
    }, 500);

    return () => {
      if (redirectTimerRef.current) {
        clearTimeout(redirectTimerRef.current);
        redirectTimerRef.current = null;
      }
    };
  }, [parsedContent.externalRedirectUrl]);

  // Early return for empty content
  if (!content) {
    return (
      <div className="lesson-content-container">
        <div className="flex items-center justify-center p-8 bg-muted/30 rounded-lg">
          <p className="text-muted-foreground text-center">No content available for this lesson.</p>
        </div>
      </div>
    );
  }

  const { videoUrl, imageUrl, mainContent, referencesContent, externalRedirectUrl } = parsedContent;

  return (
    <motion.div
      className={`enhanced-lesson-container ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Enhanced External redirect button */}
      {externalRedirectUrl && (
        <motion.div
          className="mb-8 flex flex-col items-center justify-center p-8 bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl border border-primary/20 shadow-sm backdrop-blur-sm"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
            <ExternalLink className="w-8 h-8 text-primary" />
          </div>
          <h2 className="text-xl font-semibold mb-3 text-center">External Resource</h2>
          <p className="text-center mb-6 text-muted-foreground max-w-md">
            This lesson includes an external form or resource. Click the button below to access it in a new tab.
          </p>
          <Button
            size="lg"
            onClick={() => window.open(externalRedirectUrl, '_blank')}
            className="flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg"
          >
            <ExternalLink className="w-5 h-5" />
            Open External Resource
          </Button>
        </motion.div>
      )}

      {/* Enhanced Video section with improved responsiveness */}
      {videoUrl && (
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="enhanced-video-container">
            <div className="video-wrapper aspect-video rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border border-border/20">
              {videoUrl.startsWith('data:video/') ? (
                <video
                  controls
                  className="w-full h-full object-cover rounded-xl"
                  autoPlay={false}
                  preload="metadata"
                  controlsList="nodownload"
                  onError={() => {
                    // Handle video playback errors silently
                  }}
                >
                  <source src={videoUrl} type="video/mp4" />
                  <div className="flex items-center justify-center h-full text-muted-foreground bg-muted/20 rounded-xl">
                    <div className="text-center p-6">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                        <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <p className="text-sm font-medium">Video not supported</p>
                      <p className="text-xs text-muted-foreground mt-1">Please upgrade to a modern browser</p>
                    </div>
                  </div>
                </video>
              ) : (
                <iframe
                  src={videoUrl}
                  title="Lesson video"
                  className="w-full h-full rounded-xl"
                  style={{ border: 0 }}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                  loading="lazy"
                  onError={() => {
                    // Handle iframe errors silently
                  }}
                  onLoad={() => {
                    // Handle iframe load silently
                  }}
                />
              )}
            </div>

            {/* Video title/description if available */}
            {parsedContent.mainContent && (
              <div className="mt-3 text-center">
                <div className="text-sm text-muted-foreground/80 font-medium">
                  {(() => {
                    try {
                      const contentObj = JSON.parse(content);
                      return contentObj.videoTitle || contentObj.videoDescription;
                    } catch {
                      return null;
                    }
                  })()}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Enhanced Image section */}
      {imageUrl && (
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <figure className="text-center">
            <div className="inline-block rounded-xl overflow-hidden shadow-lg border border-border/20 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
              <img
                src={imageUrl}
                alt="Lesson image"
                loading="lazy"
                className="max-w-full h-auto block transition-transform duration-300 hover:scale-105"
              />
            </div>
          </figure>
        </motion.div>
      )}

      {/* Enhanced Main content section */}
      <motion.div
        className="lesson-prose-enhanced"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {mainContent ? (
          <div className="prose-content-wrapper">
            <MarkdownPreview
              content={mainContent}
              className="lesson-prose"
              allowHtml={true}
              securityLevel="full"
            />
          </div>
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/20 flex items-center justify-center">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="font-medium">No content available</p>
            <p className="text-sm text-muted-foreground/60 mt-1">This lesson doesn't have any content yet.</p>
          </div>
        )}
      </motion.div>

      {/* Enhanced References section */}
      {referencesContent && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-12"
        >
          <div className="enhanced-references-section">
            <Accordion type="single" collapsible className="enhanced-lesson-accordion">
              <AccordionItem value="references" className="enhanced-accordion-item border border-border/20 rounded-xl overflow-hidden shadow-sm bg-card/50 backdrop-blur-sm">
                <AccordionTrigger className="enhanced-accordion-trigger px-6 py-4 hover:bg-muted/30 transition-colors duration-200">
                  <span className="flex items-center gap-3 text-lg font-semibold text-foreground">
                    <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                      <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    References & Additional Resources
                  </span>
                </AccordionTrigger>
                <AccordionContent className="enhanced-accordion-content px-6 pb-6 pt-2">
                  <div className="lesson-prose">
                    <MarkdownPreview
                      content={referencesContent}
                      className="lesson-prose"
                      allowHtml={true}
                      securityLevel="full"
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
});

LessonContent.displayName = 'LessonContent';

export default LessonContent;
