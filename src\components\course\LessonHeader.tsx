import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';

export interface LessonHeaderProps {
  title: string;
  subtitle?: string;
  backLink: string;
  moduleTitle?: string;
}

const LessonHeader: React.FC<LessonHeaderProps> = ({
  title,
  subtitle,
  backLink,
  moduleTitle
}) => {
  return (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Link
          to={backLink}
          className="flex items-center hover:text-primary transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>{moduleTitle || 'Back to modules'}</span>
        </Link>
      </div>
      <h1 className="text-2xl font-bold sm:text-3xl">{title}</h1>
      {subtitle && (
        <p className="mt-2 text-muted-foreground">{subtitle}</p>
      )}
    </motion.div>
  );
};

export default LessonHeader;
