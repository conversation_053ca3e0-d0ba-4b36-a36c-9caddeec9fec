import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2, ThumbsUp, HelpCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/context/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { completeLessonProgress } from '@/services/course/progressApi';

interface QuestionnaireOption {
  text: string;
  isCorrect?: boolean;
}

interface QuestionnaireQuestion {
  id?: string;
  question: string;
  options: (string | QuestionnaireOption)[];
  type?: string;
  minRating?: number;
  maxRating?: number;
  minLabel?: string;
  maxLabel?: string;
}

interface QuestionnaireContentProps {
  lessonId: string;
  content: string;
  onComplete?: () => void;
}

const QuestionnaireContent: React.FC<QuestionnaireContentProps> = ({ lessonId, content, onComplete }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [questionnaireData, setQuestionnaireData] = useState<{ questions: QuestionnaireQuestion[], quizType?: string } | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Parse the questionnaire content
  useEffect(() => {
    try {
      console.log('QuestionnaireContent - Raw content:', content);
      
      // Validate content is a string
      if (typeof content !== 'string') {
        setError('Invalid content format');
        return;
      }
      
      // Parse the JSON content
      const parsedContent = JSON.parse(content);
      console.log('QuestionnaireContent - Parsed content:', parsedContent);
      
      // Validate questions array
      if (!parsedContent.questions || !Array.isArray(parsedContent.questions)) {
        setError('Invalid questionnaire data: questions array is missing or not an array');
        return;
      }
      
      // Check if any questions exist
      if (parsedContent.questions.length === 0) {
        setError('Invalid questionnaire data: no questions found');
        return;
      }
      
      // Initialize the questionnaire data
      setQuestionnaireData(parsedContent);
      setSelectedAnswers(new Array(parsedContent.questions.length).fill(-1));
      setError(null);
    } catch (error) {
      console.error('Failed to parse questionnaire content:', error);
      setError('Failed to parse questionnaire content');
      setQuestionnaireData(null);
    }
  }, [content]);

  // Mutation to mark the questionnaire as completed
  const completeQuestionnaireMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');
      return await completeLessonProgress(lessonId, user.id);
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['lesson', lessonId],
      });
      
      // Get courseId from URL path
      const pathParts = window.location.pathname.split('/');
      const courseIdIndex = pathParts.indexOf('course') + 1;
      const courseId = courseIdIndex > 0 && courseIdIndex < pathParts.length ? pathParts[courseIdIndex] : null;

      if (courseId) {
        queryClient.invalidateQueries({
          queryKey: ['courseModules', courseId],
        });
      }

      queryClient.invalidateQueries({
        queryKey: ['dashboard-courses', user?.id],
      });

      queryClient.invalidateQueries({
        queryKey: ['courses'],
      });

      toast({
        title: "Questionnaire completed!",
        description: "Thank you for your feedback.",
      });
    },
    onError: (error) => {
      console.error('Error saving questionnaire progress:', error);
      toast({
        title: "Error",
        description: "Failed to save your questionnaire progress.",
        variant: "destructive",
      });
    }
  });

  // Handle answer selection
  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestionIndex] = answerIndex;
    setSelectedAnswers(newAnswers);
  };

  // Handle next button click
  const handleNext = () => {
    // Check if an answer has been selected
    if (selectedAnswers[currentQuestionIndex] === -1) {
      toast({
        title: "Please select an answer",
        description: "You need to select an answer before proceeding.",
        variant: "destructive",
      });
      return;
    }
    
    // Move to next question or complete the questionnaire
    if (currentQuestionIndex < (questionnaireData?.questions.length || 0) - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      console.log('Completing questionnaire...');
      setCompleted(true);
      setShowResults(true);
      
      if (user) {
        completeQuestionnaireMutation.mutate();
      }
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // If there's an error, show error message
  if (error) {
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle className="text-lg">Error Loading Questionnaire</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-6">
          <div className="text-center">
            <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-3" />
            <p className="text-muted-foreground">{error}</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If questionnaire data is not loaded yet, show loading
  if (!questionnaireData) {
    return (
      <Card className="my-6">
        <CardContent className="flex items-center justify-center py-6">
          <div className="text-center">
            <Loader2 className="h-12 w-12 text-muted-foreground mx-auto mb-3 animate-spin" />
            <p className="text-muted-foreground">Loading questionnaire...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get the current question
  const currentQuestion = questionnaireData.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questionnaireData.questions.length) * 100;

  // If showing results (questionnaire completed)
  if (showResults) {
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle>Questionnaire Complete</CardTitle>
          <CardDescription>
            Thank you for completing this questionnaire
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center py-6">
            <ThumbsUp className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <p className="text-lg font-medium">Your responses have been recorded</p>
            <p className="text-muted-foreground mt-2">
              Thank you for providing your feedback. Your responses will help us improve our services.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button variant="default" onClick={onComplete}>
            Continue
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Render the questionnaire
  return (
    <Card className="my-6">
      <CardHeader>
        <div className="flex justify-between items-center mb-2">
          <CardTitle className="text-lg">Questionnaire</CardTitle>
          <span className="text-sm text-muted-foreground">
            Question {currentQuestionIndex + 1} of {questionnaireData.questions.length}
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </CardHeader>
      <CardContent>
        {currentQuestion ? (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">{currentQuestion.question}</h3>
            
            {/* Rating scale questions */}
            {currentQuestion.type === 'rating_scale' ? (
              <div className="pt-2">
                <div className="flex justify-between text-sm mb-2">
                  <span>{currentQuestion.minLabel || 'Poor'}</span>
                  <span>{currentQuestion.maxLabel || 'Excellent'}</span>
                </div>
                <RadioGroup
                  value={selectedAnswers[currentQuestionIndex] === -1 ? '' : selectedAnswers[currentQuestionIndex].toString()}
                  onValueChange={(value) => handleAnswerSelect(parseInt(value))}
                  className="flex justify-between gap-1"
                >
                  {Array.isArray(currentQuestion.options) && currentQuestion.options.map((option, index) => (
                    <div key={index} className="flex flex-col items-center">
                      <RadioGroupItem value={index.toString()} id={`option-${index}`} className="mb-1" />
                      <Label htmlFor={`option-${index}`} className="text-xs">
                        {typeof option === 'string' ? option : (option && option.text) || index + 1}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            ) : (
              /* Standard multiple choice questions */
              <RadioGroup
                value={selectedAnswers[currentQuestionIndex] === -1 ? '' : selectedAnswers[currentQuestionIndex].toString()}
                onValueChange={(value) => handleAnswerSelect(parseInt(value))}
                className="space-y-3"
              >
                {Array.isArray(currentQuestion.options) && currentQuestion.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2 border rounded-md p-3 hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                    <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer">
                      {typeof option === 'string' ? option : (option && option.text) || ''}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            )}
          </div>
        ) : (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentQuestionIndex === 0}
        >
          Previous
        </Button>
        <Button
          onClick={handleNext}
          disabled={isSubmitting}
        >
          {currentQuestionIndex === questionnaireData.questions.length - 1 ? 'Finish' : 'Next'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default QuestionnaireContent;
