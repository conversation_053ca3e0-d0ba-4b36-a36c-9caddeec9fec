import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2, CheckCircle, XCircle, HelpCircle, ThumbsUp } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/context/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { completeLessonProgress } from '@/services/course/progressApi';

// Simple interface for quiz options
interface QuizOption {
  text: string;
  isCorrect?: boolean;
}

// Simple interface for quiz questions
interface QuizQuestion {
  question: string;
  options: (string | QuizOption)[];
  correctAnswer?: number;
  type?: string;
}

// Props for the UnifiedQuizContent component
interface UnifiedQuizContentProps {
  lessonId: string;
  content: string;
  onComplete?: () => void;
}

/**
 * UnifiedQuizContent - A simple component that handles both standard quizzes and questionnaires
 */
const UnifiedQuizContent: React.FC<UnifiedQuizContentProps> = ({ lessonId, content, onComplete }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State variables
  const [quizData, setQuizData] = useState<{ questions: QuizQuestion[], quizType?: string } | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isQuestionnaire, setIsQuestionnaire] = useState(false);

  // Parse the quiz content on component mount
  useEffect(() => {
    try {
      // Parse the JSON content
      const parsedContent = JSON.parse(content);
      
      // Check if this is a questionnaire
      const isQuestionnaireType = parsedContent.quizType === 'questionnaire';
      setIsQuestionnaire(isQuestionnaireType);
      
      // Set the quiz data
      setQuizData(parsedContent);
      
      // Initialize selected answers array
      if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
        setSelectedAnswers(new Array(parsedContent.questions.length).fill(-1));
      } else {
        setError('Invalid quiz data: questions array is missing or not an array');
      }
    } catch (error) {
      console.error('Failed to parse quiz content:', error);
      setError('Failed to parse quiz content');
      setQuizData(null);
    }
  }, [content]);

  // Mutation to mark the quiz as completed
  const completeQuizMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');
      return await completeLessonProgress(lessonId, user.id);
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['lesson', lessonId],
      });
      
      // Get courseId from URL path
      const pathParts = window.location.pathname.split('/');
      const courseIdIndex = pathParts.indexOf('course') + 1;
      const courseId = courseIdIndex > 0 && courseIdIndex < pathParts.length ? pathParts[courseIdIndex] : null;

      if (courseId) {
        queryClient.invalidateQueries({
          queryKey: ['courseModules', courseId],
        });
      }

      queryClient.invalidateQueries({
        queryKey: ['dashboard-courses', user?.id],
      });

      queryClient.invalidateQueries({
        queryKey: ['courses'],
      });

      // Show success message
      toast({
        title: isQuestionnaire ? "Questionnaire completed!" : "Quiz completed!",
        description: isQuestionnaire 
          ? "Thank you for your feedback." 
          : `You scored ${score} out of ${quizData?.questions.length} questions.`,
      });
    },
    onError: (error) => {
      console.error('Error saving quiz progress:', error);
      toast({
        title: "Error",
        description: "Failed to save your progress.",
        variant: "destructive",
      });
    }
  });

  // Handle answer selection
  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestionIndex] = answerIndex;
    setSelectedAnswers(newAnswers);
  };

  // Handle next button click
  const handleNext = () => {
    // Check if an answer has been selected
    if (selectedAnswers[currentQuestionIndex] === -1) {
      toast({
        title: "Please select an answer",
        description: "You need to select an answer before proceeding.",
        variant: "destructive",
      });
      return;
    }
    
    // Move to next question or complete the quiz
    if (currentQuestionIndex < (quizData?.questions.length || 0) - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // For questionnaires, we don't calculate a score
      if (isQuestionnaire) {
        setScore(quizData?.questions.length || 0); // All answers are valid
      } else {
        // For standard quizzes, calculate score based on correct answers
        let correctAnswers = 0;
        quizData?.questions.forEach((question, index) => {
          if (selectedAnswers[index] === question.correctAnswer) {
            correctAnswers++;
          }
        });
        setScore(correctAnswers);
      }
      
      // Mark as completed
      setQuizCompleted(true);
      setShowResults(true);
      
      // Save progress
      if (user) {
        completeQuizMutation.mutate();
      }
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // If there's an error, show error message
  if (error) {
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle className="text-lg">Error Loading Content</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-6">
          <div className="text-center">
            <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-3" />
            <p className="text-muted-foreground">{error}</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If quiz data is not loaded yet, show loading
  if (!quizData || !quizData.questions || quizData.questions.length === 0) {
    return (
      <Card className="my-6">
        <CardContent className="flex items-center justify-center py-6">
          <div className="text-center">
            <Loader2 className="h-12 w-12 text-muted-foreground mx-auto mb-3 animate-spin" />
            <p className="text-muted-foreground">Loading content...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get the current question
  const currentQuestion = quizData.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quizData.questions.length) * 100;

  // If showing results (quiz completed)
  if (showResults) {
    // Different results display for questionnaires vs standard quizzes
    if (isQuestionnaire) {
      return (
        <Card className="my-6">
          <CardHeader>
            <CardTitle>Questionnaire Complete</CardTitle>
            <CardDescription>
              Thank you for completing this questionnaire
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center py-6">
              <ThumbsUp className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <p className="text-lg font-medium">Your responses have been recorded</p>
              <p className="text-muted-foreground mt-2">
                Thank you for providing your feedback.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="default" onClick={onComplete}>
              Continue
            </Button>
          </CardFooter>
        </Card>
      );
    }
    
    // Standard quiz results with correct/incorrect answers
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle>Quiz Results</CardTitle>
          <CardDescription>
            You scored {score} out of {quizData.questions.length} questions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {quizData.questions.map((question, qIndex) => (
            <div key={qIndex} className="border rounded-lg p-4">
              <div className="flex items-start mb-3">
                <div className="flex-shrink-0 mr-3">
                  {selectedAnswers[qIndex] === question.correctAnswer ? (
                    <CheckCircle className="h-5 w-5 text-red-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div>
                  <p className="font-medium mb-2">Question {qIndex + 1}: {question.question}</p>
                  <div className="space-y-2">
                    {question.options.map((option, oIndex) => (
                      <div
                        key={oIndex}
                        className={`px-3 py-2 text-sm rounded ${
                          oIndex === question.correctAnswer
                            ? 'bg-red-100 border border-red-200'
                            : selectedAnswers[qIndex] === oIndex
                              ? 'bg-red-100/50 border border-red-200'
                              : 'bg-gray-50 border border-gray-200'
                        }`}
                      >
                        {typeof option === 'string' ? option : option.text}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => {
            setCurrentQuestionIndex(0);
            setSelectedAnswers(new Array(quizData.questions.length).fill(-1));
            setQuizCompleted(false);
            setShowResults(false);
          }}>
            Retry Quiz
          </Button>
          <Button variant="default" onClick={onComplete}>
            Complete
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Render the quiz questions
  return (
    <Card className="mt-6 mb-24"> {/* Added bottom margin for fixed footer */}
      <CardHeader className="space-y-1">
        <CardTitle className="text-lg sm:text-xl">Question {currentQuestionIndex + 1} of {quizData.questions.length}</CardTitle>
        <CardDescription className="text-sm sm:text-base">
          {quizData.questions[currentQuestionIndex].question}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <RadioGroup
            value={selectedAnswers[currentQuestionIndex] || ''}
            onValueChange={(value) => handleAnswerSelect(value)}
            className="space-y-2"
          >
            {quizData.questions[currentQuestionIndex].options.map((option, index) => (
              <div
                key={index}
                className="flex items-center"
              >
                <RadioGroupItem
                  value={option}
                  id={`option-${index}`}
                  className="w-5 h-5 border-2"
                />
                <Label
                  htmlFor={`option-${index}`}
                  className="flex-1 pl-3 py-3 text-sm sm:text-base"
                >
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      </CardContent>
      <CardFooter className="fixed bottom-0 left-0 right-0 p-4 bg-white/80 dark:bg-black/80 backdrop-blur-md border-t border-gray-100 dark:border-gray-800 z-10">
        <div className="max-w-[650px] mx-auto w-full flex justify-between gap-3">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            size="sm"
            className="min-w-[100px] text-sm py-2 h-9"
          >
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            size="sm"
            className="min-w-[100px] text-sm py-2 h-9"
          >
            {currentQuestionIndex === quizData.questions.length - 1 ? 'Finish' : 'Next'}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default UnifiedQuizContent;
