import React, { useEffect, useState } from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { markdownToHtml } from '@/lib/content-converter';
import { contentSecurity } from '@/lib/content-security';

const ImageDebugTest: React.FC = () => {
  const [processedHtml, setProcessedHtml] = useState<string>('');
  const [sanitizedHtml, setSanitizedHtml] = useState<string>('');

  const testContent = `
# Image Test

This is a test to verify image display functionality.

![Test Image](https://jibspqwieubavucdtccv.supabase.co/storage/v1/object/public/course-images/editor-images/general/1749397228641.jpg)

The image above should be visible if the content security fix is working properly.

## Additional Test

Here's another test with a different image format:

![Another Test](https://via.placeholder.com/300x200/0066cc/ffffff?text=Test+Image)

Both images should display correctly.
  `;

  useEffect(() => {
    // Process the markdown to HTML
    const html = markdownToHtml(testContent);
    setProcessedHtml(html);

    // Apply content security sanitization
    const sanitized = contentSecurity.sanitizeHtml(html);
    setSanitizedHtml(sanitized);

    console.log('Original markdown:', testContent);
    console.log('Processed HTML:', html);
    console.log('Sanitized HTML:', sanitized);
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Image Display Debug Test</h1>

      <div className="border rounded-lg p-4 bg-background mb-6">
        <h2 className="text-lg font-semibold mb-2">Rendered Content:</h2>
        <MarkdownPreview
          content={testContent}
          allowHtml={true}
          securityLevel="extended"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Processed HTML:</h3>
          <pre className="text-xs overflow-auto max-h-40 bg-background p-2 rounded">
            {processedHtml}
          </pre>
        </div>

        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Sanitized HTML:</h3>
          <pre className="text-xs overflow-auto max-h-40 bg-background p-2 rounded">
            {sanitizedHtml}
          </pre>
        </div>
      </div>

      <div className="p-4 bg-muted rounded-lg">
        <h2 className="font-semibold mb-2">Debug Info:</h2>
        <p className="text-sm text-muted-foreground mb-2">
          This test verifies that images from Supabase storage are properly displayed
          after fixing the content security configuration.
        </p>
        <p className="text-sm text-muted-foreground">
          Check the browser console for detailed processing logs.
        </p>
      </div>
    </div>
  );
};

export default ImageDebugTest;
