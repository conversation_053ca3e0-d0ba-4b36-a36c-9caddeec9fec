import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { findNextLessonUnified } from '@/services/course/courseApi';

const NavigationTester: React.FC = () => {
  const [lessonSlug, setLessonSlug] = useState('patient-history-and-explanation-of-procedure');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testNavigation = async () => {
    if (!lessonSlug.trim()) {
      setError('Please enter a lesson slug');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('[NAVIGATION TESTER] Testing navigation for:', lessonSlug);
      const navigationResult = await findNext<PERSON><PERSON>onUnified(lessonSlug);
      console.log('[NAVIGATION TESTER] Result:', navigationResult);
      setResult(navigationResult);
    } catch (err: any) {
      console.error('[NAVIGATION TESTER] Error:', err);
      setError(err.message || 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testLessons = [
    'patient-history-and-explanation-of-procedure',
    'seeking-consent-during-iv-cannulation',
    'medico-legal-issues-in-iv-cannulation-and-contrast-administration',
    'constrast-media-and-its-pharmacology',
    'additional-precautions-for-iv-cannulation'
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Navigation Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="lesson-slug" className="text-sm font-medium">
            Lesson Slug:
          </label>
          <Input
            id="lesson-slug"
            value={lessonSlug}
            onChange={(e) => setLessonSlug(e.target.value)}
            placeholder="Enter lesson slug to test"
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={testNavigation} disabled={loading}>
            {loading ? 'Testing...' : 'Test Navigation'}
          </Button>
          
          {testLessons.map((slug) => (
            <Button
              key={slug}
              variant="outline"
              size="sm"
              onClick={() => setLessonSlug(slug)}
            >
              {slug.split('-').slice(0, 2).join('-')}...
            </Button>
          ))}
        </div>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="font-medium text-red-800">Error:</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {result && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <h3 className="font-medium text-green-800 mb-2">Navigation Result:</h3>
            <div className="space-y-1 text-sm">
              <p><strong>Next Lesson Slug:</strong> {result.nextLessonSlug || 'None'}</p>
              <p><strong>Is Last Lesson:</strong> {result.isLastLesson ? 'Yes' : 'No'}</p>
            </div>
            
            {result.nextLessonSlug && (
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLessonSlug(result.nextLessonSlug)}
                >
                  Test Next Lesson
                </Button>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Instructions:</strong></p>
          <p>1. Enter a lesson slug or click one of the preset buttons</p>
          <p>2. Click "Test Navigation" to see what the next lesson should be</p>
          <p>3. Check the browser console for detailed logs</p>
          <p>4. If the result shows the correct next lesson, the navigation logic is working</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default NavigationTester;
