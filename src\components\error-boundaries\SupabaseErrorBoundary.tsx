import React, { Component, ErrorInfo, ReactNode } from 'react';
import { PostgrestError } from '@supabase/postgrest-js';
import { isServiceUnavailableError } from '@/lib/connection-manager';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { AlertCircle, RefreshCw, AlertTriangle } from 'lucide-react';
import { checkSupabaseConnection } from '@/lib/health-check';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  isSupabaseError: boolean;
  isMountingError: boolean;
}

class SupabaseErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      isSupabaseError: false,
      isMountingError: false
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Check for React unmounting errors
    const isMountingError = 
      (error.message && (
        error.message.includes('unmounted') ||
        error.message.includes('memory leak') ||
        error.message.includes('clean up subscriptions') ||
        error.message.includes('removeChild') ||
        error.message.includes('commitDeletion') ||
        error.message.includes('Cannot update a component')
      )) || 
      (error.stack && (
        error.stack.includes('commitDeletion') ||
        error.stack.includes('removeChild')
      ));

    // Check if it's a Supabase error using multiple detection methods
    const isSupabaseError =
      // Check using PostgrestError instance
      error instanceof PostgrestError ||
      // Check using message content
      (error.message && (
        error.message.includes('supabase') ||
        error.message.includes('network') ||
        error.message.includes('connection') ||
        error.message.includes('timeout') ||
        error.message.includes('postgrest') ||
        error.message.includes('503') ||
        error.message.includes('service unavailable')
      )) ||
      // Check using utility function from connection manager
      isServiceUnavailableError(error);

    return {
      hasError: true,
      error,
      isSupabaseError,
      isMountingError
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Supabase Error Boundary caught an error:', errorInfo);
    console.debug('Error details:', error);

    // If error related to unmounting, we can handle it gracefully
    if (this.state.isMountingError) {
      console.log('React mounting/unmounting error detected - will recover automatically');
    }
  }

  handleRetry = async (): Promise<void> => {
    toast.info("Retrying connection...", {
      description: "Attempting to reconnect to the database",
    });

    // Try to check connection first
    try {
      const isConnected = await checkSupabaseConnection();
      if (isConnected) {
        toast.success("Connection restored!", {
          description: "Successfully reconnected to the database"
        });
      } else {
        toast.error("Connection failed", {
          description: "Could not establish a database connection"
        });
        // Still reset error state to allow another retry
      }
    } catch (error) {
      console.error('Connection check failed:', error);
      toast.error("Connection check failed", {
        description: "Could not verify database connection status"
      });
      // Continue anyway - we'll reset the error state to allow a retry
    }

    // Reset the error state
    this.setState({
      hasError: false,
      error: null,
      isSupabaseError: false,
      isMountingError: false
    });
  }

  render(): ReactNode {
    const { hasError, error, isSupabaseError, isMountingError } = this.state;
    const { children, fallback } = this.props;

    // If it's just a mounting error, we can try to recover automatically
    if (hasError && isMountingError && !isSupabaseError) {
      // Auto-recover from mounting errors
      setTimeout(() => {
        this.setState({
          hasError: false,
          error: null,
          isSupabaseError: false,
          isMountingError: false
        });
      }, 1000);

      // Show a simple loading state while recovering
      return (
        <div className="p-4 text-center">
          <p className="text-sm text-muted-foreground">Recovering...</p>
        </div>
      );
    }

    if (hasError) {
      // If it's a custom fallback, use that
      if (fallback) {
        return fallback;
      }

      // For Supabase/service unavailability errors, show specific UI with amber/warning colors
      if (isSupabaseError) {
        return (
          <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-primary/20 bg-primary/5 p-6 text-center dark:border-primary/20 dark:bg-primary/10">
            <AlertTriangle className="h-10 w-10 text-primary dark:text-primary-foreground" />
            <h3 className="mt-4 text-lg font-medium text-primary-foreground dark:text-primary-foreground">
              Database Service Unavailable
            </h3>
            <p className="mt-2 text-sm text-primary/80 dark:text-primary-foreground/80">
              We're having trouble connecting to our backend service. This may be temporary.
            </p>
            <div className="flex gap-4 mt-4">
              <Button
                variant="outline"
                className="bg-white dark:bg-primary/20"
                onClick={this.handleRetry}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant="ghost"
                className="text-primary dark:text-primary-foreground"
              >
                Reload Page
              </Button>
            </div>
          </div>
        );
      }

      // For other errors, show a general error message with red/error colors
      return (
        <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-primary/20 bg-primary/5 p-6 text-center dark:border-primary/20 dark:bg-primary/10">
          <AlertCircle className="h-10 w-10 text-primary dark:text-primary-foreground" />
          <h3 className="mt-4 text-lg font-medium text-primary-foreground dark:text-primary-foreground">
            Application Error
          </h3>
          <p className="mt-2 text-sm text-primary/80 dark:text-primary-foreground/80">
            {error?.message || 'An unexpected error occurred'}
          </p>
          <div className="flex gap-4 mt-4">
            <Button
              variant="outline"
              className="bg-white dark:bg-primary/20"
              onClick={this.handleRetry}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant="ghost"
              className="text-primary dark:text-primary-foreground"
            >
              Reload Page
            </Button>
          </div>
          {error && process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-4 bg-muted rounded text-sm overflow-auto max-w-full max-h-[200px]">
              <p className="font-mono">{error.toString()}</p>
              {error.stack && (
                <pre className="mt-2 text-xs whitespace-pre-wrap">{error.stack}</pre>
              )}
            </div>
          )}
        </div>
      );
    }

    return children;
  }
}

export default SupabaseErrorBoundary;