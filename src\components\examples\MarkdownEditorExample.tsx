import React, { useState } from 'react';
import { AdvancedMarkdownEditor } from '@/components/ui/advanced-markdown-editor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getMarkdownStats } from '@/lib/markdown-utils';

/**
 * Example component showing how to integrate the Advanced Markdown Editor
 * into your existing application
 */
export function MarkdownEditorExample() {
  const [content, setContent] = useState(`# Welcome to the Advanced Markdown Editor

This is a simple example showing how to integrate the editor into your application.

## Features

- **Rich text editing** with live preview
- **GitHub Flavored Markdown** support
- **Image upload** functionality
- **Code syntax highlighting**
- **Table editing**
- **Task lists**
- **Callouts and collapsible sections**

## Getting Started

1. Import the \`AdvancedMarkdownEditor\` component
2. Set up state for your content
3. Handle the \`onChange\` callback
4. Customize the editor props as needed

\`\`\`typescript
import { AdvancedMarkdownEditor } from '@/components/ui/advanced-markdown-editor';

function MyComponent() {
  const [content, setContent] = useState('');
  
  return (
    <AdvancedMarkdownEditor
      initialContent={content}
      onChange={setContent}
      placeholder="Start writing..."
      showToolbar={true}
      showPreview={true}
    />
  );
}
\`\`\`

Try editing this content to see the editor in action!`);

  const stats = getMarkdownStats(content);

  const handleSave = () => {
    // Here you would typically save the content to your backend
    console.log('Saving content:', content);
    alert('Content saved! (Check console for details)');
  };

  const handleExport = () => {
    // Export the markdown content
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'content.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Markdown Editor Integration Example</h2>
          <p className="text-muted-foreground">
            This example shows how to use the Advanced Markdown Editor in your application
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleSave} variant="default">
            Save Content
          </Button>
          <Button onClick={handleExport} variant="outline">
            Export Markdown
          </Button>
        </div>
      </div>

      {/* Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Content Statistics</CardTitle>
          <CardDescription>Real-time statistics about your content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Words:</span>
              <Badge variant="secondary">{stats.wordCount}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Characters:</span>
              <Badge variant="secondary">{stats.characterCount}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Headings:</span>
              <Badge variant="secondary">{stats.headingCount}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Images:</span>
              <Badge variant="secondary">{stats.imageCount}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Reading Time:</span>
              <Badge variant="secondary">{stats.readingTimeMinutes} min</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Editor */}
      <Card>
        <CardContent className="p-0">
          <AdvancedMarkdownEditor
            initialContent={content}
            onChange={setContent}
            placeholder="Start writing your content..."
            minHeight={500}
            showToolbar={true}
            showPreview={true}
            className="border-0 rounded-lg"
          />
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Integration Guide</CardTitle>
          <CardDescription>How to use this editor in your own components</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Basic Usage</h4>
            <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
{`import { AdvancedMarkdownEditor } from '@/components/ui/advanced-markdown-editor';

function MyComponent() {
  const [content, setContent] = useState('');
  
  return (
    <AdvancedMarkdownEditor
      initialContent={content}
      onChange={setContent}
      placeholder="Start writing..."
    />
  );
}`}
            </pre>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Available Props</h4>
            <div className="space-y-2 text-sm">
              <div><code className="bg-muted px-2 py-1 rounded">initialContent</code> - Initial markdown content</div>
              <div><code className="bg-muted px-2 py-1 rounded">onChange</code> - Callback when content changes</div>
              <div><code className="bg-muted px-2 py-1 rounded">placeholder</code> - Placeholder text</div>
              <div><code className="bg-muted px-2 py-1 rounded">showToolbar</code> - Show/hide the toolbar</div>
              <div><code className="bg-muted px-2 py-1 rounded">showPreview</code> - Show/hide preview tabs</div>
              <div><code className="bg-muted px-2 py-1 rounded">minHeight</code> - Minimum editor height</div>
              <div><code className="bg-muted px-2 py-1 rounded">autoFocus</code> - Auto-focus the editor</div>
              <div><code className="bg-muted px-2 py-1 rounded">courseId</code> - Course ID for image uploads</div>
              <div><code className="bg-muted px-2 py-1 rounded">moduleId</code> - Module ID for image uploads</div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Features Included</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Full GitHub Flavored Markdown support</li>
              <li>• Live preview with split-pane view</li>
              <li>• Image upload with drag & drop</li>
              <li>• Syntax highlighting for code blocks</li>
              <li>• Table editing with visual controls</li>
              <li>• Task lists with interactive checkboxes</li>
              <li>• Callouts and collapsible sections</li>
              <li>• Dark mode support</li>
              <li>• Fullscreen editing mode</li>
              <li>• Export to markdown</li>
              <li>• Keyboard shortcuts</li>
              <li>• Mobile responsive design</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
