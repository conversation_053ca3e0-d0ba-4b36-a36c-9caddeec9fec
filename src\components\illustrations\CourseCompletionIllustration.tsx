import React from 'react';
import { useTheme } from '@/components/theme/theme-provider';
import { motion } from 'framer-motion';
import { useMotion } from '@/context/MotionContext';

interface CourseCompletionIllustrationProps {
  className?: string;
  width?: number;
  height?: number;
  animate?: boolean;
}

export function CourseCompletionIllustration({
  className = '',
  width = 300,
  height = 200,
  animate = true
}: CourseCompletionIllustrationProps) {
  const { theme } = useTheme();
  const { shouldReduceMotion } = useMotion();

  const isDark = theme === 'dark';

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceMotion ? 0.05 : 0.1,
        delayChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: shouldReduceMotion ? 5 : 10 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: shouldReduceMotion ? 0.2 : 0.4,
        ease: "easeOut"
      }
    }
  };

  // Confetti animation
  const confetti = {
    hidden: { opacity: 0, y: -20 },
    show: {
      opacity: [0, 1, 0],
      y: [0, 100],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeOut",
        times: [0, 0.5, 1]
      }
    }
  };

  // Colors based on theme with red color scheme
  const colors = {
    primary: isDark ? '#e63946' : '#e63946', // Red
    secondary: isDark ? '#c1121f' : '#c1121f', // Darker red
    accent: isDark ? '#f87171' : '#f87171', // Light red
    background: isDark ? '#1e293b' : '#f8fafc',
    outline: isDark ? '#334155' : '#e2e8f0',
    text: isDark ? '#94a3b8' : '#64748b',
    success: isDark ? '#e63946' : '#e63946', // Red
    confetti1: '#f472b6', // Pink
    confetti2: '#e63946', // Red
    confetti3: '#fbbf24', // Yellow
    confetti4: '#e63946', // Red
  };

  return (
    <motion.svg
      width={width}
      height={height}
      viewBox="0 0 300 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      variants={container}
      initial={animate ? "hidden" : undefined}
      animate={animate ? "show" : undefined}
    >
      {/* Certificate */}
      <motion.rect x="75" y="50" width="150" height="100" rx="8" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
      <motion.rect x="85" y="60" width="130" height="80" rx="4" stroke={colors.primary} strokeWidth="1" strokeDasharray="2 2" variants={item} />

      {/* Medal/Badge */}
      <motion.circle cx="150" cy="90" r="20" fill={colors.accent} variants={item} />
      <motion.path d="M150 75 L153 82 L160 83 L155 88 L156 95 L150 92 L144 95 L145 88 L140 83 L147 82 Z" fill="white" variants={item} />

      {/* Text lines */}
      <motion.rect x="110" y="120" width="80" height="6" rx="3" fill={colors.text} variants={item} />
      <motion.rect x="120" y="130" width="60" height="6" rx="3" fill={colors.text} opacity="0.7" variants={item} />

      {/* Confetti - only animate if animation is enabled and reduced motion is not preferred */}
      {animate && !shouldReduceMotion && (
        <>
          <motion.circle cx="80" cy="30" r="5" fill={colors.confetti1} variants={confetti} custom={0} />
          <motion.circle cx="220" cy="40" r="4" fill={colors.confetti2} variants={confetti} custom={0.2} />
          <motion.circle cx="60" cy="50" r="3" fill={colors.confetti3} variants={confetti} custom={0.4} />
          <motion.circle cx="240" cy="60" r="6" fill={colors.confetti4} variants={confetti} custom={0.6} />
          <motion.circle cx="100" cy="20" r="4" fill={colors.confetti2} variants={confetti} custom={0.8} />
          <motion.circle cx="200" cy="30" r="3" fill={colors.confetti3} variants={confetti} custom={1} />
          <motion.circle cx="120" cy="40" r="5" fill={colors.confetti4} variants={confetti} custom={1.2} />
          <motion.circle cx="180" cy="25" r="4" fill={colors.confetti1} variants={confetti} custom={1.4} />
        </>
      )}

      {/* Stars */}
      <motion.circle cx="230" cy="70" r="8" fill={colors.primary} opacity="0.6" variants={item} />
      <motion.circle cx="70" cy="130" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
      <motion.circle cx="210" cy="140" r="6" fill={colors.accent} opacity="0.6" variants={item} />
    </motion.svg>
  );
}
