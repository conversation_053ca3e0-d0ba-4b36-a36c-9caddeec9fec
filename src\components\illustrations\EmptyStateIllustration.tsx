import React from 'react';
import { useTheme } from '@/components/theme/theme-provider';
import { motion } from 'framer-motion';
import { useMotion } from '@/context/MotionContext';

interface EmptyStateIllustrationProps {
  type?: 'courses' | 'search' | 'generic';
  className?: string;
  width?: number;
  height?: number;
}

export function EmptyStateIllustration({
  type = 'generic',
  className = '',
  width = 200,
  height = 200
}: EmptyStateIllustrationProps) {
  const { theme } = useTheme();
  const { shouldReduceMotion } = useMotion();
  
  const isDark = theme === 'dark';
  
  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceMotion ? 0.05 : 0.1,
        delayChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: shouldReduceMotion ? 5 : 10 },
    show: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: shouldReduceMotion ? 0.2 : 0.4,
        ease: "easeOut"
      }
    }
  };

  // Colors based on theme
  const colors = {
    primary: isDark ? '#60a5fa' : '#3b82f6',
    secondary: isDark ? '#4f46e5' : '#6366f1',
    accent: isDark ? '#f97316' : '#f59e0b',
    background: isDark ? '#1e293b' : '#f8fafc',
    outline: isDark ? '#334155' : '#e2e8f0',
    text: isDark ? '#94a3b8' : '#64748b',
  };

  // Render different illustrations based on type
  if (type === 'courses') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.rect x="40" y="50" width="120" height="100" rx="8" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.rect x="50" y="70" width="100" height="10" rx="2" fill={colors.outline} variants={item} />
        <motion.rect x="50" y="90" width="80" height="6" rx="2" fill={colors.text} variants={item} />
        <motion.rect x="50" y="102" width="60" height="6" rx="2" fill={colors.text} variants={item} />
        <motion.rect x="50" y="120" width="100" height="8" rx="4" fill={colors.primary} variants={item} />
        <motion.circle cx="140" cy="60" r="15" fill={colors.accent} variants={item} />
        <motion.path d="M135 60L140 65L145 55" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" variants={item} />
      </motion.svg>
    );
  }
  
  if (type === 'search') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.circle cx="85" cy="85" r="35" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.path d="M110 110L140 140" stroke={colors.primary} strokeWidth="6" strokeLinecap="round" variants={item} />
        <motion.path d="M85 70V100" stroke={colors.text} strokeWidth="2" strokeLinecap="round" variants={item} />
        <motion.path d="M70 85H100" stroke={colors.text} strokeWidth="2" strokeLinecap="round" variants={item} />
        <motion.rect x="40" y="140" width="120" height="10" rx="5" fill={colors.outline} variants={item} />
      </motion.svg>
    );
  }
  
  // Default generic illustration
  return (
    <motion.svg
      width={width}
      height={height}
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.circle cx="100" cy="100" r="50" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
      <motion.path d="M80 100L95 115L120 85" stroke={colors.primary} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" variants={item} />
      <motion.circle cx="150" cy="60" r="15" fill={colors.accent} variants={item} />
      <motion.circle cx="50" cy="140" r="10" fill={colors.secondary} variants={item} />
    </motion.svg>
  );
}
