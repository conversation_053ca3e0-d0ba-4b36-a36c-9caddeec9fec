import React from 'react';
import { useTheme } from '@/components/theme/theme-provider';
import { motion } from 'framer-motion';
import { useMotion } from '@/context/MotionContext';

interface OnboardingIllustrationProps {
  step: 'welcome' | 'profile' | 'courses' | 'complete' | 'clock' | 'learning';
  className?: string;
  width?: number;
  height?: number;
}

export function OnboardingIllustration({
  step,
  className = '',
  width = 300,
  height = 200
}: OnboardingIllustrationProps) {
  const { theme } = useTheme();
  const { shouldReduceMotion } = useMotion();

  const isDark = theme === 'dark';

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceMotion ? 0.05 : 0.1,
        delayChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: shouldReduceMotion ? 5 : 10 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: shouldReduceMotion ? 0.2 : 0.4,
        ease: "easeOut"
      }
    }
  };

  // Colors based on theme
  const colors = {
    primary: isDark ? '#E63946' : '#E63946', // Red
    secondary: isDark ? '#C1121F' : '#C1121F', // Darker red
    accent: isDark ? '#9D0208' : '#9D0208', // Even darker red
    background: isDark ? '#1C1C1C' : '#FFFFFF', // Background
    outline: isDark ? '#333333' : '#E2E8F0', // Border colors
    text: isDark ? '#CCCCCC' : '#64748B', // Text colors
    success: isDark ? '#E63946' : '#E63946', // Success color (Red)
  };

  // Welcome illustration
  if (step === 'welcome') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.rect x="75" y="40" width="150" height="120" rx="10" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.circle cx="150" cy="80" r="25" fill={colors.primary} variants={item} />
        <motion.path d="M140 80L148 88L160 70" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" variants={item} />
        <motion.rect x="110" y="120" width="80" height="8" rx="4" fill={colors.text} variants={item} />
        <motion.rect x="125" y="135" width="50" height="8" rx="4" fill={colors.accent} variants={item} />
        <motion.circle cx="220" cy="50" r="15" fill={colors.accent} opacity="0.6" variants={item} />
        <motion.circle cx="80" cy="150" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
      </motion.svg>
    );
  }

  // Profile setup illustration
  if (step === 'profile') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.circle cx="150" cy="70" r="40" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.circle cx="150" cy="60" r="15" fill={colors.text} variants={item} />
        <motion.path d="M130 90C130 79.5 139 75 150 75C161 75 170 79.5 170 90" stroke={colors.text} strokeWidth="2" variants={item} />
        <motion.rect x="100" y="120" width="100" height="10" rx="5" fill={colors.outline} variants={item} />
        <motion.rect x="100" y="140" width="80" height="10" rx="5" fill={colors.outline} variants={item} />
        <motion.rect x="100" y="160" width="60" height="10" rx="5" fill={colors.primary} variants={item} />
        <motion.circle cx="220" cy="50" r="15" fill={colors.accent} opacity="0.6" variants={item} />
        <motion.circle cx="80" cy="150" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
      </motion.svg>
    );
  }

  // Course selection illustration
  if (step === 'courses') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.rect x="60" y="50" width="80" height="100" rx="8" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.rect x="70" y="70" width="60" height="8" rx="4" fill={colors.text} variants={item} />
        <motion.rect x="70" y="85" width="40" height="6" rx="3" fill={colors.text} opacity="0.6" variants={item} />
        <motion.rect x="70" y="120" width="60" height="6" rx="3" fill={colors.primary} variants={item} />

        <motion.rect x="160" y="50" width="80" height="100" rx="8" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.rect x="170" y="70" width="60" height="8" rx="4" fill={colors.text} variants={item} />
        <motion.rect x="170" y="85" width="40" height="6" rx="3" fill={colors.text} opacity="0.6" variants={item} />
        <motion.rect x="170" y="120" width="60" height="6" rx="3" fill={colors.accent} variants={item} />

        <motion.circle cx="100" cy="40" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
        <motion.circle cx="200" cy="160" r="15" fill={colors.primary} opacity="0.4" variants={item} />
      </motion.svg>
    );
  }

  // Complete illustration
  if (step === 'complete') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.circle cx="150" cy="100" r="50" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.circle cx="150" cy="100" r="40" fill={colors.success} opacity="0.2" variants={item} />
        <motion.path d="M130 100L145 115L170 85" stroke={colors.success} strokeWidth="6" strokeLinecap="round" strokeLinejoin="round" variants={item} />

        <motion.circle cx="220" cy="60" r="15" fill={colors.accent} opacity="0.6" variants={item} />
        <motion.circle cx="80" cy="140" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
        <motion.circle cx="240" cy="140" r="8" fill={colors.primary} opacity="0.6" variants={item} />
        <motion.circle cx="60" cy="70" r="12" fill={colors.primary} opacity="0.4" variants={item} />
      </motion.svg>
    );
  }

  // Time commitment illustration
  if (step === 'clock') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.circle cx="150" cy="100" r="60" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.circle cx="150" cy="100" r="55" fill={colors.background} stroke={colors.primary} strokeWidth="2" strokeDasharray="4 4" variants={item} />
        <motion.line x1="150" y1="100" x2="150" y2="70" stroke={colors.text} strokeWidth="3" strokeLinecap="round" variants={item} />
        <motion.line x1="150" y1="100" x2="175" y2="100" stroke={colors.primary} strokeWidth="3" strokeLinecap="round" variants={item} />
        <motion.circle cx="150" cy="100" r="5" fill={colors.text} variants={item} />

        <motion.text x="140" y="50" fill={colors.text} fontSize="12" fontWeight="bold" variants={item}>12</motion.text>
        <motion.text x="195" y="105" fill={colors.text} fontSize="12" fontWeight="bold" variants={item}>3</motion.text>
        <motion.text x="147" y="155" fill={colors.text} fontSize="12" fontWeight="bold" variants={item}>6</motion.text>
        <motion.text x="100" y="105" fill={colors.text} fontSize="12" fontWeight="bold" variants={item}>9</motion.text>

        <motion.circle cx="220" cy="50" r="15" fill={colors.accent} opacity="0.6" variants={item} />
        <motion.circle cx="80" cy="150" r="10" fill={colors.secondary} opacity="0.6" variants={item} />
      </motion.svg>
    );
  }

  // Learning style illustration
  if (step === 'learning') {
    return (
      <motion.svg
        width={width}
        height={height}
        viewBox="0 0 300 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        variants={container}
        initial="hidden"
        animate="show"
      >
        <motion.rect x="60" y="60" width="50" height="70" rx="5" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.rect x="65" y="70" width="40" height="5" rx="2" fill={colors.text} variants={item} />
        <motion.rect x="65" y="80" width="40" height="5" rx="2" fill={colors.text} variants={item} />
        <motion.rect x="65" y="90" width="30" height="5" rx="2" fill={colors.text} variants={item} />

        <motion.circle cx="150" cy="90" r="30" fill={colors.primary} opacity="0.2" variants={item} />
        <motion.path d="M140 90 L150 100 L160 90 L150 80 Z" fill={colors.primary} variants={item} />

        <motion.rect x="190" y="60" width="50" height="40" rx="5" fill={colors.background} stroke={colors.outline} strokeWidth="2" variants={item} />
        <motion.rect x="190" y="105" width="50" height="5" rx="2" fill={colors.accent} variants={item} />
        <motion.rect x="190" y="115" width="50" height="5" rx="2" fill={colors.accent} variants={item} />
        <motion.rect x="190" y="125" width="50" height="5" rx="2" fill={colors.accent} variants={item} />

        <motion.circle cx="215" y="80" r="15" fill={colors.secondary} opacity="0.3" variants={item} />
        <motion.path d="M210 80 L225 80 M217.5 72.5 L217.5 87.5" stroke={colors.secondary} strokeWidth="2" variants={item} />

        <motion.circle cx="220" cy="40" r="10" fill={colors.accent} opacity="0.6" variants={item} />
        <motion.circle cx="80" cy="150" r="15" fill={colors.primary} opacity="0.4" variants={item} />
      </motion.svg>
    );
  }

  // Default fallback
  return null;
}
