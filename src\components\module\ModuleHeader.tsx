import React from 'react';
import { cn } from '@/lib/utils';

interface ModuleHeaderProps {
  title: string;
  moduleNumber: number;
  lessonsCount: number;
  totalDuration: number;
  isCompleted: boolean;
  isMobile?: boolean;
}

const ModuleHeader: React.FC<ModuleHeaderProps> = ({
  title,
  moduleNumber,
  lessonsCount,
  totalDuration,
  isCompleted,
  isMobile = false
}) => {
  return (
    <div className="flex flex-col">
      <span className={cn(
        "font-semibold flex items-center",
        isCompleted ? "text-red-600 dark:text-red-400" : "",
        isMobile ? "text-sm" : "text-base"
      )}>
        <span className={cn(
          "inline-flex items-center justify-center bg-red-100 dark:bg-red-950/50 text-red-600 dark:text-red-400 rounded-full text-xs font-bold shadow-sm",
          isMobile ? "w-5 h-5 mr-2 text-[10px]" : "w-6 h-6 mr-2.5"
        )}>
          {moduleNumber}
        </span>
        {title}
      </span>
      {!isMobile ? (
        <div className={cn(
          "text-muted-foreground mt-2 flex items-center flex-wrap gap-2",
          "text-xs"
        )}>
          <span className="bg-gray-100 dark:bg-gray-800 rounded-full px-2.5 py-0.5 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1 text-red-500 dark:text-red-400">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
            </svg>
            {lessonsCount} {lessonsCount === 1 ? 'lesson' : 'lessons'}
          </span>
          <span className="bg-gray-100 dark:bg-gray-800 rounded-full px-2.5 py-0.5 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1 text-red-500 dark:text-red-400">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            {totalDuration} min
          </span>
          {isCompleted && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground text-xs">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              Completed
            </span>
          )}
        </div>
      ) : (
        <div className="text-muted-foreground mt-1 flex items-center flex-wrap gap-1.5 text-[10px]">
          {isCompleted ? (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded-full bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </span>
          ) : (
            <>
              <span className="bg-gray-100 dark:bg-gray-800 rounded-full px-1.5 py-0.5 flex items-center">
                {lessonsCount}
              </span>
              <span className="bg-gray-100 dark:bg-gray-800 rounded-full px-1.5 py-0.5 flex items-center">
                {totalDuration} min
              </span>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ModuleHeader;
