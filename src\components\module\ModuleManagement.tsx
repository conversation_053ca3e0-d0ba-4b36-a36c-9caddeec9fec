import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { processImageForStorage } from '@/lib/local-storage';
import { 
  Edit, 
  Trash2, 
  Plus, 
  ArrowUp, 
  ArrowDown, 
  Loader2, 
  X,
  Save,
  Lock,
  Unlock,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { updateModule } from '@/services/course/courseApi';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Module {
  id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked: boolean;
  is_completed?: boolean;
  course_id: string;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
}

interface ModuleManagementProps {
  courseId: string;
}

const ModuleManagement: React.FC<ModuleManagementProps> = ({ courseId }) => {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingModule, setEditingModule] = useState<Module | null>(null);
  const [moduleToDelete, setModuleToDelete] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch modules for the course
  const { data: modules, isLoading } = useQuery({
    queryKey: ['course-modules', courseId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('*')
        .eq('course_id', courseId)
        .order('module_number', { ascending: true });

      if (error) {
        toast({
          title: "Error fetching modules",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data as Module[];
    },
  });

  // Update module mutation
  const updateModuleMutation = useMutation({
    mutationFn: async (module: Partial<Module> & { id: string }) => {
      const result = await updateModule(module.id, module);
      if (!result) {
        throw new Error('Failed to update module');
      }
      return result;
    },
    onSuccess: () => {
      toast({
        title: "Module updated",
        description: "The module has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['course-modules', courseId] });
      queryClient.invalidateQueries({ queryKey: ['courseModules', courseId] });
      setIsEditDialogOpen(false);
      setEditingModule(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error updating module",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete module mutation
  const deleteModuleMutation = useMutation({
    mutationFn: async (moduleId: string) => {
      const { error } = await supabase
        .from('modules')
        .delete()
        .eq('id', moduleId);

      if (error) {
        throw error;
      }
      return moduleId;
    },
    onSuccess: () => {
      toast({
        title: "Module deleted",
        description: "The module has been deleted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['course-modules', courseId] });
      queryClient.invalidateQueries({ queryKey: ['courseModules', courseId] });
      setModuleToDelete(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting module",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reorder module mutation
  const reorderModuleMutation = useMutation({
    mutationFn: async ({ moduleId, newPosition }: { moduleId: string; newPosition: number }) => {
      // Get the module to reorder
      const moduleToReorder = modules?.find(m => m.id === moduleId);
      if (!moduleToReorder) {
        throw new Error('Module not found');
      }

      // Update the module's position
      const result = await updateModule(moduleId, {
        module_number: newPosition
      });

      if (!result) {
        throw new Error('Failed to update module position');
      }

      // If there's another module at the new position, swap them
      const moduleAtPosition = modules?.find(m => m.module_number === newPosition && m.id !== moduleId);
      if (moduleAtPosition) {
        const swapResult = await updateModule(moduleAtPosition.id, {
          module_number: moduleToReorder.module_number
        });
        if (!swapResult) {
          throw new Error('Failed to swap module positions');
        }
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course-modules', courseId] });
      queryClient.invalidateQueries({ queryKey: ['courseModules', courseId] });
    },
    onError: (error: any) => {
      toast({
        title: "Error reordering modules",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle moving a module up or down
  const handleMoveModule = (moduleId: string, direction: 'up' | 'down') => {
    const moduleIndex = modules?.findIndex(m => m.id === moduleId) || 0;
    if (moduleIndex === -1) return;

    const module = modules?.[moduleIndex];
    if (!module) return;

    const newPosition = direction === 'up' 
      ? Math.max(1, module.module_number - 1)
      : module.module_number + 1;

    reorderModuleMutation.mutate({ moduleId, newPosition });
  };

  // Handle toggling the locked state of a module
  const handleToggleLock = (moduleId: string, currentLockState: boolean) => {
    updateModuleMutation.mutate({
      id: moduleId,
      is_locked: !currentLockState
    });
  };

  // Handle image upload
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please upload an image file",
          variant: "destructive"
        });
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Image too large",
          description: "Please select an image smaller than 5MB",
          variant: "destructive"
        });
        return;
      }

      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);

      toast({
        title: "Image selected",
        description: "The image will be uploaded when you save the module",
      });
    } catch (error) {
      console.error('Error handling image:', error);
      toast({
        title: "Error",
        description: "Failed to process image",
        variant: "destructive"
      });
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
    if (editingModule) {
      setEditingModule({
        ...editingModule,
        image_url: ''
      });
    }
  };

  // Handle opening the edit dialog
  const handleEditModule = (module: Module) => {
    setEditingModule({ ...module });
    
    // Set image preview directly from the module image_url
    setImagePreview(module.image_url || null);
    
    setIsEditDialogOpen(true);
  };

  // Handle saving module changes
  const handleSaveModule = async () => {
    if (!editingModule) return;
    
    let finalImageUrl = editingModule.image_url || '';

    if (imageFile) {
      setIsUploading(true);
      try {
        // Convert the file to a data URL instead of uploading to storage
        const reader = new FileReader();
        const dataUrlPromise = new Promise<string>((resolve, reject) => {
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
        });
        reader.readAsDataURL(imageFile);
        
        const dataUrl = await dataUrlPromise;
        
        // Store the data URL directly
        finalImageUrl = dataUrl;
        console.log('Image processed as data URL');
      } catch (error: any) {
        console.error('Error processing image:', error);
        toast({
          title: "Error",
          description: error.message || "Failed to process image",
          variant: "destructive"
        });
        setIsUploading(false);
        return;
      }
      setIsUploading(false);
    }
    
    try {
      await updateModuleMutation.mutateAsync({
        id: editingModule.id,
        title: editingModule.title,
        slug: editingModule.slug,
        module_number: editingModule.module_number,
        is_locked: editingModule.is_locked,
        image_url: finalImageUrl
      });

      setIsEditDialogOpen(false);
      setEditingModule(null);
      setImageFile(null);
      setImagePreview(null);
      
      toast({
        title: "Success",
        description: "Module updated successfully",
      });
    } catch (error) {
      console.error('Error updating module:', error);
      toast({
        title: "Error",
        description: "Failed to update module",
        variant: "destructive"
      });
    }
  };

  // Handle confirming module deletion
  const handleConfirmDelete = () => {
    if (moduleToDelete) {
      deleteModuleMutation.mutate(moduleToDelete);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Module Management</CardTitle>
          <CardDescription>
            Manage the modules for this course. You can edit, reorder, or delete modules.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {modules && modules.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {modules.map((module) => (
                  <TableRow key={module.id}>
                    <TableCell className="font-medium">{module.module_number}</TableCell>
                    <TableCell>{module.title}</TableCell>
                    <TableCell>
                      {module.is_locked ? (
                        <span className="flex items-center text-amber-500">
                          <Lock className="h-4 w-4 mr-1" /> Locked
                        </span>
                      ) : (
                        <span className="flex items-center text-green-500">
                          <Unlock className="h-4 w-4 mr-1" /> Unlocked
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMoveModule(module.id, 'up')}
                          disabled={module.module_number <= 1}
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMoveModule(module.id, 'down')}
                          disabled={module.module_number >= (modules?.length || 0)}
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleLock(module.id, module.is_locked)}
                        >
                          {module.is_locked ? (
                            <Unlock className="h-4 w-4" />
                          ) : (
                            <Lock className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditModule(module)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setModuleToDelete(module.id)}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the
                                module and all its lessons.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={handleConfirmDelete}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center p-8 border rounded-md bg-muted/20">
              <p className="text-muted-foreground mb-4">No modules found for this course</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Module Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Module</DialogTitle>
            <DialogDescription>
              Make changes to the module details below.
            </DialogDescription>
          </DialogHeader>
          {editingModule && (
            <div className="space-y-4 py-4">
              {/* Module Image */}
              <div className="space-y-2">
                <Label htmlFor="image">Module Image</Label>
                <div className="flex flex-col space-y-4">
                  {imagePreview ? (
                    <div className="relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border">
                      <img
                        src={imagePreview}
                        alt="Module preview"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2 flex space-x-2">
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="opacity-90"
                          onClick={handleRemoveImage}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                        <Button
                          type="button"
                          variant="secondary"
                          size="sm"
                          className="opacity-90 bg-white"
                          onClick={() => {
                            document.getElementById('module-image')?.click();
                          }}
                        >
                          <Upload className="h-4 w-4 mr-1" />
                          Replace
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      onClick={() => document.getElementById('module-image')?.click()}
                      className="w-full max-w-md aspect-video rounded-lg border-2 border-dashed border-border hover:border-primary/50 transition-colors cursor-pointer flex flex-col items-center justify-center"
                    >
                      <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Click to upload module image
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended size: 1280x720px (max 5MB)
                      </p>
                    </div>
                  )}
                  <input
                    id="module-image"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={editingModule.title}
                  onChange={(e) => setEditingModule({
                    ...editingModule,
                    title: e.target.value
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  value={editingModule.slug}
                  onChange={(e) => setEditingModule({
                    ...editingModule,
                    slug: e.target.value
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="module_number">Module Order</Label>
                <Input
                  id="module_number"
                  type="number"
                  min={1}
                  value={editingModule.module_number}
                  onChange={(e) => setEditingModule({
                    ...editingModule,
                    module_number: parseInt(e.target.value) || 1
                  })}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_locked"
                  checked={editingModule.is_locked}
                  onCheckedChange={(checked) => setEditingModule({
                    ...editingModule,
                    is_locked: checked
                  })}
                />
                <Label htmlFor="is_locked">Lock this module</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSaveModule} 
              disabled={isUploading || updateModuleMutation.isPending}
            >
              {(isUploading || updateModuleMutation.isPending) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {isUploading ? 'Uploading...' : updateModuleMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ModuleManagement;
