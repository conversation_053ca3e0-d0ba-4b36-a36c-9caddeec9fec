import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  DemographicQuestionnaire as DemographicQuestionnaireType,
  DemographicQuestion,
  DemographicFormData
} from '@/types/demographic';
import {
  getActiveDemographicQuestionnaire,
  saveDemographicResponse,
  getUserDemographicResponse
} from '@/services/demographicApi';
import { shouldUseDropdown, getQuestionOptions, getQuestionDefault } from '@/data/countries';

interface DemographicQuestionnaireProps {
  onComplete: () => void;
}

export function DemographicQuestionnaire({ onComplete }: DemographicQuestionnaireProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [questionnaire, setQuestionnaire] = useState<DemographicQuestionnaireType | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<DemographicFormData>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [completed, setCompleted] = useState(false);

  // Load questionnaire and existing responses
  useEffect(() => {
    const loadQuestionnaire = async () => {
      try {
        const questionnaireData = await getActiveDemographicQuestionnaire();
        if (!questionnaireData) {
          console.warn('No active demographic questionnaire found');
          toast({
            title: "Questionnaire not available",
            description: "Proceeding without demographic questionnaire.",
          });
          // Complete onboarding even if no questionnaire is available
          onComplete();
          return;
        }

        setQuestionnaire(questionnaireData);

        // Load existing responses if any
        if (user) {
          const existingResponse = await getUserDemographicResponse(user.id, questionnaireData.id);
          if (existingResponse) {
            setResponses(existingResponse.responses as DemographicFormData);
            setCompleted(true);
          }
        }
      } catch (error) {
        console.error('Error loading questionnaire:', error);
        toast({
          title: "Error loading questionnaire",
          description: "Proceeding without demographic questionnaire.",
        });
        // Complete onboarding even if there's an error loading the questionnaire
        onComplete();
      } finally {
        setLoading(false);
      }
    };

    loadQuestionnaire();
  }, [user, toast, onComplete]);

  // Get visible questions based on conditional logic
  const getVisibleQuestions = (): DemographicQuestion[] => {
    if (!questionnaire) return [];

    return questionnaire.questions.filter(question => {
      if (!question.conditional) return true;
      
      const conditionValue = responses[question.conditional.field as keyof DemographicFormData];
      return conditionValue === question.conditional.value;
    });
  };

  const visibleQuestions = getVisibleQuestions();
  const currentQuestion = visibleQuestions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / visibleQuestions.length) * 100;

  // Handle answer change
  const handleAnswerChange = (value: string | number) => {
    if (!currentQuestion) return;

    setResponses(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));
  };

  // Check if current question is answered
  const isCurrentQuestionAnswered = () => {
    if (!currentQuestion) return false;
    const answer = responses[currentQuestion.id as keyof DemographicFormData];
    return answer !== undefined && answer !== '';
  };

  // Navigate to next question
  const handleNext = () => {
    if (!isCurrentQuestionAnswered() && currentQuestion?.required) {
      toast({
        title: "Answer required",
        description: "Please answer this question before proceeding.",
        variant: "destructive"
      });
      return;
    }

    if (currentQuestionIndex < visibleQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleSubmit();
    }
  };

  // Navigate to previous question
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // Submit questionnaire
  const handleSubmit = async () => {
    if (!user || !questionnaire) return;

    setSaving(true);
    try {
      await saveDemographicResponse(user.id, questionnaire.id, responses);
      setCompleted(true);
      
      toast({
        title: "Questionnaire completed!",
        description: "Thank you for providing your demographic information.",
      });

      // Complete onboarding after a short delay
      setTimeout(() => {
        onComplete();
      }, 1500);
    } catch (error) {
      console.error('Error saving responses:', error);
      toast({
        title: "Error",
        description: "Failed to save your responses. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Set default values for questions (moved outside render function)
  useEffect(() => {
    if (currentQuestion) {
      const defaultValue = getQuestionDefault(currentQuestion.id);
      const currentValue = responses[currentQuestion.id as keyof DemographicFormData];

      if (defaultValue && !currentValue && currentQuestion.id === 'country') {
        handleAnswerChange(defaultValue);
      }
    }
  }, [currentQuestion?.id, responses]);

  // Render question input based on type
  const renderQuestionInput = () => {
    if (!currentQuestion) return null;

    const currentValue = responses[currentQuestion.id as keyof DemographicFormData];

    // Determine if this question should use dropdown
    const useDropdown = shouldUseDropdown(currentQuestion) || currentQuestion.type === 'dropdown';

    // Get options for this question (with special handling for country)
    const questionOptions = getQuestionOptions(currentQuestion.id, currentQuestion.options);

    // Get default value for this question
    const defaultValue = getQuestionDefault(currentQuestion.id);

    if (useDropdown && questionOptions.length > 0) {
      // Render dropdown
      return (
        <div className="w-full">
          <div className="relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200">
            <Select
              value={currentValue as string || defaultValue || ''}
              onValueChange={handleAnswerChange}
            >
              <SelectTrigger className="w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium">
                <SelectValue placeholder={`Select your ${currentQuestion.question.toLowerCase().replace('?', '')}`} />
              </SelectTrigger>
              <SelectContent className="max-h-48 sm:max-h-60 z-50">
                {questionOptions.map((option) => (
                  <SelectItem key={option} value={option} className="text-sm sm:text-base py-2 sm:py-3">
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      );
    }

    switch (currentQuestion.type) {
      case 'single_choice':
        return (
          <div className="space-y-2 sm:space-y-3">
            {currentQuestion.options?.map((option, index) => (
              <motion.div
                key={option}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "relative flex items-center justify-between py-2.5 sm:py-4 px-3 sm:px-6 cursor-pointer transition-all duration-200 rounded-md sm:rounded-lg border-2",
                  currentValue === option
                    ? "border-primary bg-primary/10 dark:bg-primary/20 shadow-sm"
                    : "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800/70 hover:border-gray-300 dark:hover:border-gray-600"
                )}
                onClick={() => handleAnswerChange(option)}
              >
                <div className="flex-1 text-sm sm:text-base font-medium text-foreground pr-3">
                  {option}
                </div>
                <div className="flex-shrink-0">
                  <div className={cn(
                    "w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 transition-all duration-200 flex items-center justify-center",
                    currentValue === option
                      ? "border-primary bg-primary shadow-sm"
                      : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                  )}>
                    {currentValue === option && (
                      <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full bg-white" />
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );

      case 'text':
        return (
          <div className="w-full">
            <div className="relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200">
              <Input
                type="text"
                value={currentValue as string || ''}
                onChange={(e) => handleAnswerChange(e.target.value)}
                placeholder="Enter your answer"
                className="w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium placeholder:text-gray-400"
              />
            </div>
          </div>
        );

      case 'number':
        return (
          <div className="w-full">
            <div className="relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200">
              <Input
                type="number"
                value={currentValue as number || ''}
                onChange={(e) => handleAnswerChange(parseInt(e.target.value) || '')}
                placeholder="Enter your age"
                className="w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium placeholder:text-gray-400"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="w-full h-screen overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6"
        >
          <div className="min-h-screen flex flex-col justify-center items-center">
            <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center">
              <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-primary mx-auto mb-4 sm:mb-6"></div>
              <h3 className="text-base sm:text-xl font-semibold mb-2">Loading Questionnaire</h3>
              <p className="text-sm text-muted-foreground">Please wait while we prepare your questionnaire...</p>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  if (completed) {
    return (
      <div className="w-full h-screen overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6"
        >
          <div className="min-h-screen flex flex-col justify-center items-center">
            <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                <CheckCircle className="h-14 sm:h-20 w-14 sm:w-20 text-green-500 mx-auto mb-4 sm:mb-6" />
              </motion.div>
              <h3 className="text-lg sm:text-2xl font-bold mb-3 sm:mb-4 text-green-600">Questionnaire Completed!</h3>
              <p className="text-sm sm:text-lg text-muted-foreground mb-3 sm:mb-4">
                Thank you for providing your demographic information.
              </p>
              <p className="text-xs sm:text-sm text-muted-foreground">
                You will be redirected to your dashboard shortly...
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  if (!questionnaire || visibleQuestions.length === 0) {
    return (
      <div className="w-full h-screen overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6"
        >
          <div className="min-h-screen flex flex-col justify-center items-center">
            <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center">
              <AlertCircle className="h-10 sm:h-16 w-10 sm:w-16 text-amber-500 mx-auto mb-4 sm:mb-6" />
              <h3 className="text-base sm:text-xl font-semibold mb-2">Questionnaire Unavailable</h3>
              <p className="text-sm text-muted-foreground mb-4 sm:mb-6">
                The demographic questionnaire is currently not available.
              </p>
              <Button onClick={onComplete} variant="outline" size="sm" className="h-9 sm:h-11">
                Continue to Dashboard
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full h-screen overflow-y-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6"
      >
        {/* Header Section - Compact for Mobile */}
        <div className="mb-4 sm:mb-6">
          <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6">
            <div className="text-center">
              <h1 className="text-lg sm:text-2xl font-bold text-foreground mb-1 sm:mb-2">{questionnaire.title}</h1>
              <div className="text-xs sm:text-sm font-medium text-muted-foreground mb-2 sm:mb-4">
                Question {currentQuestionIndex + 1} of {visibleQuestions.length}
              </div>
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 sm:h-2">
                <div
                  className="bg-primary h-1.5 sm:h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${((currentQuestionIndex + 1) / visibleQuestions.length) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content Section - Compact and Scrollable */}
        <div className="mb-4 sm:mb-6">
          <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6 lg:p-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentQuestionIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-4 sm:space-y-6"
              >
                {/* Question - Compact */}
                <div className="text-center mb-4 sm:mb-6">
                  <h2 className="text-base sm:text-xl font-semibold text-foreground leading-relaxed">
                    {currentQuestion?.question}
                    {currentQuestion?.required && <span className="text-red-500 ml-1">*</span>}
                  </h2>
                </div>

                {/* Answer Options - Compact */}
                <div className="space-y-2 sm:space-y-4 max-w-2xl mx-auto">
                  {renderQuestionInput()}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Footer Section - Always Accessible */}
        <div className="pb-4 sm:pb-6">
          <div className="bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6">
            <div className="flex justify-between items-center gap-3 sm:gap-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentQuestionIndex === 0}
                size="sm"
                className="min-w-[80px] sm:min-w-[120px] text-xs sm:text-sm font-medium h-9 sm:h-11"
              >
                Previous
              </Button>

              <div className="text-center hidden sm:block">
                <span className="text-xs text-muted-foreground">
                  {currentQuestionIndex + 1} / {visibleQuestions.length}
                </span>
              </div>

              <Button
                onClick={handleNext}
                disabled={saving}
                size="sm"
                className={cn(
                  "min-w-[80px] sm:min-w-[120px] text-xs sm:text-sm font-medium h-9 sm:h-11",
                  currentQuestionIndex === visibleQuestions.length - 1 && "bg-primary hover:bg-primary/90"
                )}
              >
                {saving ? (
                  <div className="flex items-center gap-1 sm:gap-2">
                    <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white"></div>
                    <span className="hidden sm:inline">Submitting...</span>
                    <span className="sm:hidden">...</span>
                  </div>
                ) : currentQuestionIndex === visibleQuestions.length - 1 ? (
                  <span>
                    <span className="hidden sm:inline">Complete Questionnaire</span>
                    <span className="sm:hidden">Complete</span>
                  </span>
                ) : (
                  "Next"
                )}
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
