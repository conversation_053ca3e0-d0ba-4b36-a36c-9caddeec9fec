import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { OnboardingIllustration } from '@/components/illustrations/OnboardingIllustration';
import { useMotion } from '@/context/MotionContext';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { AlertCircle } from 'lucide-react';
import { DemographicQuestionnaire } from './DemographicQuestionnaire';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

interface OnboardingFlowProps {
  onComplete: () => void;
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [open, setOpen] = useState(true);
  const [step, setStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const { user, setUser } = useAuth();
  const { shouldReduceMotion, transitions } = useMotion();

  // Handle questionnaire completion
  const handleQuestionnaireComplete = () => {
    handleComplete();
  };

  // Handle onboarding completion
  const handleComplete = async () => {
    if (!user) return;

    setLoading(true);

    try {
      // Update user metadata with completion flags
      const { data, error } = await supabase.auth.updateUser({
        data: {
          onboarding_completed: true,
          demographic_questionnaire_completed: true
        }
      });

      if (error) {
        console.error('Error updating user:', error);
      } else if (data.user) {
        // Also update user preferences
        const { error: prefError } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: user.id,
            demographic_questionnaire_completed: true
          });

        if (prefError) {
          console.error('Error updating preferences:', prefError);
        }

        // Update local user state
        setUser?.(data.user);

        // Store onboarding completion in localStorage
        localStorage.setItem('onboarding_completed', 'true');

        // Close dialog and notify parent
        setOpen(false);
        onComplete();
      }
    } catch (error) {
      console.error('Error in onboarding completion:', error);
    } finally {
      setLoading(false);
    }
  };



  // Define steps
  const steps = [
    {
      title: 'Welcome to Intravaneous Cannulation Elearning Platform',
      description: 'Let\'s get you set up to start learning.',
      illustration: 'welcome',
      content: (
        <div className="text-center">
          <p className="text-muted-foreground mb-6">
            Welcome! Before you begin your learning journey, we need you to complete a short questionnaire.
          </p>
          <Button onClick={() => nextStep()}>
            Get Started
          </Button>
        </div>
      )
    },
    {
      title: 'Complete the Demographic Questionnaire',
      description: 'Required step before you can access courses.',
      illustration: 'courses',
      content: (
        <DemographicQuestionnaire onComplete={handleQuestionnaireComplete} />
      )
    }
  ];

  // Animation variants
  const contentVariants = {
    hidden: { opacity: 0, x: shouldReduceMotion ? 0 : 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: transitions.default
    },
    exit: {
      opacity: 0,
      x: shouldReduceMotion ? 0 : -20,
      transition: transitions.default
    }
  };

  // Navigation
  const nextStep = () => {
    if (step < steps.length - 1) {
      setStep(step + 1);
    }
  };

  // Check if current step is demographic questionnaire
  const isDemographicStep = step === 1; // Assuming demographic questionnaire is step 1

  if (isDemographicStep) {
    // Full-screen layout for demographic questionnaire
    return (
      <div className="fixed inset-0 bg-background z-50">
        {steps[step].content}
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="w-[95vw] max-w-[95vw] sm:max-w-[500px] p-0 overflow-hidden"
        onInteractOutside={(e) => e.preventDefault()} // Prevent closing by clicking outside
        onEscapeKeyDown={(e) => e.preventDefault()} // Prevent closing with Escape key
      >
        <VisuallyHidden>
          <DialogTitle>{steps[step].title}</DialogTitle>
          <DialogDescription>{steps[step].description}</DialogDescription>
        </VisuallyHidden>
        <div className="relative">
          {/* Progress indicator */}
          <div className="absolute top-4 left-0 right-0 flex justify-center">
            <div className="flex gap-1">
              {steps.map((_, i) => (
                <div
                  key={i}
                  className={`h-1.5 rounded-full transition-all duration-300 ${
                    i === step ? 'w-6 bg-primary' : 'w-2 bg-primary/30'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Illustration */}
          <div className="bg-muted/30 p-4 sm:p-6 flex justify-center">
            <OnboardingIllustration
              step={steps[step].illustration as any}
              width={window.innerWidth < 640 ? 240 : 280}
              height={window.innerWidth < 640 ? 160 : 180}
            />
          </div>

          {/* Content */}
          <div className="p-4 sm:p-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-1">{steps[step].title}</h2>
            <p className="text-muted-foreground text-xs sm:text-sm mb-4 sm:mb-6">{steps[step].description}</p>

            <motion.div
              key={step}
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              {steps[step].content}
            </motion.div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
