import React, { useState } from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { LogOut, Camera } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { toast } from 'sonner';
import ProfilePictureUpload from './ProfilePictureUpload';

type ProfileHeaderProps = {
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  avatarUrl?: string | null;
  onSignOut: () => Promise<void>;
  onAvatarUpdate?: (url: string) => void;
};

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  firstName,
  lastName,
  email,
  avatarUrl,
  onSignOut,
  onAvatarUpdate
}) => {
  const { role } = useUserRole();
  const { user } = useAuth();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  const getInitials = () => {
    if (firstName) {
      return `${firstName[0]}${lastName ? lastName[0] : ''}`.toUpperCase();
    }
    return email?.[0].toUpperCase() || 'U';
  };

  const handleAvatarSuccess = (newAvatarUrl: string) => {
    if (onAvatarUpdate) {
      onAvatarUpdate(newAvatarUrl);
    }
  };

  const handleSignOutClick = async () => {
    try {
      console.log('ProfileHeader: Handling sign out button click');
      await onSignOut();
      console.log('ProfileHeader: Sign out complete');
    } catch (error) {
      console.error('ProfileHeader: Error during sign out:', error);
      toast.error('Failed to sign out. Please try again.');
    }
  };

  return (
    <div className="mb-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
      >
        <div className="flex items-center gap-4">
          <div className="relative">
            <Avatar className="w-20 h-20 text-xl border-4 border-white shadow-lg">
              {avatarUrl ? (
                <AvatarImage src={avatarUrl} alt={`${firstName || ''} ${lastName || ''}`} />
              ) : (
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-full h-full flex items-center justify-center text-white font-medium">
                  {getInitials()}
                </AvatarFallback>
              )}
            </Avatar>
            <button
              onClick={() => setIsUploadModalOpen(true)}
              className="absolute bottom-0 right-0 bg-blue-600 text-white p-1.5 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
              aria-label="Update profile picture"
            >
              <Camera className="w-4 h-4" />
            </button>

            {user && (
              <ProfilePictureUpload
                userId={user.id}
                open={isUploadModalOpen}
                onOpenChange={setIsUploadModalOpen}
                onSuccess={handleAvatarSuccess}
              />
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              {firstName || ''} {lastName || ''}
            </h1>
            <p className="text-muted-foreground">{email}</p>
            {role && (
              <div className="flex items-center gap-2 mt-1">
                <span className="inline-block px-2 py-1 text-xs font-medium rounded bg-primary/10 text-primary">
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </span>
                {/* Removed 'Become a Teacher' button */}
              </div>
            )}
          </div>
        </div>
        <Button variant="outline" onClick={handleSignOutClick} className="flex items-center gap-2">
          <LogOut className="w-4 h-4" />
          Sign out
        </Button>
      </motion.div>
    </div>
  );
};
