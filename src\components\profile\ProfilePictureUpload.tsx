import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Camera, Upload, X, Image as ImageIcon } from 'lucide-react';
import { toast } from 'sonner';
import { uploadProfilePicture, generateImagePreview } from '@/lib/avatar-upload';

interface ProfilePictureUploadProps {
  userId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: (avatarUrl: string) => void;
}

const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
  userId,
  open,
  onOpenChange,
  onSuccess
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Generate preview
      const dataUrl = await generateImagePreview(file);
      setSelectedFile(file);
      setPreview(dataUrl);
    } catch (error: any) {
      console.error('Error handling file:', error);
      toast.error(error.message || 'Failed to process image');
    }
  };

  // This function is now handled by handleUploadWithDelay

  // Add a small delay to ensure UI is responsive during upload
  const handleUploadWithDelay = () => {
    if (!selectedFile || !userId) return;

    setUploading(true);

    // Add a small delay to ensure UI is responsive
    setTimeout(async () => {
      try {
        // Call the original upload function
        if (!selectedFile || !userId) {
          setUploading(false);
          return;
        }

        // Upload the profile picture using our helper function
        const publicUrl = await uploadProfilePicture(userId, selectedFile);

        // Call the success callback with the new URL
        onSuccess(publicUrl);

        toast.success('Profile picture updated successfully');
        onOpenChange(false);
      } catch (error: any) {
        console.error('Error uploading profile picture:', error);
        toast.error(error.message || 'Failed to upload profile picture');
      } finally {
        setUploading(false);
      }
    }, 100);
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setPreview(null);
    onOpenChange(false);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const clearSelectedFile = () => {
    setSelectedFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Profile Picture</DialogTitle>
          <DialogDescription>
            Upload a new profile picture. The image will be visible on your profile and in the sidebar.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center justify-center gap-4 py-4">
          {preview ? (
            <div className="relative">
              <img
                src={preview}
                alt="Preview"
                className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
              />
              <button
                onClick={clearSelectedFile}
                className="absolute -top-2 -right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <div
              onClick={triggerFileInput}
              className="w-32 h-32 rounded-full bg-gray-100 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors border-4 border-white shadow-lg"
            >
              <ImageIcon className="w-10 h-10 text-gray-400" />
              <span className="text-xs text-gray-500 mt-2">Select Image</span>
            </div>
          )}

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />

          {!preview && (
            <Button
              type="button"
              variant="outline"
              onClick={triggerFileInput}
              className="flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              Choose File
            </Button>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-end gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={uploading}>
            Cancel
          </Button>
          <Button
            onClick={handleUploadWithDelay}
            disabled={!selectedFile || uploading}
            className="flex items-center gap-2"
          >
            {uploading ? 'Uploading...' : 'Upload'}
            {uploading && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProfilePictureUpload;
