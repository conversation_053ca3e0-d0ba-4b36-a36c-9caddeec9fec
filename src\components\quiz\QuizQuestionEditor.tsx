import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { QuizQuestion, QuestionType, createDefaultQuestion } from '@/types/quiz';
import { PlusCircle, MinusCircle, Trash2 } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';

interface QuizQuestionEditorProps {
  question: QuizQuestion;
  index: number;
  onChange: (question: QuizQuestion) => void;
  onRemove: () => void;
}

export const QuizQuestionEditor: React.FC<QuizQuestionEditorProps> = ({
  question,
  index,
  onChange,
  onRemove
}) => {
  const handleQuestionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange({
      ...question,
      question: e.target.value
    });
  };

  const handleTypeChange = (type: QuestionType) => {
    // Create a new question of the selected type, preserving the question text and ID
    const newQuestion = createDefaultQuestion(type);
    onChange({
      ...newQuestion,
      id: question.id,
      question: question.question,
      points: question.points
    });
  };

  const handlePointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const points = parseInt(e.target.value) || 1;
    onChange({
      ...question,
      points: Math.max(1, points)
    });
  };

  const handleOptionChange = (index: number, value: string) => {
    if (question.type !== 'multiple-choice') return;
    
    const newOptions = [...question.options];
    newOptions[index] = value;
    
    onChange({
      ...question,
      options: newOptions
    });
  };

  const handleCorrectAnswerChange = (value: any) => {
    switch (question.type) {
      case 'multiple-choice':
        onChange({
          ...question,
          correctAnswer: parseInt(value)
        });
        break;
      case 'true-false':
        onChange({
          ...question,
          correctAnswer: value === 'true'
        });
        break;
      case 'short-answer':
        onChange({
          ...question,
          correctAnswer: value
        });
        break;
    }
  };

  const addOption = () => {
    if (question.type !== 'multiple-choice') return;
    
    onChange({
      ...question,
      options: [...question.options, '']
    });
  };

  const removeOption = (index: number) => {
    if (question.type !== 'multiple-choice') return;
    
    const newOptions = [...question.options];
    newOptions.splice(index, 1);
    
    // Adjust correctAnswer if needed
    let correctAnswer = question.correctAnswer;
    if (correctAnswer >= newOptions.length) {
      correctAnswer = 0;
    }
    
    onChange({
      ...question,
      options: newOptions,
      correctAnswer
    });
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-base">Question {index + 1}</CardTitle>
        <div className="flex items-center gap-2">
          <Select
            value={question.type}
            onValueChange={(value) => handleTypeChange(value as QuestionType)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Question Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
              <SelectItem value="true-false">True/False</SelectItem>
              <SelectItem value="short-answer">Short Answer</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="destructive"
            size="sm"
            onClick={onRemove}
            className="h-8 w-8 p-0"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor={`question-${question.id}`}>Question</Label>
          <Textarea
            id={`question-${question.id}`}
            value={question.question}
            onChange={handleQuestionChange}
            placeholder="Enter your question"
            className="min-h-[60px]"
          />
        </div>

        <div className="flex items-center gap-4">
          <div className="w-24">
            <Label htmlFor={`points-${question.id}`}>Points</Label>
            <Input
              id={`points-${question.id}`}
              type="number"
              min="1"
              value={question.points}
              onChange={handlePointsChange}
            />
          </div>
        </div>

        {question.type === 'multiple-choice' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Answer Options</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOption}
                className="h-7 px-2 text-xs"
              >
                <PlusCircle className="h-3 w-3 mr-1" /> Add Option
              </Button>
            </div>
            
            <RadioGroup
              value={question.correctAnswer.toString()}
              onValueChange={handleCorrectAnswerChange}
              className="space-y-2"
            >
              {question.options.map((option, i) => (
                <div key={i} className="flex items-center gap-2">
                  <RadioGroupItem value={i.toString()} id={`option-${question.id}-${i}`} />
                  <Input
                    value={option}
                    onChange={(e) => handleOptionChange(i, e.target.value)}
                    placeholder={`Option ${i + 1}`}
                    className="flex-1"
                  />
                  {question.options.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOption(i)}
                      className="h-8 w-8 p-0"
                    >
                      <MinusCircle className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {question.type === 'true-false' && (
          <div className="space-y-3">
            <Label>Correct Answer</Label>
            <RadioGroup
              value={question.correctAnswer ? 'true' : 'false'}
              onValueChange={handleCorrectAnswerChange}
              className="flex gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id={`true-${question.id}`} />
                <Label htmlFor={`true-${question.id}`}>True</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id={`false-${question.id}`} />
                <Label htmlFor={`false-${question.id}`}>False</Label>
              </div>
            </RadioGroup>
          </div>
        )}

        {question.type === 'short-answer' && (
          <div className="space-y-2">
            <Label htmlFor={`answer-${question.id}`}>Correct Answer</Label>
            <Input
              id={`answer-${question.id}`}
              value={question.correctAnswer}
              onChange={(e) => handleCorrectAnswerChange(e.target.value)}
              placeholder="Enter the correct answer"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuizQuestionEditor;
