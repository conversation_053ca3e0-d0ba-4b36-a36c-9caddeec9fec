import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Youtube from '@tiptap/extension-youtube';
import { common, createLowlight } from 'lowlight';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { EnhancedMarkdownPreview } from '@/components/ui/enhanced-markdown-preview';
// import { Details, DetailsSummary, DetailsContent } from '@/lib/tiptap-extensions/details';
// import { Callout } from '@/lib/tiptap-extensions/callout';
import { tiptapToAdvancedMarkdown, htmlToAdvancedMarkdown } from '@/lib/advanced-markdown-serializer';
import { uploadEditorImage, validateImageFile, createImagePreview } from '@/lib/tiptap-image-upload';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  Code,
  Quote,
  Link as LinkIcon,
  Image as ImageIcon,
  Heading1,
  Heading2,
  Heading3,
  HighlighterIcon,
  CheckSquare,
  Undo,
  Redo,
  Type,
  Eye,
  Table as TableIcon,
  Plus,
  Minus,
  Upload,
  Loader2,
  Youtube as YoutubeIcon,
  ChevronDown,
  Info,
  Moon,
  Sun,
  Download,
  Copy,
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

const lowlight = createLowlight(common);

interface AdvancedMarkdownEditorProps {
  initialContent?: string;
  onChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  autoFocus?: boolean;
  courseId?: string;
  moduleId?: string;
  showToolbar?: boolean;
  showPreview?: boolean;
  fullscreen?: boolean;
  onFullscreenChange?: (fullscreen: boolean) => void;
}

export function AdvancedMarkdownEditor({
  initialContent = '',
  onChange,
  placeholder = 'Start writing your content...',
  className = '',
  minHeight = 500,
  autoFocus = false,
  courseId,
  moduleId,
  showToolbar = true,
  showPreview = true,
  fullscreen = false,
  onFullscreenChange,
}: AdvancedMarkdownEditorProps) {
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<'editor' | 'preview' | 'split'>('split');
  const [markdownContent, setMarkdownContent] = useState(initialContent);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [isYoutubeDialogOpen, setIsYoutubeDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(fullscreen);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: 'plaintext',
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto cursor-zoom-in',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          rel: 'noopener noreferrer',
          target: '_blank',
          class: 'text-primary hover:text-primary/80 underline transition-colors',
        },
      }),
      Underline,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded',
        },
      }),
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border rounded-lg overflow-hidden',
        },
      }),
      TableRow,
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-muted font-semibold',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border px-4 py-2',
        },
      }),
      Youtube.configure({
        controls: true,
        nocookie: true,
        HTMLAttributes: {
          class: 'rounded-lg overflow-hidden my-4',
        },
      }),
      // Details,
      // DetailsSummary,
      // DetailsContent,
      // Callout,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: initialContent,
    autofocus: autoFocus,
    onUpdate: ({ editor }) => {
      try {
        const doc = editor.state.doc;
        const markdown = tiptapToAdvancedMarkdown(doc);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      } catch (error) {
        console.error('Error converting to markdown:', error);
        // Fallback to HTML conversion
        const html = editor.getHTML();
        const markdown = htmlToAdvancedMarkdown(html);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      }
    },
  });

  // Sync content when initialContent changes
  useEffect(() => {
    if (editor && initialContent !== markdownContent) {
      setMarkdownContent(initialContent);
      // Convert markdown to HTML for editor
      // This is a simplified approach - in production you'd want a proper markdown parser
      editor.commands.setContent(initialContent);
    }
  }, [initialContent, editor, markdownContent]);

  // Handle fullscreen changes
  useEffect(() => {
    setIsFullscreen(fullscreen);
  }, [fullscreen]);

  const toggleFullscreen = useCallback(() => {
    const newFullscreen = !isFullscreen;
    setIsFullscreen(newFullscreen);
    onFullscreenChange?.(newFullscreen);
  }, [isFullscreen, onFullscreenChange]);

  // Toolbar action handlers
  const handleBold = useCallback(() => editor?.chain().focus().toggleBold().run(), [editor]);
  const handleItalic = useCallback(() => editor?.chain().focus().toggleItalic().run(), [editor]);
  const handleUnderline = useCallback(() => editor?.chain().focus().toggleUnderline().run(), [editor]);
  const handleStrike = useCallback(() => editor?.chain().focus().toggleStrike().run(), [editor]);
  const handleCode = useCallback(() => editor?.chain().focus().toggleCode().run(), [editor]);
  const handleHighlight = useCallback(() => editor?.chain().focus().toggleHighlight().run(), [editor]);
  
  const handleHeading = useCallback((level: 1 | 2 | 3) => {
    editor?.chain().focus().toggleHeading({ level }).run();
  }, [editor]);

  const handleBulletList = useCallback(() => editor?.chain().focus().toggleBulletList().run(), [editor]);
  const handleOrderedList = useCallback(() => editor?.chain().focus().toggleOrderedList().run(), [editor]);
  const handleTaskList = useCallback(() => editor?.chain().focus().toggleTaskList().run(), [editor]);
  const handleBlockquote = useCallback(() => editor?.chain().focus().toggleBlockquote().run(), [editor]);
  const handleCodeBlock = useCallback(() => editor?.chain().focus().toggleCodeBlock().run(), [editor]);
  const handleHorizontalRule = useCallback(() => editor?.chain().focus().setHorizontalRule().run(), [editor]);

  const handleUndo = useCallback(() => editor?.chain().focus().undo().run(), [editor]);
  const handleRedo = useCallback(() => editor?.chain().focus().redo().run(), [editor]);

  // Table actions
  const insertTable = useCallback(() => {
    editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  }, [editor]);

  const addColumnBefore = useCallback(() => editor?.chain().focus().addColumnBefore().run(), [editor]);
  const addColumnAfter = useCallback(() => editor?.chain().focus().addColumnAfter().run(), [editor]);
  const deleteColumn = useCallback(() => editor?.chain().focus().deleteColumn().run(), [editor]);
  const addRowBefore = useCallback(() => editor?.chain().focus().addRowBefore().run(), [editor]);
  const addRowAfter = useCallback(() => editor?.chain().focus().addRowAfter().run(), [editor]);
  const deleteRow = useCallback(() => editor?.chain().focus().deleteRow().run(), [editor]);
  const deleteTable = useCallback(() => editor?.chain().focus().deleteTable().run(), [editor]);

  // Special content actions
  const insertDetails = useCallback(() => {
    // editor?.chain().focus().setDetails().run();
    editor?.chain().focus().insertContent('<details><summary>Click to expand</summary><p>Content goes here...</p></details>').run();
  }, [editor]);

  const insertCallout = useCallback((type: 'info' | 'warning' | 'success' | 'error' | 'tip' = 'info') => {
    // editor?.chain().focus().setCallout(type).run();
    const calloutText = `> [!${type.toUpperCase()}]\n> This is a ${type} callout with important information.`;
    editor?.chain().focus().insertContent(calloutText).run();
  }, [editor]);

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={cn(
        'advanced-markdown-editor border border-border rounded-lg overflow-hidden bg-background',
        isFullscreen && 'fixed inset-0 z-50 rounded-none border-0',
        className
      )}>
        {/* Toolbar */}
        {showToolbar && (
          <div className="border-b border-border bg-muted/50 p-2">
            <div className="flex items-center gap-1 flex-wrap">
              {/* Text formatting */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('bold') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleBold}
                    >
                      <Bold className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Bold (Ctrl+B)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('italic') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleItalic}
                    >
                      <Italic className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Italic (Ctrl+I)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('underline') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleUnderline}
                    >
                      <UnderlineIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Underline (Ctrl+U)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('strike') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleStrike}
                    >
                      <Strikethrough className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Strikethrough</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('code') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleCode}
                    >
                      <Code className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Inline Code</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('highlight') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleHighlight}
                    >
                      <HighlighterIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Highlight</TooltipContent>
                </Tooltip>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* Headings */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('heading', { level: 1 }) ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handleHeading(1)}
                    >
                      <Heading1 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Heading 1</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('heading', { level: 2 }) ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handleHeading(2)}
                    >
                      <Heading2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Heading 2</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('heading', { level: 3 }) ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handleHeading(3)}
                    >
                      <Heading3 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Heading 3</TooltipContent>
                </Tooltip>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* Lists */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('bulletList') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleBulletList}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Bullet List</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('orderedList') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleOrderedList}
                    >
                      <ListOrdered className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Numbered List</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('taskList') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleTaskList}
                    >
                      <CheckSquare className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Task List</TooltipContent>
                </Tooltip>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* More content types */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={editor.isActive('blockquote') ? 'default' : 'ghost'}
                      size="sm"
                      onClick={handleBlockquote}
                    >
                      <Quote className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Quote</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCodeBlock}
                    >
                      <Code className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Code Block</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsLinkDialogOpen(true)}
                    >
                      <LinkIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Insert Link</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsImageDialogOpen(true)}
                    >
                      <ImageIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Insert Image</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={insertTable}
                    >
                      <TableIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Insert Table</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsYoutubeDialogOpen(true)}
                    >
                      <YoutubeIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Insert YouTube Video</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={insertDetails}
                    >
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Insert Collapsible Section</TooltipContent>
                </Tooltip>

                <Select onValueChange={(value) => insertCallout(value as any)}>
                  <SelectTrigger className="w-auto h-8 px-2">
                    <Info className="h-4 w-4" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="info">Info Callout</SelectItem>
                    <SelectItem value="warning">Warning Callout</SelectItem>
                    <SelectItem value="success">Success Callout</SelectItem>
                    <SelectItem value="error">Error Callout</SelectItem>
                    <SelectItem value="tip">Tip Callout</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* Undo/Redo */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleUndo}
                      disabled={!editor.can().undo()}
                    >
                      <Undo className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRedo}
                      disabled={!editor.can().redo()}
                    >
                      <Redo className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
                </Tooltip>
              </div>

              {/* Right side controls */}
              <div className="ml-auto flex items-center gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                    >
                      {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Toggle Theme</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleFullscreen}
                    >
                      {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        )}

        {/* Editor/Preview Content */}
        {showPreview ? (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-muted/50 border-b border-border rounded-none">
              <TabsTrigger value="editor" className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                Editor
              </TabsTrigger>
              <TabsTrigger value="split" className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-4 bg-current opacity-60" />
                  <div className="w-2 h-4 bg-current opacity-60" />
                </div>
                Split
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="editor" className="mt-0 h-full">
              <div className="obsidian-editor h-full">
                <EditorContent 
                  editor={editor} 
                  style={{ minHeight: `${minHeight}px` }}
                  className="h-full"
                />
              </div>
            </TabsContent>

            <TabsContent value="split" className="mt-0 h-full">
              <div className="grid grid-cols-2 h-full">
                <div className="obsidian-editor border-r border-border">
                  <EditorContent 
                    editor={editor} 
                    style={{ minHeight: `${minHeight}px` }}
                    className="h-full"
                  />
                </div>
                <div className="h-full overflow-auto">
                  <EnhancedMarkdownPreview 
                    content={markdownContent}
                    className="p-6"
                    style={{ minHeight: `${minHeight}px` }}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="mt-0 h-full">
              <div className="h-full overflow-auto">
                <EnhancedMarkdownPreview 
                  content={markdownContent}
                  className="p-6"
                  style={{ minHeight: `${minHeight}px` }}
                />
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="obsidian-editor h-full">
            <EditorContent 
              editor={editor} 
              style={{ minHeight: `${minHeight}px` }}
              className="h-full"
            />
          </div>
        )}

        {/* Image Upload Dialog */}
        <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Insert Image</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="image-file">Upload Image</Label>
                <Input
                  id="image-file"
                  type="file"
                  accept="image/*"
                  ref={fileInputRef}
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const validation = validateImageFile(file);
                      if (!validation.valid) {
                        toast.error(validation.message);
                        return;
                      }
                      setSelectedFile(file);
                      setImageAlt(file.name.replace(/\.[^/.]+$/, ''));
                      const preview = await createImagePreview(file);
                      setImagePreview(preview);
                    }
                  }}
                />
              </div>

              {imagePreview && (
                <div className="space-y-2">
                  <img src={imagePreview} alt="Preview" className="max-w-full h-32 object-cover rounded" />
                  <Input
                    placeholder="Alt text"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                  />
                </div>
              )}

              <div className="text-center text-muted-foreground">or</div>

              <div>
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsImageDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  if (selectedFile) {
                    try {
                      setIsUploading(true);
                      const uploadedUrl = await uploadEditorImage(selectedFile, { courseId, moduleId });
                      editor?.chain().focus().setImage({ src: uploadedUrl, alt: imageAlt }).run();
                      toast.success('Image uploaded successfully!');
                    } catch (error) {
                      toast.error('Failed to upload image');
                    } finally {
                      setIsUploading(false);
                    }
                  } else if (imageUrl) {
                    editor?.chain().focus().setImage({ src: imageUrl, alt: imageAlt }).run();
                  }
                  setIsImageDialogOpen(false);
                  setSelectedFile(null);
                  setImagePreview(null);
                  setImageUrl('');
                  setImageAlt('');
                }}
                disabled={isUploading || (!selectedFile && !imageUrl)}
              >
                {isUploading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Insert Image
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Link Dialog */}
        <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Insert Link</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="link-text">Link Text</Label>
                <Input
                  id="link-text"
                  placeholder="Link text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="link-url">URL</Label>
                <Input
                  id="link-url"
                  placeholder="https://example.com"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsLinkDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (linkUrl) {
                    if (linkText) {
                      editor?.chain().focus().insertContent(`<a href="${linkUrl}">${linkText}</a>`).run();
                    } else {
                      editor?.chain().focus().setLink({ href: linkUrl }).run();
                    }
                  }
                  setIsLinkDialogOpen(false);
                  setLinkUrl('');
                  setLinkText('');
                }}
                disabled={!linkUrl}
              >
                Insert Link
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* YouTube Dialog */}
        <Dialog open={isYoutubeDialogOpen} onOpenChange={setIsYoutubeDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Insert YouTube Video</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="youtube-url">YouTube URL</Label>
                <Input
                  id="youtube-url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsYoutubeDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (youtubeUrl) {
                    editor?.chain().focus().setYoutubeVideo({ src: youtubeUrl }).run();
                  }
                  setIsYoutubeDialogOpen(false);
                  setYoutubeUrl('');
                }}
                disabled={!youtubeUrl}
              >
                Insert Video
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
