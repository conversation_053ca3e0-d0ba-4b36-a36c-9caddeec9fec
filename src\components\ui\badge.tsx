import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-1 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-sm",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary/90 backdrop-blur-sm text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary/90 backdrop-blur-sm text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive/90 backdrop-blur-sm text-destructive-foreground hover:bg-destructive/80",
        outline: "border-input/50 bg-background/50 backdrop-blur-sm text-foreground",
        ios: "border-transparent bg-primary/90 backdrop-blur-sm text-white font-medium",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
