import React, { useEffect, useState, use<PERSON>allback, useRef } from 'react';
import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Highlight from '@tiptap/extension-highlight';
import Strike from '@tiptap/extension-strike';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Placeholder from '@tiptap/extension-placeholder';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, Di<PERSON>Tit<PERSON>, DialogFooter } from '@/components/ui/dialog';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { tiptapToMarkdown } from '@/lib/enhanced-markdown-serializer';
import { markdownToHtml } from '@/lib/content-converter';
// Removed complex image upload functionality
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  Code,
  Link as LinkIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Type,
  Eye,
  Highlight as HighlightIcon,
  CheckSquare,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Save,
  History,
  Columns,
  Upload,
  X
} from 'lucide-react';

interface EnhancedMarkdownEditorProps {
  initialContent?: string;
  onChange?: (markdown: string) => void;
  onSave?: (content: string) => Promise<void>;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  courseId?: string;
  moduleId?: string;
  autoSave?: boolean;
  autoSaveInterval?: number;
  showVersionHistory?: boolean;
  enableCollaboration?: boolean;
}

export function EnhancedMarkdownEditor({
  initialContent = '',
  onChange,
  onSave,
  placeholder = 'Start writing your lesson content...',
  className = '',
  minHeight = 400,
  courseId,
  moduleId,
  autoSave = true,
  autoSaveInterval = 30000, // 30 seconds
  showVersionHistory = false,
  enableCollaboration = false,
}: EnhancedMarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<'editor' | 'preview' | 'split'>('editor');
  const [markdownContent, setMarkdownContent] = useState(initialContent);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Removed file input ref

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto shadow-md',
        },
        allowBase64: true,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline underline-offset-2 hover:text-primary/80 transition-colors',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border rounded-md overflow-hidden',
        },
      }),
      TableRow,
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-muted font-semibold',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border px-3 py-2',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'task-item flex items-start gap-2',
        },
        nested: true,
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded',
        },
      }),
      Strike.configure({
        HTMLAttributes: {
          class: 'line-through',
        },
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: markdownToHtml(initialContent),
    onUpdate: ({ editor }) => {
      try {
        const doc = editor.state.doc;
        const markdown = tiptapToMarkdown(doc);
        setMarkdownContent(markdown);
        setHasUnsavedChanges(true);
        onChange?.(markdown);

        // Auto-save functionality
        if (autoSave && onSave) {
          if (autoSaveTimeoutRef.current) {
            clearTimeout(autoSaveTimeoutRef.current);
          }
          autoSaveTimeoutRef.current = setTimeout(() => {
            handleAutoSave(markdown);
          }, autoSaveInterval);
        }
      } catch (error) {
        console.error('Error processing editor content:', error);
        toast.error('Error processing content');
      }
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none focus:outline-none p-4',
          `min-h-[${minHeight}px]`
        ),
      },
      // Removed drag and drop functionality
    },
  });

  useEffect(() => {
    if (editor && initialContent !== markdownContent) {
      const htmlContent = markdownToHtml(initialContent);
      editor.commands.setContent(htmlContent);
      setMarkdownContent(initialContent);
      setHasUnsavedChanges(false);
    }
  }, [initialContent, editor]);

  // Cleanup auto-save timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  const handleAutoSave = useCallback(async (content: string) => {
    if (!onSave || !hasUnsavedChanges) return;

    try {
      setIsSaving(true);
      await onSave(content);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
      toast.success('Auto-saved', { duration: 2000 });
    } catch (error) {
      console.error('Auto-save failed:', error);
      toast.error('Auto-save failed');
    } finally {
      setIsSaving(false);
    }
  }, [onSave, hasUnsavedChanges]);

  const handleManualSave = useCallback(async () => {
    if (!onSave) return;

    try {
      setIsSaving(true);
      await onSave(markdownContent);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
      toast.success('Saved successfully');
    } catch (error) {
      console.error('Save failed:', error);
      toast.error('Save failed');
    } finally {
      setIsSaving(false);
    }
  }, [onSave, markdownContent]);

  const addImage = useCallback(() => {
    const url = prompt('Enter image URL:');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
      toast.success('Image added!');
    }
  }, [editor]);

  if (!editor) return null;

  return (
    <div className={cn("border rounded-lg bg-card shadow-sm", className)}>
      {/* Enhanced Toolbar */}
      <div className="border-b bg-muted/30 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 flex-wrap">
            {/* View Mode Tabs */}
            <div className="flex items-center bg-background rounded-md p-1 mr-4">
              <Button
                variant={activeTab === 'editor' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('editor')}
                className="h-7 px-2"
              >
                <Type className="h-3 w-3 mr-1" />
                Edit
              </Button>
              <Button
                variant={activeTab === 'preview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('preview')}
                className="h-7 px-2"
              >
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </Button>
              <Button
                variant={activeTab === 'split' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('split')}
                className="h-7 px-2"
              >
                <Columns className="h-3 w-3 mr-1" />
                Split
              </Button>
            </div>

            {/* Formatting Tools */}
            {activeTab !== 'preview' && (
              <>
                <ToolButton
                  onClick={() => editor.chain().focus().toggleBold().run()}
                  isActive={editor.isActive('bold')}
                  title="Bold (Ctrl+B)"
                >
                  <Bold className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleItalic().run()}
                  isActive={editor.isActive('italic')}
                  title="Italic (Ctrl+I)"
                >
                  <Italic className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleUnderline().run()}
                  isActive={editor.isActive('underline')}
                  title="Underline (Ctrl+U)"
                >
                  <UnderlineIcon className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleStrike().run()}
                  isActive={editor.isActive('strike')}
                  title="Strikethrough"
                >
                  <Strikethrough className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleHighlight().run()}
                  isActive={editor.isActive('highlight')}
                  title="Highlight"
                >
                  <HighlightIcon className="h-4 w-4" />
                </ToolButton>

                <div className="w-px h-6 bg-border mx-1" />

                <ToolButton
                  onClick={() => editor.chain().focus().toggleBulletList().run()}
                  isActive={editor.isActive('bulletList')}
                  title="Bullet List"
                >
                  <List className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleOrderedList().run()}
                  isActive={editor.isActive('orderedList')}
                  title="Numbered List"
                >
                  <ListOrdered className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().toggleTaskList().run()}
                  isActive={editor.isActive('taskList')}
                  title="Task List"
                >
                  <CheckSquare className="h-4 w-4" />
                </ToolButton>

                <div className="w-px h-6 bg-border mx-1" />

                <ToolButton
                  onClick={() => setIsLinkDialogOpen(true)}
                  title="Add Link"
                >
                  <LinkIcon className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={addImage}
                  title="Add Image"
                >
                  <ImageIcon className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
                  title="Add Table"
                >
                  <TableIcon className="h-4 w-4" />
                </ToolButton>

                <div className="w-px h-6 bg-border mx-1" />

                <ToolButton
                  onClick={() => editor.chain().focus().setTextAlign('left').run()}
                  isActive={editor.isActive({ textAlign: 'left' })}
                  title="Align Left"
                >
                  <AlignLeft className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().setTextAlign('center').run()}
                  isActive={editor.isActive({ textAlign: 'center' })}
                  title="Align Center"
                >
                  <AlignCenter className="h-4 w-4" />
                </ToolButton>

                <ToolButton
                  onClick={() => editor.chain().focus().setTextAlign('right').run()}
                  isActive={editor.isActive({ textAlign: 'right' })}
                  title="Align Right"
                >
                  <AlignRight className="h-4 w-4" />
                </ToolButton>
              </>
            )}
          </div>

          {/* Save Controls */}
          <div className="flex items-center gap-2">
            {hasUnsavedChanges && (
              <span className="text-xs text-muted-foreground">Unsaved changes</span>
            )}
            {lastSaved && (
              <span className="text-xs text-muted-foreground">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}
            {isSaving && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
                Saving...
              </div>
            )}
            {onSave && (
              <Button
                size="sm"
                onClick={handleManualSave}
                disabled={isSaving || !hasUnsavedChanges}
                className="h-7"
              >
                <Save className="h-3 w-3 mr-1" />
                Save
              </Button>
            )}
            {showVersionHistory && (
              <Button
                size="sm"
                variant="outline"
                className="h-7"
              >
                <History className="h-3 w-3 mr-1" />
                History
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Editor Content Area */}
      <div className="relative">
        {activeTab === 'editor' && (
          <EditorContent
            editor={editor}
            style={{ minHeight: `${minHeight}px` }}
            className="prose-editor"
          />
        )}

        {activeTab === 'preview' && (
          <div className="p-4" style={{ minHeight: `${minHeight}px` }}>
            <MarkdownPreview
              content={markdownContent}
              className="prose max-w-none"
              allowHtml={true}
            />
          </div>
        )}

        {activeTab === 'split' && (
          <div className="flex h-full">
            <div className="flex-1 border-r">
              <EditorContent
                editor={editor}
                style={{ minHeight: `${minHeight}px` }}
                className="prose-editor"
              />
            </div>
            <div className="flex-1 p-4 overflow-auto">
              <MarkdownPreview
                content={markdownContent}
                className="prose max-w-none"
                allowHtml={true}
              />
            </div>
          </div>
        )}
      </div>

      {/* Removed file input */}

      {/* Link Dialog */}
      <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Link</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Link Text</label>
              <Input
                value={linkText}
                onChange={(e) => setLinkText(e.target.value)}
                placeholder="Enter link text"
              />
            </div>
            <div>
              <label className="text-sm font-medium">URL</label>
              <Input
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsLinkDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (linkUrl) {
                  if (linkText) {
                    editor.chain().focus().insertContent(`[${linkText}](${linkUrl})`).run();
                  } else {
                    editor.chain().focus().setLink({ href: linkUrl }).run();
                  }
                }
                setIsLinkDialogOpen(false);
                setLinkText('');
                setLinkUrl('');
              }}
            >
              Add Link
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Tool Button Component
const ToolButton = ({ onClick, isActive = false, children, title, disabled = false }: any) => (
  <Button
    type="button"
    variant="ghost"
    size="sm"
    onClick={onClick}
    disabled={disabled}
    className={cn(
      "h-7 w-7 p-0",
      isActive ? 'bg-muted text-foreground' : 'text-muted-foreground hover:text-foreground'
    )}
    title={title}
  >
    {children}
  </Button>
);

export default EnhancedMarkdownEditor;
