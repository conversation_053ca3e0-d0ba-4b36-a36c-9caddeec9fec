import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { markdownToHtml } from '@/lib/content-converter';
import { htmlToAdvancedMarkdown } from '@/lib/advanced-markdown-serializer';
import DOMPurify from 'dompurify';
import Prism from 'prismjs';
import { X, ZoomIn, Copy, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

// Import Prism languages
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-scss';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-java';
import 'prismjs/components/prism-csharp';
import 'prismjs/components/prism-php';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-yaml';

interface EnhancedMarkdownPreviewProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
  allowHtml?: boolean;
  enableImageZoom?: boolean;
  enableCodeCopy?: boolean;
  enableExport?: boolean;
}

export function EnhancedMarkdownPreview({
  content,
  className = '',
  style,
  allowHtml = true,
  enableImageZoom = true,
  enableCodeCopy = true,
  enableExport = false,
}: EnhancedMarkdownPreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const [zoomedImage, setZoomedImage] = useState<string | null>(null);
  const [isProcessed, setIsProcessed] = useState(false);
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to apply professional enhancements to HTML
  const applyProfessionalEnhancements = useCallback((html: string): string => {
    let enhancedHtml = html;

    // Enhance existing tables with professional styling
    enhancedHtml = enhancedHtml.replace(
      /<table([^>]*)>/gi,
      '<div class="professional-table-wrapper"><table class="professional-table"$1>'
    );

    enhancedHtml = enhancedHtml.replace(
      /<\/table>/gi,
      '</table></div>'
    );

    enhancedHtml = enhancedHtml.replace(
      /<thead([^>]*)>/gi,
      '<thead$1>'
    );

    // Track if we're inside a thead
    let insideThead = false;
    enhancedHtml = enhancedHtml.replace(
      /<(\/?)thead([^>]*)>/gi,
      (match, closing) => {
        insideThead = !closing;
        return match;
      }
    );

    enhancedHtml = enhancedHtml.replace(
      /<tr([^>]*)>/gi,
      (match, attrs, offset) => {
        // Check if this tr is inside a thead by looking at the context
        const beforeTr = enhancedHtml.substring(0, offset);
        const lastTheadOpen = beforeTr.lastIndexOf('<thead');
        const lastTheadClose = beforeTr.lastIndexOf('</thead>');

        if (lastTheadOpen > lastTheadClose) {
          return `<tr class="table-header-row"${attrs}>`;
        }
        return `<tr class="table-row"${attrs}>`;
      }
    );

    enhancedHtml = enhancedHtml.replace(
      /<th([^>]*)>/gi,
      '<th class="table-header-cell"$1>'
    );

    enhancedHtml = enhancedHtml.replace(
      /<td([^>]*)>/gi,
      '<td class="table-cell"$1>'
    );

    // Enhance existing details/summary elements with a simpler approach
    enhancedHtml = enhancedHtml.replace(
      /<details([^>]*?)>([\s\S]*?)<\/details>/gi,
      (match, detailsAttrs, content) => {
        // Extract summary and content
        const summaryMatch = content.match(/<summary([^>]*?)>(.*?)<\/summary>([\s\S]*)/i);
        if (summaryMatch) {
          const [, summaryAttrs, summaryText, detailsContent] = summaryMatch;
          return `<div class="professional-accordion">
            <details class="accordion-details"${detailsAttrs}>
              <summary class="accordion-summary"${summaryAttrs}>
                <span class="accordion-title">${summaryText}</span>
                <span class="accordion-icon">
                  <svg class="chevron-down" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </span>
              </summary>
              <div class="accordion-content">
                <div class="accordion-inner">${detailsContent.trim()}</div>
              </div>
            </details>
          </div>`;
        }
        return match; // Return original if no summary found
      }
    );

    // Enhance task list items
    enhancedHtml = enhancedHtml.replace(
      /<li([^>]*?)>\s*<input\s+type="checkbox"([^>]*?)>\s*(.*?)<\/li>/gi,
      (match, liAttrs, inputAttrs, content) => {
        const isChecked = inputAttrs.includes('checked');
        return `<li${liAttrs}>
          <div class="professional-task-item">
            <div class="task-checkbox-wrapper">
              <input type="checkbox" ${inputAttrs} class="professional-checkbox">
              <span class="checkmark"></span>
            </div>
            <span class="task-text ${isChecked ? 'completed' : ''}">${content}</span>
          </div>
        </li>`;
      }
    );

    // Enhance images with professional styling
    enhancedHtml = enhancedHtml.replace(
      /<img([^>]*?)>/gi,
      (match, attrs) => {
        if (attrs.includes('class=')) {
          return match.replace(/class="([^"]*)"/, 'class="$1 professional-image"');
        } else {
          return `<img${attrs} class="professional-image">`;
        }
      }
    );

    // Wrap images in figures if they have alt text
    enhancedHtml = enhancedHtml.replace(
      /<img([^>]*?)alt="([^"]*?)"([^>]*?)>/gi,
      (match, beforeAlt, altText, afterAlt) => {
        if (altText.trim()) {
          return `<figure class="professional-image-figure">
            <img${beforeAlt}alt="${altText}"${afterAlt} class="professional-image">
            <figcaption class="image-caption">${altText}</figcaption>
          </figure>`;
        }
        return match;
      }
    );

    return enhancedHtml;
  }, []);

  // Process and sanitize markdown content with professional enhancements
  const renderedContent = useMemo(() => {
    if (!content) return '';

    try {
      // Process markdown content with enhanced professional features
      let processedContent = content;

      // Keep callouts processing before markdown conversion since they use blockquote syntax
      processedContent = processedContent.replace(
        /^>\s*\[!(INFO|WARNING|SUCCESS|ERROR|TIP|NOTE|IMPORTANT|CAUTION)\]\s*\n((?:^>.*\n?)*)/gim,
        (match, type, content) => {
          const cleanContent = content.replace(/^>\s*/gm, '');
          const typeClass = type.toLowerCase();

          const iconMap = {
            info: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>',
            warning: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>',
            success: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22,4 12,14.01 9,11.01"></polyline></svg>',
            error: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',
            tip: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"></path><path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path></svg>',
            note: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline></svg>',
            important: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
            caution: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'
          };

          const icon = iconMap[typeClass] || iconMap.info;

          return `<div class="professional-callout callout-${typeClass}">
            <div class="callout-header">
              <div class="callout-icon">${icon}</div>
              <div class="callout-title">${type}</div>
            </div>
            <div class="callout-content">${cleanContent}</div>
          </div>`;
        }
      );

      // Convert to HTML first
      let html = markdownToHtml(processedContent);

      // Now apply our professional enhancements to the HTML
      html = applyProfessionalEnhancements(html);

      // Sanitize HTML to prevent XSS attacks
      return DOMPurify.sanitize(html, {
        ADD_TAGS: allowHtml ? ['iframe', 'img', 'details', 'summary', 'input', 'figure', 'figcaption', 'svg', 'path', 'polyline', 'line', 'circle'] : ['img', 'details', 'summary', 'input', 'figure', 'figcaption', 'svg', 'path', 'polyline', 'line', 'circle'],
        ADD_ATTR: allowHtml ?
          ['frameborder', 'allowfullscreen', 'allow', 'src', 'width', 'height', 'style', 'type', 'checked', 'disabled', 'class', 'data-accordion-id', 'data-indent', 'data-column', 'data-row', 'loading', 'viewBox', 'fill', 'stroke', 'stroke-width', 'points', 'cx', 'cy', 'r', 'x1', 'y1', 'x2', 'y2', 'd'] :
          ['src', 'width', 'height', 'style', 'type', 'checked', 'disabled', 'class', 'data-accordion-id', 'data-indent', 'data-column', 'data-row', 'loading', 'viewBox', 'fill', 'stroke', 'stroke-width', 'points', 'cx', 'cy', 'r', 'x1', 'y1', 'x2', 'y2', 'd'],
      });
    } catch (error) {
      console.error('Error processing markdown content:', error);
      return '';
    }
  }, [content, allowHtml]);

  // Setup enhanced image zoom and accordion functionality
  useEffect(() => {
    if (!previewRef.current || !renderedContent) return;

    const eventListeners = new Map();

    // Enhanced image zoom functionality
    if (enableImageZoom) {
      const images = previewRef.current.querySelectorAll('.professional-image');

      images.forEach((img) => {
        const handleClick = () => {
          setZoomedImage(img.src);
        };

        img.style.cursor = 'zoom-in';
        img.addEventListener('click', handleClick);
        eventListeners.set(img, handleClick);

        // Add hover effects
        const handleMouseEnter = () => {
          img.style.transform = 'scale(1.02)';
          img.style.transition = 'transform 0.3s ease';
        };

        const handleMouseLeave = () => {
          img.style.transform = 'scale(1)';
        };

        img.addEventListener('mouseenter', handleMouseEnter);
        img.addEventListener('mouseleave', handleMouseLeave);
        eventListeners.set(`${img}-enter`, handleMouseEnter);
        eventListeners.set(`${img}-leave`, handleMouseLeave);
      });
    }

    // Enhanced accordion functionality
    const accordions = previewRef.current.querySelectorAll('.professional-accordion');

    accordions.forEach((accordion) => {
      const details = accordion.querySelector('.accordion-details');
      const summary = accordion.querySelector('.accordion-summary');
      const icon = accordion.querySelector('.chevron-down');

      if (details && summary && icon) {
        const handleToggle = () => {
          const isOpen = details.hasAttribute('open');

          // Animate the chevron
          if (isOpen) {
            icon.style.transform = 'rotate(180deg)';
            accordion.classList.add('expanded');
          } else {
            icon.style.transform = 'rotate(0deg)';
            accordion.classList.remove('expanded');
          }
        };

        details.addEventListener('toggle', handleToggle);
        eventListeners.set(`${accordion}-toggle`, handleToggle);

        // Add smooth transition
        icon.style.transition = 'transform 0.3s ease';
      }
    });

    // Enhanced table interactions
    const tables = previewRef.current.querySelectorAll('.professional-table');

    tables.forEach((table) => {
      const rows = table.querySelectorAll('.table-row');

      rows.forEach((row) => {
        const handleMouseEnter = () => {
          row.classList.add('hovered');
        };

        const handleMouseLeave = () => {
          row.classList.remove('hovered');
        };

        row.addEventListener('mouseenter', handleMouseEnter);
        row.addEventListener('mouseleave', handleMouseLeave);
        eventListeners.set(`${row}-enter`, handleMouseEnter);
        eventListeners.set(`${row}-leave`, handleMouseLeave);
      });
    });

    return () => {
      eventListeners.forEach((listener, element) => {
        if (typeof element === 'string') return; // Skip string keys
        element.removeEventListener('click', listener);
        element.removeEventListener('mouseenter', listener);
        element.removeEventListener('mouseleave', listener);
        element.removeEventListener('toggle', listener);
      });
    };
  }, [renderedContent, enableImageZoom]);

  // Setup code copy functionality
  useEffect(() => {
    if (!previewRef.current || !renderedContent || !enableCodeCopy) return;
    
    const codeBlocks = previewRef.current.querySelectorAll('pre code');
    
    codeBlocks.forEach((codeBlock) => {
      const pre = codeBlock.parentElement;
      if (!pre) return;
      
      // Create copy button
      const copyButton = document.createElement('button');
      copyButton.className = 'code-copy-button';
      copyButton.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>';
      copyButton.title = 'Copy code';
      
      copyButton.addEventListener('click', async () => {
        try {
          await navigator.clipboard.writeText(codeBlock.textContent || '');
          copyButton.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"></polyline></svg>';
          setTimeout(() => {
            copyButton.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>';
          }, 2000);
          toast.success('Code copied to clipboard!');
        } catch (error) {
          toast.error('Failed to copy code');
        }
      });
      
      pre.style.position = 'relative';
      pre.appendChild(copyButton);
    });
  }, [renderedContent, enableCodeCopy]);

  // Setup syntax highlighting
  useEffect(() => {
    if (!previewRef.current || !renderedContent || isProcessed) return;
    
    // Clean up previous timeout
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }
    
    // Debounce processing to avoid excessive re-highlighting
    processingTimeoutRef.current = setTimeout(() => {
      try {
        const codeBlocks = previewRef.current?.querySelectorAll('pre code');
        codeBlocks?.forEach((block) => {
          Prism.highlightElement(block as HTMLElement);
        });
        setIsProcessed(true);
      } catch (error) {
        console.error('Error highlighting code:', error);
      }
    }, 100);
    
    return () => {
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
        processingTimeoutRef.current = null;
      }
    };
  }, [renderedContent, isProcessed]);

  // Reset processed state when content changes
  useEffect(() => {
    setIsProcessed(false);
  }, [content]);

  const handleCloseZoom = useCallback(() => {
    setZoomedImage(null);
  }, []);

  const handleExportMarkdown = useCallback(() => {
    if (!content) return;
    
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'content.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Markdown exported!');
  }, [content]);

  const handleCopyMarkdown = useCallback(async () => {
    if (!content) return;
    
    try {
      await navigator.clipboard.writeText(content);
      toast.success('Markdown copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy markdown');
    }
  }, [content]);

  return (
    <>
      <div className={cn('enhanced-markdown-preview', className)} style={style}>
        {/* Export controls */}
        {enableExport && (
          <div className="flex items-center gap-2 mb-4 p-2 bg-muted/50 rounded-lg">
            <Button variant="ghost" size="sm" onClick={handleCopyMarkdown}>
              <Copy className="h-4 w-4 mr-2" />
              Copy Markdown
            </Button>
            <Button variant="ghost" size="sm" onClick={handleExportMarkdown}>
              <Download className="h-4 w-4 mr-2" />
              Export Markdown
            </Button>
          </div>
        )}
        
        <div
          ref={previewRef}
          className="markdown-content professional-lesson-preview"
          dangerouslySetInnerHTML={{ __html: renderedContent }}
        />
      </div>
      
      {/* Image zoom modal */}
      {zoomedImage && (
        <div 
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={handleCloseZoom}
        >
          <button 
            className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-colors"
            onClick={handleCloseZoom}
          >
            <X className="h-6 w-6 text-white" />
          </button>
          <img 
            src={zoomedImage} 
            alt="Zoomed" 
            className="max-h-[85vh] max-w-[85vw] object-contain rounded-lg"
          />
        </div>
      )}
    </>
  );
}
