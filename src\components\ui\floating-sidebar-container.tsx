import React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface FloatingSidebarContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  centerContent?: boolean;
  fullWidth?: boolean;
}

/**
 * A container component optimized for the floating sidebar layout
 * Provides consistent spacing and responsive behavior across all LMS pages
 */
export function FloatingSidebarContainer({
  children,
  className,
  maxWidth = '4xl',
  padding = 'md',
  centerContent = true,
  fullWidth = false,
  ...props
}: FloatingSidebarContainerProps) {
  const isMobile = useIsMobile(768);

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: isMobile ? 'px-3 py-3' : 'px-3 py-3', // Reduced desktop padding for consistency
    md: isMobile ? 'px-4 py-4' : 'px-4 py-4', // Reduced desktop padding for consistency
    lg: isMobile ? 'px-5 py-5' : 'px-5 py-5', // Reduced desktop padding for consistency
    xl: isMobile ? 'px-6 py-6' : 'px-6 py-6'  // Reduced desktop padding for consistency
  };

  return (
    <div
      className={cn(
        // Base container styles
        "w-full",
        
        // Responsive width and centering
        !fullWidth && [
          maxWidthClasses[maxWidth],
          centerContent && "mx-auto"
        ],
        
        // Responsive padding
        paddingClasses[padding],
        
        // Additional spacing considerations for floating sidebar
        !isMobile && "relative",
        
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * A specialized container for page content with floating sidebar
 * Provides optimal spacing and layout for different page types
 */
export function PageContainer({
  children,
  className,
  pageType = 'default',
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  pageType?: 'default' | 'dashboard' | 'lesson' | 'module' | 'certificate' | 'full-width';
}) {
  const isMobile = useIsMobile(768);

  // Page-specific configurations - Updated for consistent reduced spacing
  const pageConfigs = {
    default: {
      maxWidth: '4xl' as const,
      padding: 'md' as const, // Consistent medium padding
      centerContent: true,
      fullWidth: false
    },
    dashboard: {
      maxWidth: '6xl' as const,
      padding: 'md' as const, // Reduced from 'lg' for consistent spacing
      centerContent: true,
      fullWidth: false
    },
    lesson: {
      maxWidth: 'full' as const,
      padding: 'none' as const,
      centerContent: false,
      fullWidth: true
    },
    module: {
      maxWidth: '4xl' as const,
      padding: 'md' as const, // Increased from 'sm' for consistency
      centerContent: true,
      fullWidth: false
    },
    certificate: {
      maxWidth: '5xl' as const,
      padding: 'md' as const, // Reduced from 'lg' for consistent spacing
      centerContent: true,
      fullWidth: false
    },
    'full-width': {
      maxWidth: 'full' as const,
      padding: 'none' as const,
      centerContent: false,
      fullWidth: true
    }
  };

  const config = pageConfigs[pageType];

  return (
    <FloatingSidebarContainer
      className={cn(
        // Page-specific adjustments - Updated for consistent reduced spacing
        pageType === 'lesson' && [
          // Lesson pages need special handling for full-width content
          "!px-0 !py-0",
          !isMobile && "!pl-0"
        ],
        pageType === 'dashboard' && [
          // Dashboard gets consistent spacing (reduced from previous)
          "space-y-4 md:space-y-5"
        ],
        pageType === 'module' && [
          // Module pages get consistent spacing
          "space-y-3 md:space-y-3"
        ],
        pageType === 'certificate' && [
          // Certificate pages get consistent spacing (reduced from previous)
          "space-y-4 md:space-y-5"
        ],
        className
      )}
      maxWidth={config.maxWidth}
      padding={config.padding}
      centerContent={config.centerContent}
      fullWidth={config.fullWidth}
      {...props}
    >
      {children}
    </FloatingSidebarContainer>
  );
}

/**
 * A content wrapper that provides consistent inner spacing
 * Use this inside PageContainer for additional content organization
 */
export function ContentSection({
  children,
  className,
  spacing = 'md',
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const spacingClasses = {
    sm: 'space-y-2 md:space-y-2', // Further reduced for consistency
    md: 'space-y-3 md:space-y-3', // Further reduced for consistency
    lg: 'space-y-4 md:space-y-4', // Further reduced for consistency
    xl: 'space-y-5 md:space-y-6'  // Further reduced for consistency
  };

  return (
    <div
      className={cn(
        spacingClasses[spacing],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * A responsive grid container optimized for floating sidebar layout
 */
export function ResponsiveContentGrid({
  children,
  className,
  cols = { default: 1, sm: 1, md: 2, lg: 3 },
  gap = 'md',
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const gapClasses = {
    sm: 'gap-3 md:gap-3', // Consistent gap for better spacing
    md: 'gap-4 md:gap-4', // Consistent gap for better spacing
    lg: 'gap-5 md:gap-5', // Consistent gap for better spacing
    xl: 'gap-6 md:gap-6'  // Consistent gap for better spacing
  };

  return (
    <div
      className={cn(
        "grid w-full",
        `grid-cols-${cols.default}`,
        cols.sm && `sm:grid-cols-${cols.sm}`,
        cols.md && `md:grid-cols-${cols.md}`,
        cols.lg && `lg:grid-cols-${cols.lg}`,
        cols.xl && `xl:grid-cols-${cols.xl}`,
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
