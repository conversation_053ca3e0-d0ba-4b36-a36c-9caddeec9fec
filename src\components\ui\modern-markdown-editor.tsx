import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { MarkdownPreview } from './markdown-preview';
import { Button } from './button';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Code,
  Quote,
  Link,
  Image as ImageIcon,
  Heading1,
  Heading2,
  Heading3,
  CheckSquare,
  AlertTriangle,
  Info,
  HelpCircle,
  Lightbulb,
  FileCode,
  Table,
  Undo,
  Redo,
  Loader2,
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';
import { Input } from './input';

interface ModernMarkdownEditorProps {
  initialContent?: string;
  onChange?: (content: string) => void;
  className?: string;
  minHeight?: number;
  placeholder?: string;
  autoFocus?: boolean;
  onImageUpload?: (file: File) => Promise<string>;
}

export function ModernMarkdownEditor({
  initialContent = '',
  onChange,
  className,
  minHeight = 300,
  placeholder = 'Write your content here using Markdown...',
  autoFocus = false,
  onImageUpload
}: ModernMarkdownEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [activeTab, setActiveTab] = useState<'write' | 'preview'>('write');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [history, setHistory] = useState<string[]>([initialContent]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageWidth, setImageWidth] = useState('');
  const [imageHeight, setImageHeight] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setContent(initialContent);
    setHistory([initialContent]);
    setHistoryIndex(0);
  }, [initialContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);

    // Add to history if content is different from the last entry
    if (newContent !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newContent);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }

    if (onChange) onChange(newContent);
  };

  const insertText = (before: string, after: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);

    const newContent =
      content.substring(0, start) +
      before +
      selectedText +
      after +
      content.substring(end);

    setContent(newContent);
    if (onChange) onChange(newContent);

    // Add to history
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newContent);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);

    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + before.length,
        end + before.length
      );
    }, 0);
  };

  const insertHeading = (level: number) => {
    const prefix = '#'.repeat(level) + ' ';

    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    // Get the start of the line
    let lineStart = start;
    while (lineStart > 0 && content[lineStart - 1] !== '\n') {
      lineStart--;
    }

    // Check if there's already a heading
    const currentLine = content.substring(lineStart, end);
    const headingMatch = currentLine.match(/^(#{1,6})\s/);

    if (headingMatch) {
      // Replace existing heading
      const newContent =
        content.substring(0, lineStart) +
        prefix +
        currentLine.substring(headingMatch[0].length) +
        content.substring(end);

      setContent(newContent);
      if (onChange) onChange(newContent);

      // Add to history
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newContent);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    } else {
      // Insert new heading at the beginning of the line
      const newContent =
        content.substring(0, lineStart) +
        prefix +
        content.substring(lineStart);

      setContent(newContent);
      if (onChange) onChange(newContent);

      // Add to history
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newContent);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }

    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        end + prefix.length - (headingMatch ? headingMatch[0].length : 0),
        end + prefix.length - (headingMatch ? headingMatch[0].length : 0)
      );
    }, 0);
  };

  const insertCallout = (type: 'info' | 'warning' | 'tip' | 'note') => {
    let emoji = '';
    let title = '';

    switch (type) {
      case 'info':
        emoji = 'ℹ️';
        title = 'Info';
        break;
      case 'warning':
        emoji = '⚠️';
        title = 'Warning';
        break;
      case 'tip':
        emoji = '💡';
        title = 'Tip';
        break;
      case 'note':
        emoji = '📝';
        title = 'Note';
        break;
    }

    const callout = `> ${emoji} **${title}**\n> \n> `;
    insertText(callout);
  };

  const insertCodeBlock = () => {
    insertText("```\n", "\n```");
  };

  const insertTable = () => {
    const table = "| Header 1 | Header 2 | Header 3 |\n| --- | --- | --- |\n| Row 1, Col 1 | Row 1, Col 2 | Row 1, Col 3 |\n| Row 2, Col 1 | Row 2, Col 2 | Row 2, Col 3 |";
    insertText(table);
  };

  const insertTaskList = () => {
    const taskList = "- [ ] Task 1\n- [ ] Task 2\n- [ ] Task 3";
    insertText(taskList);
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setContent(history[historyIndex - 1]);
      if (onChange) onChange(history[historyIndex - 1]);
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setContent(history[historyIndex + 1]);
      if (onChange) onChange(history[historyIndex + 1]);
    }
  };

  const handleImageInsert = async () => {
    if (imageFile && onImageUpload) {
      try {
        setIsUploading(true);
        const uploadedUrl = await onImageUpload(imageFile);
        let markdownImage = `![${imageAlt}](${uploadedUrl}`;
        if (imageWidth && imageHeight) {
          markdownImage += ` ${imageWidth}x${imageHeight}`;
        }
        markdownImage += ')';
        insertText(markdownImage);
      } catch (error) {
        console.error('Error uploading image:', error);
        toast({
          title: "Error",
          description: "Failed to upload image",
          variant: "destructive"
        });
      } finally {
        setIsUploading(false);
      }
    } else if (imageUrl) {
      let markdownImage = `![${imageAlt}](${imageUrl}`;
      if (imageWidth && imageHeight) {
        markdownImage += ` ${imageWidth}x${imageHeight}`;
      }
      markdownImage += ')';
      insertText(markdownImage);
    }
    handleCloseImageDialog();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setImageUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
      
      // Auto-fill dimensions
      const img = new Image();
      img.onload = () => {
        setImageWidth(img.width.toString());
        setImageHeight(img.height.toString());
      };
      img.src = URL.createObjectURL(file);
    }
  };

  const handleCloseImageDialog = () => {
    setIsImageDialogOpen(false);
    setImageUrl('');
    setImageFile(null);
    setImageWidth('');
    setImageHeight('');
    setImageAlt('');
  };

  const ToolbarButton = ({ icon, label, onClick }: { icon: React.ReactNode, label: string, onClick: () => void }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-600 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900/20"
            onClick={onClick}
          >
            {icon}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <>
      <div className={cn("border border-gray-200 dark:border-gray-800 rounded-md overflow-hidden shadow-sm", className)}>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'write' | 'preview')}>
          <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
            <TabsList className="bg-transparent border-b-0 rounded-none">
              <TabsTrigger
                value="write"
                className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:text-red-600 dark:data-[state=active]:text-red-400 data-[state=active]:border-b-2 data-[state=active]:border-red-500 rounded-none"
              >
                Write
              </TabsTrigger>
              <TabsTrigger
                value="preview"
                className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:text-red-600 dark:data-[state=active]:text-red-400 data-[state=active]:border-b-2 data-[state=active]:border-red-500 rounded-none"
              >
                Preview
              </TabsTrigger>
            </TabsList>

            {activeTab === 'write' && (
              <div className="flex items-center px-2 overflow-x-auto scrollbar-hide">
                <div className="flex space-x-1 py-1">
                  <ToolbarButton icon={<Undo className="h-4 w-4" />} label="Undo" onClick={handleUndo} />
                  <ToolbarButton icon={<Redo className="h-4 w-4" />} label="Redo" onClick={handleRedo} />
                  <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 mx-1" />
                  <ToolbarButton icon={<Bold className="h-4 w-4" />} label="Bold" onClick={() => insertText('**', '**')} />
                  <ToolbarButton icon={<Italic className="h-4 w-4" />} label="Italic" onClick={() => insertText('*', '*')} />
                  <ToolbarButton icon={<Code className="h-4 w-4" />} label="Inline Code" onClick={() => insertText('`', '`')} />
                  <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 mx-1" />
                  <ToolbarButton icon={<Heading1 className="h-4 w-4" />} label="Heading 1" onClick={() => insertHeading(1)} />
                  <ToolbarButton icon={<Heading2 className="h-4 w-4" />} label="Heading 2" onClick={() => insertHeading(2)} />
                  <ToolbarButton icon={<Heading3 className="h-4 w-4" />} label="Heading 3" onClick={() => insertHeading(3)} />
                  <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 mx-1" />
                  <ToolbarButton icon={<List className="h-4 w-4" />} label="Bullet List" onClick={() => insertText('- ')} />
                  <ToolbarButton icon={<ListOrdered className="h-4 w-4" />} label="Numbered List" onClick={() => insertText('1. ')} />
                  <ToolbarButton icon={<CheckSquare className="h-4 w-4" />} label="Task List" onClick={insertTaskList} />
                  <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 mx-1" />
                  <ToolbarButton icon={<Quote className="h-4 w-4" />} label="Blockquote" onClick={() => insertText('> ')} />
                  <ToolbarButton icon={<Link className="h-4 w-4" />} label="Link" onClick={() => insertText('[', '](https://)')} />
                  <ToolbarButton icon={<ImageIcon className="h-4 w-4" />} label="Image" onClick={() => setIsImageDialogOpen(true)} />
                  <ToolbarButton icon={<FileCode className="h-4 w-4" />} label="Code Block" onClick={insertCodeBlock} />
                  <ToolbarButton icon={<Table className="h-4 w-4" />} label="Table" onClick={insertTable} />
                  <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 mx-1" />
                  <ToolbarButton icon={<Info className="h-4 w-4" />} label="Info Callout" onClick={() => insertCallout('info')} />
                  <ToolbarButton icon={<AlertTriangle className="h-4 w-4" />} label="Warning Callout" onClick={() => insertCallout('warning')} />
                  <ToolbarButton icon={<Lightbulb className="h-4 w-4" />} label="Tip Callout" onClick={() => insertCallout('tip')} />
                  <ToolbarButton icon={<HelpCircle className="h-4 w-4" />} label="Note Callout" onClick={() => insertCallout('note')} />
                </div>
              </div>
            )}
          </div>

          <TabsContent value="write" className="p-0 m-0">
            <Textarea
              ref={textareaRef}
              id="modern-markdown-editor-textarea"
              value={content}
              onChange={handleContentChange}
              className="min-h-[300px] border-0 rounded-none rounded-b-md focus-visible:ring-0 focus-visible:ring-red-200 dark:focus-visible:ring-red-900/30 resize-y font-mono text-base px-6 py-5"
              placeholder={placeholder}
              style={{
                minHeight: `${minHeight}px`,
                lineHeight: '1.8',
                letterSpacing: '0.01em'
              }}
              autoFocus={autoFocus}
            />
          </TabsContent>

          <TabsContent value="preview" className="p-6 m-0 min-h-[300px] bg-white dark:bg-gray-900" style={{ minHeight: `${minHeight}px` }}>
            <div className="enhanced-markdown-editor">
              <MarkdownPreview
                content={content}
                className="prose-red max-w-none"
                allowHtml={true}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Dialog open={isImageDialogOpen} onOpenChange={handleCloseImageDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="upload">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
            </TabsList>
            <TabsContent value="upload" className="space-y-4">
              <div className="grid w-full items-center gap-4">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                />
                {imageUrl && (
                  <div className="relative aspect-video w-full overflow-hidden rounded-lg border">
                    <img
                      src={imageUrl}
                      alt="Preview"
                      className="object-contain w-full h-full"
                    />
                  </div>
                )}
              </div>
            </TabsContent>
            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full items-center gap-4">
                <Input
                  placeholder="Enter image URL"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                />
                {imageUrl && (
                  <div className="relative aspect-video w-full overflow-hidden rounded-lg border">
                    <img
                      src={imageUrl}
                      alt="Preview"
                      className="object-contain w-full h-full"
                      onError={() => setImageUrl('')}
                    />
                  </div>
                )}
              </div>
            </TabsContent>
            <div className="space-y-4 mt-4">
              <Input
                placeholder="Alt text"
                value={imageAlt}
                onChange={(e) => setImageAlt(e.target.value)}
              />
              <div className="grid grid-cols-2 gap-4">
                <Input
                  placeholder="Width (px)"
                  type="number"
                  value={imageWidth}
                  onChange={(e) => setImageWidth(e.target.value)}
                />
                <Input
                  placeholder="Height (px)"
                  type="number"
                  value={imageHeight}
                  onChange={(e) => setImageHeight(e.target.value)}
                />
              </div>
              <Button
                className="w-full"
                onClick={handleImageInsert}
                disabled={(!imageUrl && !imageFile) || isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  'Insert Image'
                )}
              </Button>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}
