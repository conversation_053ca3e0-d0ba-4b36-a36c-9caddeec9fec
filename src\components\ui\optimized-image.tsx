import React, { useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  fallbackSrc?: string;
  priority?: boolean;
}

/**
 * OptimizedImage component for efficient image loading
 * 
 * Features:
 * - Lazy loading for images not in viewport
 * - Loading skeleton while image loads
 * - Error handling with fallback image
 * - Accessibility support
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  fallbackSrc = '/images/placeholder.jpg',
  priority = false,
}: OptimizedImageProps) {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  
  // Handle image load event
  const handleLoad = () => {
    setLoaded(true);
  };
  
  // Handle image error event
  const handleError = () => {
    setError(true);
    setLoaded(true); // We still consider it "loaded" even if it's the error state
  };
  
  // Create full URL if it's a relative path
  const getFullImageUrl = (imagePath: string): string => {
    // If it's already an absolute URL, return it as is
    if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
      return imagePath;
    }
    
    // For relative paths, make sure they start with a slash
    const path = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
    return path;
  };
  
  return (
    <div className="relative" style={{ width, height }}>
      {!loaded && (
        <Skeleton className="absolute inset-0 rounded-md" style={{ width, height }} />
      )}
      <img
        src={error ? getFullImageUrl(fallbackSrc) : getFullImageUrl(src)}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? "eager" : "lazy"}
        onLoad={handleLoad}
        onError={handleError}
        className={`transition-opacity duration-300 ${loaded ? 'opacity-100' : 'opacity-0'} ${className}`}
        style={{ objectFit: 'cover' }}
      />
    </div>
  );
}

export default OptimizedImage; 