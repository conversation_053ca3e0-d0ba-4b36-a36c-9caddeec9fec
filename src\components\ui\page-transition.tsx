import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * PageTransition component for smooth transitions between pages
 */
export function PageTransition({ children, className }: PageTransitionProps) {
  const isMobile = useIsMobile();

  // Use gentler animations on mobile for better performance
  const pageVariants = {
    initial: {
      opacity: 0,
      y: isMobile ? 10 : 20,
    },
    animate: {
      opacity: 1,
      y: 0,
    },
    exit: {
      opacity: 0,
      y: isMobile ? -10 : -20,
    },
  };

  const pageTransition = {
    type: "tween",
    ease: "easeInOut",
    duration: isMobile ? 0.2 : 0.3,
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants}
        transition={pageTransition}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}

/**
 * Section transition component for animating sections within a page
 */
export function SectionTransition({
  children,
  delay = 0,
  duration = 0.5,
}: {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration, delay }}
    >
      {children}
    </motion.div>
  );
}
