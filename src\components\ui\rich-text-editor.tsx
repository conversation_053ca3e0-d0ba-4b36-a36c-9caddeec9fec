import React from 'react';
import { Textarea } from './textarea';
import { Button } from './button';
import { Bold, Italic, List, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2 } from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Start typing...',
  height = 400,
  disabled = false,
}) => {
  const insertTag = (tag: string) => {
    const textarea = document.getElementById('rich-text-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const openTag = `<${tag}>`;
    const closeTag = `</${tag}>`;

    const newValue = value.substring(0, start) + openTag + selectedText + closeTag + value.substring(end);
    onChange(newValue);

    // Set cursor position after insertion
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + openTag.length + selectedText.length + closeTag.length, start + openTag.length + selectedText.length + closeTag.length);
    }, 0);
  };

  const insertHeading = (level: number) => {
    const textarea = document.getElementById('rich-text-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const tag = `h${level}`;
    const openTag = `<${tag}>`;
    const closeTag = `</${tag}>`;

    const newValue = value.substring(0, start) + openTag + selectedText + closeTag + value.substring(end);
    onChange(newValue);
  };

  const insertList = (type: 'ul' | 'ol') => {
    const textarea = document.getElementById('rich-text-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const lines = selectedText.split('\n').filter(line => line.trim() !== '');

    let listItems = '';
    if (lines.length > 0) {
      lines.forEach(line => {
        listItems += `<li>${line}</li>\n`;
      });
    } else {
      listItems = '<li></li>\n';
    }

    const openTag = `<${type}>\n`;
    const closeTag = `</${type}>`;

    const newValue = value.substring(0, start) + openTag + listItems + closeTag + value.substring(end);
    onChange(newValue);
  };

  const insertAlignment = (align: 'left' | 'center' | 'right') => {
    const textarea = document.getElementById('rich-text-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const openTag = `<div style="text-align: ${align}">`;
    const closeTag = `</div>`;

    const newValue = value.substring(0, start) + openTag + selectedText + closeTag + value.substring(end);
    onChange(newValue);
  };

  return (
    <div className="border rounded-md">
      <div className="flex flex-wrap gap-1 p-2 border-b bg-gray-50">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertTag('b')}
          className="h-8 w-8 p-0"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertTag('i')}
          className="h-8 w-8 p-0"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertHeading(1)}
          className="h-8 w-8 p-0"
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertHeading(2)}
          className="h-8 w-8 p-0"
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertList('ul')}
          className="h-8 w-8 p-0"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertAlignment('left')}
          className="h-8 w-8 p-0"
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertAlignment('center')}
          className="h-8 w-8 p-0"
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertAlignment('right')}
          className="h-8 w-8 p-0"
        >
          <AlignRight className="h-4 w-4" />
        </Button>
      </div>
      <Textarea
        id="rich-text-editor"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        style={{ height: `${height}px`, minHeight: '200px' }}
        className="border-0 rounded-none focus-visible:ring-0 focus-visible:ring-offset-0"
      />
      <div className="p-2 border-t bg-gray-50 text-xs text-gray-500">
        Use HTML tags for formatting. Preview will be shown in the lesson view.
      </div>
    </div>
  );
};

export default RichTextEditor;
