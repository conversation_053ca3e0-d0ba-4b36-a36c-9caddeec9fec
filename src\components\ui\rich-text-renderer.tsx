import React, { useEffect, useMemo } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import Youtube from '@tiptap/extension-youtube';
import DOMPurify from 'dompurify';
import { normalizeToHtml } from '@/lib/content-converter';

interface RichTextRendererProps {
  content: string;
  className?: string;
}

const RichTextRenderer: React.FC<RichTextRendererProps> = ({ content, className = '' }) => {
  // Memoize the HTML content to prevent unnecessary re-renders
  const htmlContent = useMemo(() => {
    if (!content) return '';
    
    try {
      // Convert any content format to HTML
      const normalizedContent = normalizeToHtml(content);
      
      // Sanitize the content while preserving tables and styling
      return DOMPurify.sanitize(normalizedContent, {
        ADD_TAGS: ['iframe', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'div', 'span'],
        ADD_ATTR: ['frameborder', 'allowfullscreen', 'allow', 'style', 'class', 'id'],
      });
    } catch (error) {
      console.error('Error processing rich text content:', error);
      return '';
    }
  }, [content]);

  // Memoize the editor extensions to prevent recreating them on every render
  const extensions = useMemo(() => [
    StarterKit,
    Image,
    Link.configure({
      openOnClick: true,
      HTMLAttributes: {
        rel: 'noopener noreferrer',
        target: '_blank',
      },
    }),
    Underline,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Highlight,
    Youtube.configure({
      controls: true,
      nocookie: true,
    }),
  ], []);

  // Use memoized editor to prevent recreation on each render
  const editor = useMemo(() => {
    return useEditor({
      extensions,
      content: htmlContent,
      editable: false,
    });
  }, [extensions, htmlContent]);

  // Update editor content only when htmlContent changes
  useEffect(() => {
    if (editor && htmlContent) {
      // Only update content if it's different to prevent unnecessary rerenders
      if (editor.getHTML() !== htmlContent) {
        editor.commands.setContent(htmlContent);
      }
    }
  }, [editor, htmlContent]);

  // If we don't have content or the editor is not initialized yet, show simple content
  if (!editor || !htmlContent) {
    // Render a simple div with the content to avoid the blank flash
    return (
      <div className={`rich-text-content ${className}`}>
        <div dangerouslySetInnerHTML={{ __html: htmlContent || '' }} />
      </div>
    );
  }

  return (
    <div className={`rich-text-content ${className}`}>
      <EditorContent editor={editor} />
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(RichTextRenderer);
