import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export function LessonContentSkeleton() {
  return (
    <div className="bg-card rounded-xl shadow-sm border border-border/40 overflow-hidden">
      {/* Header */}
      <div className="p-6 border-b border-border/40">
        <Skeleton className="h-8 w-3/4 mb-3" />
        <div className="flex items-center">
          <Skeleton className="h-5 w-24 mr-4" />
          <Skeleton className="h-5 w-20" />
        </div>
      </div>
      
      {/* Content */}
      <div className="p-8 space-y-6">
        <Skeleton className="h-6 w-full" />
        
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
        </div>
        
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/6" />
        </div>
        
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
        </div>
        
        <Skeleton className="h-40 w-full rounded-lg" />
        
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/6" />
        </div>
      </div>
      
      {/* Footer */}
      <div className="p-6 border-t border-border/40 flex justify-between">
        <Skeleton className="h-10 w-28" />
        <Skeleton className="h-10 w-28" />
      </div>
    </div>
  );
}
