import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface StatCardSkeletonProps {
  className?: string;
}

export function StatCardSkeleton({ className }: StatCardSkeletonProps) {
  return (
    <div className={cn("rounded-2xl p-6 shadow-md border border-border/10", className)}>
      <div className="flex items-center mb-3">
        <Skeleton className="w-10 h-10 rounded-full mr-3" />
        <Skeleton className="h-4 w-24" />
      </div>
      <Skeleton className="h-8 w-16" />
    </div>
  );
}

export function StatCardSkeletonList() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-5">
      <StatCardSkeleton className="bg-primary/30" />
      <StatCardSkeleton className="bg-purple-500/30" />
      <StatCardSkeleton className="bg-red-500/30" />
      <StatCardSkeleton className="bg-orange-500/30" />
    </div>
  );
}
