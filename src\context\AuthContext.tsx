import { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { AuthResponse, Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { markAllCoursesAsCompleted, markAllModulesAndLessonsAsCompleted } from '@/services/course/autoCompletionService';
import { toast } from 'sonner';
import { executeWithRetry } from '@/lib/connection-manager';
import { verifyTeacherRole } from '@/services/auth/roleService';
import { v4 as uuidv4 } from 'uuid';

export interface AuthContextType {
  user: User | null;
  signIn?: (email: string, password: string) => Promise<AuthResponse>;
  signUp?: (email: string, password: string, userData?: any) => Promise<AuthResponse>;
  signOut?: () => Promise<void>;
  loading: boolean;
  setUser?: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
});

export const useAuth = () => useContext(AuthContext);

// Create a server-verification token for auto-completion security
const generateVerificationToken = (userId: string): string => {
  // Generate a unique token for this session
  const token = uuidv4();
  // Store with short expiry (30 minutes)
  try {
    const tokenData = {
      token,
      created: Date.now()
    };
    sessionStorage.setItem(`auth_token_${userId}`, JSON.stringify(tokenData));
  } catch (e) {
    console.error('Failed to store verification token:', e);
  }
  return token;
};

// Verify a token is valid and hasn't expired
const verifyToken = (userId: string, token: string): boolean => {
  try {
    const tokenDataStr = sessionStorage.getItem(`auth_token_${userId}`);
    if (!tokenDataStr) return false;

    const tokenData = JSON.parse(tokenDataStr);
    const isValid = tokenData.token === token;
    const isNotExpired = (Date.now() - tokenData.created) < (30 * 60 * 1000); // 30 minutes

    return isValid && isNotExpired;
  } catch (e) {
    console.error('Error verifying token:', e);
    return false;
  }
};

// Function to handle service unavailability properly
const handleServiceUnavailable = (error: any): boolean => {
  if (!error) return false;

  // Check for common error patterns
  return (
    error.status === 503 ||
    error.code === '503' ||
    (error.message && (
      error.message.includes('service unavailable') ||
      error.message.includes('failed after') ||
      error.message.includes('network') ||
      error.message.includes('Failed to fetch')
    ))
  );
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  /**
   * Check if auto-completion is enabled for a user
   * This function uses secure role verification and user preferences
   */
  const isAutoCompletionEnabled = useCallback(async (userId: string): Promise<boolean> => {
    try {
      // First verify user is a teacher using our secure server-side function
      const isTeacher = await verifyTeacherRole(userId);

      if (!isTeacher) {
        console.log('Auto-completion disabled for non-teacher user:', userId);
        return false;
      }

      // For teachers, check their preferences with proper error handling
      const { data, error } = await supabase
        .from('user_preferences')
        .select('auto_complete_courses')
        .eq('user_id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error checking teacher preferences:', error);
        return false;
      }

      // For teachers, respect their preference if it exists, default to false for safety
      const isEnabled = data?.auto_complete_courses === true;

      // Log the result for debugging
      console.log(`Auto-completion for user ${userId} is ${isEnabled ? 'enabled' : 'disabled'}`);

      return isEnabled;
    } catch (error) {
      console.error('Error checking auto-completion setting:', error);
      return false;
    }
  }, []);

  /**
   * Generate a verification token for auto-completion
   * This token is used to verify that the auto-completion request is legitimate
   */
  const generateVerificationToken = useCallback((userId: string): string => {
    // Simple token generation - in a real app, this would be more secure
    return `auto-complete-${userId}-${new Date().getDate()}`;
  }, []);

  /**
   * Handle auto-completion for a user
   * This function is called when a user signs in and has auto-completion enabled
   */
  const handleAutoCompletion = useCallback(async (userId: string) => {
    if (!userId) return;

    try {
      // Step 1: Check if auto-completion is enabled for this user
      const autoCompletionEnabled = await isAutoCompletionEnabled(userId);

      if (!autoCompletionEnabled) {
        console.log('Auto-completion is disabled for user:', userId);
        return;
      }

      // Step 2: Generate a verification token
      const verificationToken = generateVerificationToken(userId);

      // Step 3: Mark all courses as completed with verification token
      // Pass false for isManualTrigger since this is automatic
      const courseSuccess = await markAllCoursesAsCompleted(userId, verificationToken, false);

      if (courseSuccess) {
        console.log('Successfully auto-completed all courses');

        // Step 4: Mark all modules and lessons as completed
        const moduleSuccess = await markAllModulesAndLessonsAsCompleted(userId, verificationToken, false);
        if (moduleSuccess) {
          console.log('Successfully completed all modules and lessons');
        } else {
          console.error('Failed to complete modules and lessons');
        }
      } else {
        console.error('Failed to auto-complete courses');
      }
    } catch (error) {
      console.error('Error in auto-completion:', error);
    }
  }, [isAutoCompletionEnabled, generateVerificationToken]);

  // Function to attempt to recover a cached session if available
  const recoverCachedSession = useCallback((): User | null => {
    try {
      const cachedSessionStr = sessionStorage.getItem('cached_user_session');
      if (cachedSessionStr) {
        const cachedSession = JSON.parse(cachedSessionStr);
        if (cachedSession.user) {
          console.log('Recovering from cached session due to service unavailability');

          // Notification disabled per user request
          // No toast will be shown for cached login

          return cachedSession.user;
        }
      }
    } catch (e) {
      console.error('Failed to parse cached session:', e);
    }
    return null;
  }, []);

  useEffect(() => {
    const getSession = async () => {
      try {
        setLoading(true);

        // Try to get the session with retry logic
        try {
          // First check for a cached session to speed up initial load
          const cachedUser = recoverCachedSession();
          if (cachedUser) {
            console.log('Using cached session for faster initial load');
            setUser(cachedUser);
            setLoading(false);

            // Continue loading the real session in the background
            setTimeout(async () => {
              try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (session?.user) {
                  setUser(session.user);

                  // Update the cache
                  try {
                    sessionStorage.setItem('cached_user_session', JSON.stringify(session));
                  } catch (e) {
                    console.error("Failed to cache session:", e);
                  }

                  // Defer auto-completion to after page load
                  if (session.user.id) {
                    setTimeout(() => {
                      handleAutoCompletion(session.user.id).catch(err => {
                        console.error('Error in handleAutoCompletion:', err);
                      });
                    }, 5000); // Delay by 5 seconds after page load
                  }
                }
              } catch (bgError) {
                console.error('Background session refresh error:', bgError);
              }
            }, 1000);

            return;
          }

          // No cached session, get a fresh one
          const { data: { session }, error } = await executeWithRetry(() =>
            supabase.auth.getSession()
          );

          // Check for service unavailability
          if (error && handleServiceUnavailable(error)) {
            const cachedUser = recoverCachedSession();
            if (cachedUser) {
              setUser(cachedUser);
              setLoading(false);
              return;
            }
          }

          if (session?.user) {
            setUser(session.user);

            // Cache the session for future use
            try {
              sessionStorage.setItem('cached_user_session', JSON.stringify(session));
            } catch (e) {
              console.error("Failed to cache session:", e);
            }

            // Defer auto-completion to after page load is complete
            if (session.user.id) {
              // Use requestIdleCallback or setTimeout to defer non-critical operations
              if ('requestIdleCallback' in window) {
                window.requestIdleCallback(() => {
                  handleAutoCompletion(session.user.id).catch(err => {
                    console.error('Error in handleAutoCompletion:', err);
                  });
                }, { timeout: 5000 });
              } else {
                setTimeout(() => {
                  handleAutoCompletion(session.user.id).catch(err => {
                    console.error('Error in handleAutoCompletion:', err);
                  });
                }, 3000); // Delay by 3 seconds
              }
            }
          } else {
            setUser(null);
          }
        } catch (error: any) {
          console.error("Error getting session:", error);

          // Try to recover from cached session if Supabase is unavailable
          if (handleServiceUnavailable(error)) {
            const cachedUser = recoverCachedSession();
            if (cachedUser) {
              setUser(cachedUser);
            } else {
              toast.error("Unable to connect to the server. Please check your internet connection.", {
                duration: 5000
              });
            }
          } else {
            toast.error("Unable to connect to the server. Please check your internet connection.", {
              duration: 5000
            });
          }
        }
      } finally {
        setLoading(false);
      }
    };

    getSession();

    const authListener = supabase.auth.onAuthStateChange((event, session: Session | null) => {
      if (event === 'SIGNED_IN') {
        if (session?.user) {
          setUser(session.user);

          // Defer auto-completion when user signs in
          if (session.user.id) {
            setTimeout(() => {
              handleAutoCompletion(session.user.id).catch(err => {
                console.error('Error in handleAutoCompletion:', err);
              });
            }, 2000); // Delay by 2 seconds
          }
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
      }

      setLoading(false);
    });

    // Clean up the subscription when the component unmounts
    return () => {
      authListener.data.subscription.unsubscribe();
    };
  }, [handleAutoCompletion, recoverCachedSession]);

  const signIn = async (email: string, password: string): Promise<AuthResponse> => {
    setLoading(true);
    try {
      // Use executeWithRetry for better error handling
      const response = await executeWithRetry(() =>
        supabase.auth.signInWithPassword({ email, password })
      );

      if (response.error) {
        console.error('Sign-in error:', response.error);
        if (response.error.message.includes('Invalid login credentials')) {
          toast.error('Invalid email or password');
        } else if (handleServiceUnavailable(response.error)) {
          toast.error('Network error. Supabase service is currently unavailable.', {
            description: 'We are using cached data where possible.'
          });
        } else {
          toast.error(response.error.message || 'Failed to sign in');
        }
      } else if (response.data.user) {
        // Auto-complete courses for the user - non-blocking
        handleAutoCompletion(response.data.user.id);
      }
      return response;
    } catch (error: any) {
      console.error('Sign-in error:', error);
      if (handleServiceUnavailable(error)) {
        toast.error('Network error. Supabase service is currently unavailable.');
      } else {
        toast.error(error.message || 'Failed to sign in');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, userData: any = {}): Promise<AuthResponse> => {
    setLoading(true);
    try {
      // Ensure user metadata always sets student role, regardless of what was passed
      if (userData) {
        userData.requested_role = 'student';
      }

      // Use executeWithRetry for better error handling
      const response = await executeWithRetry(() =>
        supabase.auth.signUp({
          email,
          password,
          options: {
            data: userData,
            emailRedirectTo: `${window.location.origin}/auth/callback`,
          },
        })
      );

      if (response.error) {
        console.error('Sign-up error:', response.error);
        if (response.error.message.includes('already registered')) {
          toast.error('This email is already registered');
        } else if (handleServiceUnavailable(response.error)) {
          toast.error('Network error. Supabase service is currently unavailable.');
        } else {
          toast.error(response.error.message || 'Failed to sign up');
        }
      } else if (response.data.user) {
        // Check if email confirmation is required
        if (response.data.user.identities?.length === 0) {
          toast.info('A confirmation email has been sent to your email address. Please check your inbox and confirm your email to complete registration.');
        } else {
          // Auto-complete courses for the user - non-blocking
          handleAutoCompletion(response.data.user.id);

          // Always redirect to dashboard regardless of requested role
          setTimeout(() => {
            navigate('/dashboard');
          }, 1000);

          // Assign role based on user's selection
          try {
            // Import dynamically to avoid circular dependencies
            const { assignStudentRole } = await import('@/services/auth/userRoleService');

            // Always assign student role regardless of what was requested
            await assignStudentRole(response.data.user.id);
            
          } catch (roleError) {
            console.error('Error assigning role:', roleError);
          }

          // Show a welcome toast
          setTimeout(() => {
            toast.success('Welcome! Your account has been created successfully.');
          }, 1500); // Delay toast to avoid UI blocking
        }
      }
      return response;
    } catch (error: any) {
      console.error('Sign-up error:', error);
      if (handleServiceUnavailable(error)) {
        toast.error('Network error. Supabase service is currently unavailable.');
      } else {
        toast.error(error.message || 'Failed to sign up');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      console.log('Starting sign out process');

      // Immediately set user to null and redirect to improve perceived performance
      setUser(null);

      // Show a loading indicator
      const toastId = toast.loading('Signing out...');

      // Navigate first for better user experience
      navigate('/login');

      // Then perform the actual sign out in the background
      try {
        await supabase.auth.signOut();
        console.log('Sign out API call completed');
        toast.success('You have been signed out', { id: toastId });
      } catch (error: any) {
        console.error('Error during Supabase sign out:', error);
        // The user is already signed out locally, so we just update the toast
        toast.error('Error during sign out, but you have been signed out locally', { id: toastId });
      }
    } catch (error: any) {
      console.error('Error in sign out flow:', error);
      toast.error('Failed to sign out: ' + (error.message || 'Unknown error'));

      // Force sign out locally even if there was an error in the flow
      setUser(null);
      navigate('/login');
    }
  };

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo<AuthContextType>(() => ({
    user,
    signIn,
    signUp,
    signOut,
    loading,
    setUser,
  }), [user, loading]); // Only re-create when user or loading state changes

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
