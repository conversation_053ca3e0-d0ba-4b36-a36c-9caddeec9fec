import React, { createContext, useContext, useState, useEffect } from 'react';
import { useReducedMotion } from '@/hooks/useReducedMotion';

interface MotionContextType {
  shouldReduceMotion: boolean;
  enableAnimations: boolean;
  setEnableAnimations: (enable: boolean) => void;
  // Animation presets for consistent animations
  transitions: {
    default: {
      type: string;
      duration: number;
      ease: string;
    };
    spring: {
      type: string;
      stiffness: number;
      damping: number;
    };
    microInteraction: {
      type: string;
      duration: number;
    };
  };
}

const defaultContext: MotionContextType = {
  shouldReduceMotion: false,
  enableAnimations: true,
  setEnableAnimations: () => {},
  transitions: {
    default: {
      type: 'tween',
      duration: 0.2, // Reduced from 0.3
      ease: 'easeOut',
    },
    spring: {
      type: 'spring',
      stiffness: 400, // Increased from 300 for faster animations
      damping: 30, // Increased from 25 for less oscillation
    },
    microInteraction: {
      type: 'tween',
      duration: 0.1, // Reduced from 0.15
    },
  },
};

const MotionContext = createContext<MotionContextType>(defaultContext);

export const useMotion = () => useContext(MotionContext);

export const MotionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const prefersReducedMotion = useReducedMotion();
  const [enableAnimations, setEnableAnimations] = useState<boolean>(
    localStorage.getItem('enableAnimations') !== 'false'
  );

  // Update localStorage when enableAnimations changes
  useEffect(() => {
    localStorage.setItem('enableAnimations', enableAnimations.toString());
  }, [enableAnimations]);

  // Determine if motion should be reduced
  const shouldReduceMotion = prefersReducedMotion || !enableAnimations;

  // Provide reduced transition values if motion should be reduced
  const transitions = shouldReduceMotion
    ? {
        default: {
          type: 'tween',
          duration: 0.1,
          ease: 'easeOut',
        },
        spring: {
          type: 'tween',
          stiffness: 0,
          damping: 0,
        },
        microInteraction: {
          type: 'tween',
          duration: 0.05,
        },
      }
    : defaultContext.transitions;

  return (
    <MotionContext.Provider
      value={{
        shouldReduceMotion,
        enableAnimations,
        setEnableAnimations,
        transitions,
      }}
    >
      {children}
    </MotionContext.Provider>
  );
};
