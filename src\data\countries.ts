export const countries = [
  "Ghana",
  "Afghanistan",
  "Albania", 
  "Algeria",
  "Argentina",
  "Armenia",
  "Australia",
  "Austria",
  "Azerbaijan",
  "Bahrain",
  "Bangladesh",
  "Belarus",
  "Belgium",
  "Bolivia",
  "Bosnia and Herzegovina",
  "Brazil",
  "Bulgaria",
  "Cambodia",
  "Cameroon",
  "Canada",
  "Chile",
  "China",
  "Colombia",
  "Costa Rica",
  "Croatia",
  "Czech Republic",
  "Denmark",
  "Ecuador",
  "Egypt",
  "Estonia",
  "Ethiopia",
  "Finland",
  "France",
  "Georgia",
  "Germany",
  "Greece",
  "Guatemala",
  "Honduras",
  "Hungary",
  "Iceland",
  "India",
  "Indonesia",
  "Iran",
  "Iraq",
  "Ireland",
  "Israel",
  "Italy",
  "Japan",
  "Jordan",
  "Kazakhstan",
  "Kenya",
  "Kuwait",
  "Latvia",
  "Lebanon",
  "Lithuania",
  "Luxembourg",
  "Malaysia",
  "Mexico",
  "Morocco",
  "Netherlands",
  "New Zealand",
  "Nigeria",
  "Norway",
  "Pakistan",
  "Peru",
  "Philippines",
  "Poland",
  "Portugal",
  "Qatar",
  "Romania",
  "Russia",
  "Saudi Arabia",
  "Singapore",
  "Slovakia",
  "Slovenia",
  "South Africa",
  "South Korea",
  "Spain",
  "Sri Lanka",
  "Sweden",
  "Switzerland",
  "Thailand",
  "Turkey",
  "Ukraine",
  "United Arab Emirates",
  "United Kingdom",
  "United States",
  "Uruguay",
  "Venezuela",
  "Vietnam",
  "Other"
];

// Function to determine if a question should use dropdown based on options count
export function shouldUseDropdown(question: any): boolean {
  // Use dropdown for specific questions or when there are many options
  const dropdownQuestions = ['country', 'university', 'location', 'undergraduate_year'];
  
  if (dropdownQuestions.includes(question.id)) {
    return true;
  }
  
  // Use dropdown if more than 5 options
  return question.options && question.options.length > 5;
}

// Get options for specific questions
export function getQuestionOptions(questionId: string, originalOptions?: string[]): string[] {
  switch (questionId) {
    case 'country':
      return countries;
    default:
      return originalOptions || [];
  }
}

// Get default value for specific questions
export function getQuestionDefault(questionId: string): string | undefined {
  switch (questionId) {
    case 'country':
      return 'Ghana';
    default:
      return undefined;
  }
}
