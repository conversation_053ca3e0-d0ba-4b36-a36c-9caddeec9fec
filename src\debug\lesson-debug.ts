/**
 * Debug utilities for lesson content issues
 */

import { supabase } from '@/integrations/supabase/client';

export async function debugLessonContent(lessonSlug: string) {
  console.log('🔍 Debugging lesson content for:', lessonSlug);
  
  try {
    // Fetch the lesson directly from the database
    const { data: lesson, error } = await supabase
      .from('lessons')
      .select('*')
      .eq('slug', lessonSlug)
      .single();
    
    if (error) {
      console.error('❌ Error fetching lesson:', error);
      return;
    }
    
    if (!lesson) {
      console.error('❌ Lesson not found');
      return;
    }
    
    console.log('✅ Lesson found:', {
      id: lesson.id,
      title: lesson.title,
      slug: lesson.slug,
      contentType: typeof lesson.content,
      contentLength: lesson.content?.length || 0,
      hasContent: !!lesson.content,
      contentPreview: lesson.content?.substring(0, 200) + '...'
    });
    
    // Try to parse content if it's JSON
    if (lesson.content) {
      try {
        const parsed = JSON.parse(lesson.content);
        console.log('📄 Content is JSON:', {
          hasVideoUrl: !!parsed.videoUrl,
          hasImageUrl: !!parsed.imageUrl,
          hasContent: !!parsed.content,
          hasExternalRedirectUrl: !!parsed.externalRedirectUrl,
          keys: Object.keys(parsed)
        });
        
        if (parsed.content) {
          console.log('📝 Inner content preview:', parsed.content.substring(0, 200) + '...');
        }
      } catch (e) {
        console.log('📝 Content is plain text/markdown');
        console.log('Content preview:', lesson.content.substring(0, 500) + '...');
      }
    }
    
    return lesson;
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

export async function debugAllLessons() {
  console.log('🔍 Debugging all lessons...');
  
  try {
    const { data: lessons, error } = await supabase
      .from('lessons')
      .select('id, title, slug, content')
      .limit(10);
    
    if (error) {
      console.error('❌ Error fetching lessons:', error);
      return;
    }
    
    console.log(`✅ Found ${lessons?.length || 0} lessons`);
    
    lessons?.forEach((lesson, index) => {
      console.log(`${index + 1}. ${lesson.title} (${lesson.slug})`);
      console.log(`   Content: ${lesson.content ? 'Yes' : 'No'} (${lesson.content?.length || 0} chars)`);
      
      if (lesson.content) {
        try {
          const parsed = JSON.parse(lesson.content);
          console.log(`   Type: JSON (keys: ${Object.keys(parsed).join(', ')})`);
        } catch {
          console.log(`   Type: Plain text/markdown`);
        }
      }
      console.log('');
    });
    
    return lessons;
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Make functions available globally for browser console debugging
if (typeof window !== 'undefined') {
  (window as any).debugLessonContent = debugLessonContent;
  (window as any).debugAllLessons = debugAllLessons;
}
