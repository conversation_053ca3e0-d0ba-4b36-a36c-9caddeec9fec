// Debug script to test Supabase connection and lesson fetching
import { supabase } from '@/integrations/supabase/client';

export async function testSupabaseConnection() {
  console.log('=== Testing Supabase Connection ===');
  
  try {
    // Test basic connection
    console.log('1. Testing basic connection...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('lessons')
      .select('count')
      .limit(1);
    
    if (healthError) {
      console.error('Health check failed:', healthError);
      return false;
    }
    
    console.log('✓ Basic connection successful');
    
    // Test lessons table access
    console.log('2. Testing lessons table access...');
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, title, slug')
      .limit(5);
    
    if (lessonsError) {
      console.error('Lessons table access failed:', lessonsError);
      return false;
    }
    
    console.log('✓ Lessons table accessible');
    console.log('Available lessons:', lessons?.map(l => ({ id: l.id, title: l.title, slug: l.slug })));
    
    // Test specific lesson slug
    console.log('3. Testing specific lesson slug: introduction-to-intravenous-cannulation');
    const { data: specificLesson, error: specificError } = await supabase
      .from('lessons')
      .select('id, title, slug, content')
      .eq('slug', 'introduction-to-intravenous-cannulation')
      .single();
    
    if (specificError) {
      console.error('Specific lesson fetch failed:', specificError);
      console.error('Error details:', {
        code: specificError.code,
        message: specificError.message,
        details: specificError.details,
        hint: specificError.hint
      });
      return false;
    }
    
    console.log('✓ Specific lesson found:', {
      id: specificLesson.id,
      title: specificLesson.title,
      slug: specificLesson.slug,
      hasContent: !!specificLesson.content
    });
    
    return true;
  } catch (error) {
    console.error('Connection test failed with error:', error);
    return false;
  }
}

// Function to run the test from browser console
(window as any).testSupabaseConnection = testSupabaseConnection;
