# Offline Support Documentation

This document describes the offline support features implemented in the IVCAN Course LMS application.

## Overview

The offline support system allows the application to:

1. Detect online/offline status
2. Queue operations while offline
3. Process queued operations when back online
4. Provide visual indicators for users

## Components

### Hooks

#### `useOnlineStatus`

Located in `src/hooks/useOnlineStatus.ts`, this hook monitors the browser's online/offline status.

```tsx
import { useOnlineStatus } from '@/hooks/useOnlineStatus';

function MyComponent() {
  const { isOnline, isOffline } = useOnlineStatus();
  
  return (
    <div>
      {isOnline ? 'You are online' : 'You are offline'}
    </div>
  );
}
```

#### `useOfflineQueue`

Located in `src/hooks/useOfflineQueue.ts`, this hook provides methods for working with the offline operation queue.

```tsx
import { useOfflineQueue } from '@/hooks/useOfflineQueue';

function MyComponent() {
  const { 
    isOnline,
    queueSize,
    processQueue,
    queueOperation,
    removeOperation,
    executeOperation,
    getQueue
  } = useOfflineQueue();
  
  // Execute an operation with offline support
  const handleSaveData = async () => {
    const result = await executeOperation(
      'saveData',
      { data: 'example' },
      async () => {
        // Call API when online
        const response = await api.saveData('example');
        return response;
      }
    );
    
    if (result) {
      // Operation succeeded online
    } else {
      // Operation was queued for later
    }
  };
}
```

### Utility Functions

The enhanced connection manager (`src/lib/enhanced-connection-manager.ts`) provides utility functions:

- `executeWithOfflineSupport`: Executes a function with offline queueing
- `processOfflineQueue`: Processes all queued operations
- `queueOfflineOperation`: Manually adds an operation to the queue
- `removeFromOfflineQueue`: Removes an operation from the queue
- `getOfflineQueue`: Gets all queued operations

### UI Components

- `OfflineIndicator`: Shows when the user is offline
- `OfflineQueueStatus`: Displays pending operations and allows processing the queue

## Usage Example

See `src/examples/OfflineExample.tsx` for a complete example of using offline support.

## Best Practices

1. **Handle both online and offline scenarios:**
   Always check for both cases and provide appropriate UI feedback.

2. **Use `executeOperation` for critical operations:**
   This ensures that operations will be queued when offline.

3. **Keep operations small and atomic:**
   Each queued operation should be independent and handle its own data.

4. **Store minimal data in the queue:**
   Only store what's needed to replay the operation later.

5. **Handle failures gracefully:**
   Some operations might fail when processed later.

## Technical Details

### Offline Queue

The offline queue is stored in `localStorage` under the key `ivcan-offline-queue`. Each queue entry contains:

- `id`: Unique identifier
- `operation`: Operation name/type
- `params`: Operation parameters
- `timestamp`: When the operation was queued
- `retries`: Number of failed attempts

### Service Worker

The service worker (`public/service-worker.js`) handles caching for offline access:

- API requests use a network-first strategy
- Static assets use a cache-first strategy
- Fallbacks are provided for offline scenarios

## Limitations

1. `localStorage` has a size limit (typically 5-10MB)
2. Complex operations or large data might not be suitable for queueing
3. Conflicts may occur when processing operations after being offline for extended periods

## Future Improvements

- Implement data synchronization for offline content editing
- Add conflict resolution for concurrent edits
- Enhance offline UI experience with better visual indicators
- Add support for IndexedDB for larger operation storage 