import { useState, useEffect } from 'react';

/**
 * Hook to detect if the current device is a mobile device based on screen width
 * @param breakpoint The breakpoint to consider as mobile (default: 768px)
 * @returns boolean indicating if the device is mobile
 */
export function useIsMobile(breakpoint: number = 768): boolean {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    // Function to check if window width is less than breakpoint
    const checkMobile = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    // Set initial value
    checkMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [breakpoint]);

  return isMobile;
}

/**
 * Hook to detect the current device type based on screen width
 * @returns object with boolean flags for different device types
 */
export function useDeviceType() {
  const [deviceType, setDeviceType] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
  });

  useEffect(() => {
    // Function to check device type based on window width
    const checkDeviceType = () => {
      const width = window.innerWidth;
      setDeviceType({
        isMobile: width < 640,
        isTablet: width >= 640 && width < 1024,
        isDesktop: width >= 1024,
      });
    };

    // Set initial value
    checkDeviceType();

    // Add event listener for window resize
    window.addEventListener('resize', checkDeviceType);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('resize', checkDeviceType);
    };
  }, []);

  return deviceType;
}

/**
 * Hook to get the current screen size category
 * @returns string representing the current screen size ('xs', 'sm', 'md', 'lg', 'xl', '2xl')
 */
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('md');

  useEffect(() => {
    // Function to determine screen size category
    const getScreenSize = () => {
      const width = window.innerWidth;
      if (width < 480) return 'xs';
      if (width < 640) return 'sm';
      if (width < 768) return 'md';
      if (width < 1024) return 'lg';
      if (width < 1280) return 'xl';
      return '2xl';
    };

    // Set initial value
    setScreenSize(getScreenSize() as 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl');

    // Function to update screen size on resize
    const handleResize = () => {
      setScreenSize(getScreenSize() as 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl');
    };

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return screenSize;
}
