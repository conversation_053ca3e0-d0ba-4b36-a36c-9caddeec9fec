/**
 * useError<PERSON><PERSON><PERSON> Hook
 * 
 * This hook provides a convenient way to use the error service in React components.
 * It creates an error handler with the component name and user ID pre-filled.
 */

import { useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { 
  handleError, 
  createError<PERSON><PERSON><PERSON>, 
  executeWithErrorHandling,
  executeWithDefault,
  ErrorContext
} from '@/services/error/errorService';

interface UseErrorHandlerOptions {
  componentName?: string;
}

/**
 * Hook to create an error handler for a component
 * @param options Options for the error handler
 * @returns Error handling functions
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const { user } = useAuth();
  const userId = user?.id;
  const componentName = options.componentName || 'UnknownComponent';
  
  // Create a default context for all errors from this component
  const defaultContext: ErrorContext = {
    userId,
    component: componentName
  };
  
  // Create an error handler with the default context
  const errorHandler = useCallback(
    (error: any, additionalContext: ErrorContext = {}) => {
      handleError(error, {
        ...defaultContext,
        ...additionalContext
      });
    },
    [defaultContext]
  );
  
  // Create a function to execute with error handling
  const executeWithHandling = useCallback(
    async <T>(
      fn: () => Promise<T>,
      actionName?: string
    ): Promise<T | undefined> => {
      return executeWithErrorHandling(fn, {
        ...defaultContext,
        action: actionName
      });
    },
    [defaultContext]
  );
  
  // Create a function to execute with a default value on error
  const executeWithDefaultValue = useCallback(
    async <T>(
      fn: () => Promise<T>,
      defaultValue: T,
      actionName?: string
    ): Promise<T> => {
      return executeWithDefault(fn, defaultValue, {
        ...defaultContext,
        action: actionName
      });
    },
    [defaultContext]
  );
  
  return {
    handleError: errorHandler,
    executeWithHandling,
    executeWithDefaultValue,
    createContextualHandler: (context: ErrorContext) => 
      createErrorHandler({ ...defaultContext, ...context })
  };
}

export default useErrorHandler;
