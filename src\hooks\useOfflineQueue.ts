import { useCallback, useEffect, useState } from 'react';
import useOnlineStatus from './useOnlineStatus';
import {
  getOfflineQueue,
  processOfflineQueue,
  queueOfflineOperation,
  removeFromOfflineQueue,
  executeWithOfflineSupport
} from '@/lib/enhanced-connection-manager';

/**
 * Hook for working with offline operations queue
 */
export function useOfflineQueue() {
  const { isOnline } = useOnlineStatus();
  const [queueSize, setQueueSize] = useState<number>(0);
  
  // Update queue size when component mounts or when online status changes
  useEffect(() => {
    const queue = getOfflineQueue();
    setQueueSize(queue.length);
    
    // If we just came back online and have queued operations, process them
    if (isOnline && queue.length > 0) {
      processQueue();
    }
  }, [isOnline]);
  
  // Process the offline queue with a custom processor function
  const processQueue = useCallback(async (
    processor?: (operation: string, params: any) => Promise<any>
  ) => {
    if (!isOnline) return { successful: 0, failed: 0 };
    
    // Using the default processor if none provided
    const defaultProcessor = async (operation: string, params: any) => {
      console.log(`Processing operation: ${operation}`, params);
      // This would typically call an API or perform some operation
      // based on the operation type and parameters
      return true;
    };
    
    const result = await processOfflineQueue(processor || defaultProcessor);
    setQueueSize(getOfflineQueue().length);
    return result;
  }, [isOnline]);
  
  // Add an operation to the queue
  const queueOperation = useCallback((operation: string, params: any): string => {
    const id = queueOfflineOperation(operation, params);
    setQueueSize(getOfflineQueue().length);
    return id;
  }, []);
  
  // Remove an operation from the queue
  const removeOperation = useCallback((id: string): boolean => {
    const result = removeFromOfflineQueue(id);
    setQueueSize(getOfflineQueue().length);
    return result;
  }, []);
  
  // Execute an operation with offline support
  const executeOperation = useCallback(async <T>(
    operation: string,
    params: any,
    onlineFn: () => Promise<T>
  ): Promise<T | null> => {
    const result = await executeWithOfflineSupport(
      operation,
      params,
      onlineFn,
      isOnline
    );
    
    // Update queue size in case the operation was queued
    setQueueSize(getOfflineQueue().length);
    
    return result;
  }, [isOnline]);
  
  return {
    isOnline,
    queueSize,
    processQueue,
    queueOperation,
    removeOperation,
    executeOperation,
    getQueue: getOfflineQueue
  };
} 