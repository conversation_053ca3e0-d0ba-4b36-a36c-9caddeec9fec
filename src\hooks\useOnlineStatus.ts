import { useState, useEffect } from 'react';
import { toast } from 'sonner';

/**
 * Hook to monitor online/offline status with persistence
 * @returns {Object} Object containing online status information
 */
export function useOnlineStatus() {
  // Get initial state from navigator if available, otherwise assume online
  const [isOnline, setIsOnline] = useState<boolean>(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  
  // Track previous state for comparison
  const [wasOffline, setWasOffline] = useState<boolean>(false);

  useEffect(() => {
    // Handler for when network comes back online
    const handleOnline = () => {
      setIsOnline(true);
      
      // Only show toast if we were previously offline
      if (wasOffline) {
        toast.success('You are back online');
        setWasOffline(false);
      }
    };

    // Handler for when network goes offline
    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      toast.error('You are offline. Some features may be limited.');
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return {
    isOnline,
    isOffline: !isOnline
  };
}

export default useOnlineStatus; 