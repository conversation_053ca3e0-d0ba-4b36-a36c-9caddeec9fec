
import React from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Hook for real-time course data sync
 */
export const useRealtimeCourseSync = (courseId: string | null) => {
  const queryClient = useQueryClient();
  
  React.useEffect(() => {
    if (!courseId) return;
    
    // Set up real-time channel subscription for the specific course
    const channel = supabase
      .channel(`course-changes-${courseId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'courses',
        filter: `id=eq.${courseId}`
      }, (payload) => {
        console.log('Course updated:', payload);
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({
          queryKey: ['course', courseId],
        });
        queryClient.invalidateQueries({
          queryKey: ['admin-courses'],
        });
        toast.info('Course information has been updated');
      })
      .subscribe();
      
    // Listen for module changes for this course
    const moduleChannel = supabase
      .channel(`module-changes-${courseId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'modules',
        filter: `course_id=eq.${courseId}`
      }, (payload) => {
        console.log('Module updated:', payload);
        queryClient.invalidateQueries({
          queryKey: ['courseModules', courseId],
        });
        queryClient.invalidateQueries({
          queryKey: ['admin-modules', courseId],
        });
        toast.info('Course modules have been updated');
      })
      .subscribe();
      
    // Listen for lesson changes related to this course's modules
    const lessonChannel = supabase
      .channel(`lesson-changes-${courseId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'lessons'
      }, (payload) => {
        // Since we can't filter by course_id directly (lessons are linked to modules)
        // we'll invalidate all lesson queries when any lesson changes
        console.log('Lesson updated:', payload);
        queryClient.invalidateQueries({
          queryKey: ['courseModules', courseId],
        });
        toast.info('Course content has been updated');
      })
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
      supabase.removeChannel(moduleChannel);
      supabase.removeChannel(lessonChannel);
    };
  }, [courseId, queryClient]);
};

export default useRealtimeCourseSync;
