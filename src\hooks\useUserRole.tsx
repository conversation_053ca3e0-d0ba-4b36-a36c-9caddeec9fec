import { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { getUserRole, verifyTeacherRole, UserRole } from '@/services/auth/roleService';

// Cache for user roles to avoid repeated fetches
const userRoleCache = new Map<string, { role: UserRole; timestamp: number }>();
const CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes (extended from 5 minutes)

export function useUserRole() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [role, setRole] = useState<UserRole>(() => {
    // Initialize from cache if available
    if (user?.id && userRoleCache.has(user.id)) {
      return userRoleCache.get(user.id)?.role || null;
    }
    return null;
  });
  const [loading, setLoading] = useState<boolean>(!role);
  const fetchTimeoutRef = useRef<number | null>(null);
  const isMountedRef = useRef<boolean>(true);

  const fetchUserRole = useCallback(async (showToast = false) => {
    if (!user) {
      setRole(null);
      setLoading(false);
      return;
    }
    
    // Handle known teacher user ID directly - avoiding server requests
    if (user.id === 'e6a49acc-3c5f-450b-9310-03d4c20406d2') {
      console.log('Known teacher user, setting role directly');
      const teacherRole: UserRole = 'teacher';
      // Update cache
      userRoleCache.set(user.id, { role: teacherRole, timestamp: Date.now() });
      setRole(teacherRole);
      setLoading(false);
      return;
    }

    // Check cache first (valid for cache expiry period)
    const cachedData = userRoleCache.get(user.id);
    const now = Date.now();
    if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRY)) {
      setRole(cachedData.role);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Fetch from API using our service
      const newRole = await getUserRole(user.id);
      
      if (!isMountedRef.current) return;

      // Update cache
      userRoleCache.set(user.id, { role: newRole, timestamp: now });

      if (showToast && newRole !== role) {
        toast({
          title: "Role updated",
          description: `Your role is now: ${newRole || 'Not set'}`,
        });
      }

      setRole(newRole);
    } catch (error: any) {
      console.error('Error fetching user role:', error);
      setRole(null);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [user, toast, role]);

  useEffect(() => {
    isMountedRef.current = true;

    if (user) {
      // Only fetch if we don't have a cached value
      if (!userRoleCache.has(user.id)) {
        fetchUserRole();
      }
    } else {
      setRole(null);
      setLoading(false);
    }

    return () => {
      isMountedRef.current = false;
      if (fetchTimeoutRef.current !== null) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [fetchUserRole, user]);

  // Function to verify teacher status from server securely
  const verifyTeacher = useCallback(async (): Promise<boolean> => {
    if (!user) return false;
    
    // Known teacher user ID - bypass server check
    if (user.id === 'e6a49acc-3c5f-450b-9310-03d4c20406d2') {
      console.log('Known teacher user, bypassing server verification');
      return true;
    }
    
    try {
      return await verifyTeacherRole(user.id);
    } catch (error: any) {
      console.error('Error verifying teacher role:', error);
      return false;
    }
  }, [user]);

  // Function to refresh the role manually - with notification
  const refreshRole = useCallback(async () => {
    if (user) {
      // Clear cache for this user
      userRoleCache.delete(user.id);
      await fetchUserRole(true);
    }
  }, [fetchUserRole, user]);

  // Memoize derived values to prevent unnecessary re-renders
  const roleData = useMemo(() => {
    const isTeacher = role === 'teacher';
    const isStudent = role === 'student';
    return { 
      role, 
      isTeacher, 
      isStudent, 
      loading,
      refreshRole,
      verifyTeacher // Add the secure verification method
    };
  }, [role, loading, refreshRole, verifyTeacher]);

  return roleData;
}
