/**
 * Supabase client configuration
 *
 * This file configures the Supabase client with optimized settings
 * and uses the centralized configuration from src/config/supabase.ts
 */
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';
import { isServiceUnavailableError } from '@/lib/connection-manager';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/config/supabase';

// Counter for current retries to implement circuit breaking
let currentConsecutiveFailures = 0;
const MAX_CONSECUTIVE_FAILURES = 20; // After this many failures, we'll back off more aggressively
const CIRCUIT_BREAKER_RESET_TIMEOUT = 60000; // 1 minute before trying normal requests again
let circuitBreakerTripped = false;
let circuitBreakerResetTimer: ReturnType<typeof setTimeout> | null = null;

// Reset circuit breaker if it was previously tripped
if (circuitBreakerTripped) {
  console.log('Manually resetting circuit breaker');
  circuitBreakerTripped = false;
  currentConsecutiveFailures = 0;
  if (circuitBreakerResetTimer) {
    clearTimeout(circuitBreakerResetTimer);
    circuitBreakerResetTimer = null;
  }
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Enhanced fetch implementation with improved retries and timeouts
const customFetch = async (url: RequestInfo | URL, options?: RequestInit): Promise<Response> => {
  const MAX_RETRIES = 3; // Increase retries for better reliability
  const INITIAL_BACKOFF = 500; // 500ms initial backoff
  const DEFAULT_TIMEOUT = 8000; // 8 seconds timeout for most requests
  const AUTH_TIMEOUT = 10000; // 10 seconds timeout for auth requests

  // Check if this is a critical auth request
  const urlStr = url.toString();
  const isAuthRequest = urlStr.includes('/auth/');

  // Set appropriate timeout based on request type
  const timeout = isAuthRequest ? AUTH_TIMEOUT : DEFAULT_TIMEOUT;

  // For non-auth requests during circuit breaker, fail fast
  if (circuitBreakerTripped && !isAuthRequest) {
    console.warn('Circuit breaker active, non-auth request rejected');
    return Promise.reject(new Error('Service temporarily unavailable'));
  }

  let retries = 0;
  let lastError: Error | null = null;

  while (retries < MAX_RETRIES) {
    try {
      // Create AbortController for this attempt
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Check if service is unavailable
      if (isServiceUnavailableError(response)) {
        currentConsecutiveFailures++;
        
        // If we've hit too many failures, trip the circuit breaker
        if (currentConsecutiveFailures >= MAX_CONSECUTIVE_FAILURES && !circuitBreakerTripped) {
          console.warn('Circuit breaker tripped due to consecutive failures');
          circuitBreakerTripped = true;
          
          // Set timer to reset circuit breaker
          circuitBreakerResetTimer = setTimeout(() => {
            console.log('Circuit breaker reset timer expired');
            circuitBreakerTripped = false;
            currentConsecutiveFailures = 0;
            circuitBreakerResetTimer = null;
          }, CIRCUIT_BREAKER_RESET_TIMEOUT);
        }
        
        throw new Error('Service unavailable');
      }

      // Reset failure counter on success
      currentConsecutiveFailures = 0;
      
      return response;
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry if circuit breaker is tripped
      if (circuitBreakerTripped) {
        break;
      }
      
      // Don't retry certain errors
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('Request timed out');
          break;
        }
      }
      
      retries++;
      if (retries < MAX_RETRIES) {
        // Exponential backoff
        const backoff = INITIAL_BACKOFF * Math.pow(2, retries - 1);
        await new Promise(resolve => setTimeout(resolve, backoff));
      }
    }
  }

  // If we got here, all retries failed
  throw lastError || new Error('Request failed');
};

// Create client with optimized settings for better connection handling
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    storageKey: 'ivcan-lms-auth',
    flowType: 'pkce', // More secure authentication flow
  },
  global: {
    headers: {
      'x-application-name': 'ivcan-lms',
      'Cache-Control': 'no-cache', // Prevent caching issues
    },
    fetch: customFetch,
  },
  realtime: {
    params: {
      eventsPerSecond: 1, // Reduce to avoid rate limiting
    },
    heartbeatIntervalMs: 15000, // More frequent heartbeats
    reconnectAfterMs: (attempts) => Math.min(10000, 1000 * Math.pow(2, attempts)), // Exponential backoff with max
  },
  db: {
    schema: 'public',
  }
});

// Export a connection health check function
export const checkSupabaseConnection = async (): Promise<boolean> => {
  try {
    // Use a schema-level query instead of a specific table
    const { error } = await supabase
      .from('user_roles')
      .select('id')
      .limit(1);
    return !error;
  } catch (err) {
    console.error('Supabase connection check failed:', err);
    return false;
  }
};