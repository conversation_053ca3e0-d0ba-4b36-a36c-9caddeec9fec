/**
 * Advanced Markdown serializer with full GitHub Flavored Markdown support
 * Supports tables, task lists, details/summary, callouts, and more
 */

import { Node } from '@tiptap/pm/model';
import TurndownService from 'turndown';
import { gfm } from 'turndown-plugin-gfm';

export interface AdvancedSerializerOptions {
  tightLists?: boolean;
  preserveWhitespace?: boolean;
  gfmTables?: boolean;
  taskLists?: boolean;
  details?: boolean;
  callouts?: boolean;
}

export class AdvancedMarkdownSerializer {
  private options: AdvancedSerializerOptions;
  private turndownService: TurndownService;

  constructor(options: AdvancedSerializerOptions = {}) {
    this.options = {
      tightLists: false,
      preserveWhitespace: false,
      gfmTables: true,
      taskLists: true,
      details: true,
      callouts: true,
      ...options,
    };

    // Initialize Turndown with GFM plugin
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      hr: '---',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
    });

    // Add GFM plugin for tables, strikethrough, etc.
    this.turndownService.use(gfm);

    // Add custom rules
    this.addCustomRules();
  }

  private addCustomRules() {
    // Task list items
    if (this.options.taskLists) {
      this.turndownService.addRule('taskListItem', {
        filter: (node) => {
          return node.nodeName === 'LI' && 
                 node.getAttribute('data-type') === 'taskItem';
        },
        replacement: (content, node) => {
          const checkbox = node.querySelector('input[type="checkbox"]');
          const checked = checkbox?.checked ? 'x' : ' ';
          return `- [${checked}] ${content}\n`;
        }
      });
    }

    // Details/Summary (collapsible sections)
    if (this.options.details) {
      this.turndownService.addRule('details', {
        filter: 'details',
        replacement: (content, node) => {
          const summary = node.querySelector('summary');
          const summaryText = summary?.textContent || 'Details';
          const detailsContent = content.replace(summaryText, '').trim();
          return `<details>\n<summary>${summaryText}</summary>\n\n${detailsContent}\n</details>\n\n`;
        }
      });
    }

    // Callouts/Admonitions
    if (this.options.callouts) {
      this.turndownService.addRule('callout', {
        filter: (node) => {
          return node.nodeName === 'DIV' && 
                 node.getAttribute('data-callout') !== null;
        },
        replacement: (content, node) => {
          const type = node.getAttribute('data-type') || 'info';
          return `> [!${type.toUpperCase()}]\n> ${content.split('\n').join('\n> ')}\n\n`;
        }
      });
    }

    // Code blocks with language
    this.turndownService.addRule('codeBlock', {
      filter: (node) => {
        return node.nodeName === 'PRE' && node.querySelector('code');
      },
      replacement: (content, node) => {
        const codeElement = node.querySelector('code');
        const language = codeElement?.className?.match(/language-(\w+)/)?.[1] || '';
        const code = codeElement?.textContent || content;
        return `\`\`\`${language}\n${code}\n\`\`\`\n\n`;
      }
    });

    // Highlight/Mark
    this.turndownService.addRule('highlight', {
      filter: 'mark',
      replacement: (content) => `==${content}==`
    });

    // Underline
    this.turndownService.addRule('underline', {
      filter: 'u',
      replacement: (content) => `<u>${content}</u>`
    });

    // YouTube embeds
    this.turndownService.addRule('youtube', {
      filter: (node) => {
        return node.nodeName === 'DIV' && 
               node.getAttribute('data-youtube-video') !== null;
      },
      replacement: (content, node) => {
        const iframe = node.querySelector('iframe');
        const src = iframe?.getAttribute('src') || '';
        return `[![YouTube Video](https://img.youtube.com/vi/${this.extractYouTubeId(src)}/0.jpg)](${src})\n\n`;
      }
    });

    // Images with alt text
    this.turndownService.addRule('image', {
      filter: 'img',
      replacement: (content, node) => {
        const src = node.getAttribute('src') || '';
        const alt = node.getAttribute('alt') || '';
        const title = node.getAttribute('title');
        const titlePart = title ? ` "${title}"` : '';
        return `![${alt}](${src}${titlePart})`;
      }
    });
  }

  private extractYouTubeId(url: string): string {
    const match = url.match(/(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/);
    return match?.[1] || '';
  }

  serialize(doc: Node): string {
    // Convert TipTap document to HTML first
    const html = this.nodeToHtml(doc);
    
    // Then convert HTML to Markdown using Turndown
    const markdown = this.turndownService.turndown(html);
    
    // Clean up the markdown
    return this.cleanMarkdown(markdown);
  }

  private nodeToHtml(node: Node): string {
    // This is a simplified HTML serializer for TipTap nodes
    // In a real implementation, you'd use TipTap's HTML serializer
    return this.serializeNodeToHtml(node);
  }

  private serializeNodeToHtml(node: Node): string {
    switch (node.type.name) {
      case 'doc':
        return this.serializeChildrenToHtml(node);
      
      case 'paragraph':
        const content = this.serializeChildrenToHtml(node);
        return content ? `<p>${content}</p>` : '';
      
      case 'heading':
        const level = node.attrs.level || 1;
        const headingContent = this.serializeChildrenToHtml(node);
        return `<h${level}>${headingContent}</h${level}>`;
      
      case 'bulletList':
        return `<ul>${this.serializeChildrenToHtml(node)}</ul>`;
      
      case 'orderedList':
        return `<ol>${this.serializeChildrenToHtml(node)}</ol>`;
      
      case 'listItem':
        return `<li>${this.serializeChildrenToHtml(node)}</li>`;
      
      case 'taskList':
        return `<ul data-type="taskList">${this.serializeChildrenToHtml(node)}</ul>`;
      
      case 'taskItem':
        const checked = node.attrs.checked ? 'checked' : '';
        const taskContent = this.serializeChildrenToHtml(node);
        return `<li data-type="taskItem"><input type="checkbox" ${checked}>${taskContent}</li>`;
      
      case 'codeBlock':
        const language = node.attrs.language || '';
        const code = node.textContent;
        return `<pre><code class="language-${language}">${code}</code></pre>`;
      
      case 'blockquote':
        return `<blockquote>${this.serializeChildrenToHtml(node)}</blockquote>`;
      
      case 'horizontalRule':
        return '<hr>';
      
      case 'image':
        const src = node.attrs.src || '';
        const alt = node.attrs.alt || '';
        const title = node.attrs.title || '';
        return `<img src="${src}" alt="${alt}" title="${title}">`;
      
      case 'table':
        return `<table>${this.serializeChildrenToHtml(node)}</table>`;
      
      case 'tableRow':
        return `<tr>${this.serializeChildrenToHtml(node)}</tr>`;
      
      case 'tableHeader':
        return `<th>${this.serializeChildrenToHtml(node)}</th>`;
      
      case 'tableCell':
        return `<td>${this.serializeChildrenToHtml(node)}</td>`;
      
      case 'details':
        return `<details>${this.serializeChildrenToHtml(node)}</details>`;
      
      case 'detailsSummary':
        return `<summary>${this.serializeChildrenToHtml(node)}</summary>`;
      
      case 'detailsContent':
        return `<div data-details-content>${this.serializeChildrenToHtml(node)}</div>`;
      
      case 'callout':
        const type = node.attrs.type || 'info';
        return `<div data-callout data-type="${type}">${this.serializeChildrenToHtml(node)}</div>`;
      
      case 'text':
        let text = node.text || '';
        
        // Apply marks
        if (node.marks) {
          for (const mark of node.marks) {
            switch (mark.type.name) {
              case 'bold':
                text = `<strong>${text}</strong>`;
                break;
              case 'italic':
                text = `<em>${text}</em>`;
                break;
              case 'underline':
                text = `<u>${text}</u>`;
                break;
              case 'strike':
                text = `<s>${text}</s>`;
                break;
              case 'code':
                text = `<code>${text}</code>`;
                break;
              case 'highlight':
                text = `<mark>${text}</mark>`;
                break;
              case 'link':
                const href = mark.attrs.href || '';
                const target = mark.attrs.target || '';
                text = `<a href="${href}" target="${target}">${text}</a>`;
                break;
            }
          }
        }
        
        return text;
      
      default:
        return this.serializeChildrenToHtml(node);
    }
  }

  private serializeChildrenToHtml(node: Node): string {
    let result = '';
    node.forEach((child) => {
      result += this.serializeNodeToHtml(child);
    });
    return result;
  }

  private cleanMarkdown(markdown: string): string {
    // Remove excessive line breaks
    markdown = markdown.replace(/\n{3,}/g, '\n\n');
    
    // Ensure proper spacing around headers
    markdown = markdown.replace(/^(#{1,6}\s.+)$/gm, '\n$1\n');
    
    // Clean up list formatting
    markdown = markdown.replace(/^(\s*[-*+]\s)/gm, '$1');
    
    return markdown.trim();
  }
}

/**
 * Converts TipTap editor content to advanced Markdown
 */
export function tiptapToAdvancedMarkdown(doc: Node, options?: AdvancedSerializerOptions): string {
  const serializer = new AdvancedMarkdownSerializer(options);
  return serializer.serialize(doc);
}

/**
 * Enhanced HTML to Markdown converter using Turndown
 */
export function htmlToAdvancedMarkdown(html: string, options?: AdvancedSerializerOptions): string {
  const serializer = new AdvancedMarkdownSerializer(options);
  return serializer.turndownService.turndown(html);
}
