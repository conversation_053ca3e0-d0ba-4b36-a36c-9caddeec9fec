import { supabase } from '@/integrations/supabase/client';
import { updateProfile } from './profile';
import { fileToDataUrl } from './file-upload';
import { uploadToStorage } from './storage-utils';

/**
 * Upload a profile picture to Supabase storage and update the user's profile
 * @param userId The user's ID
 * @param file The image file to upload
 * @returns The public URL of the uploaded image
 */
export async function uploadProfilePicture(userId: string, file: File): Promise<string> {
  if (!userId || !file) {
    throw new Error('User ID and file are required');
  }

  // Validate file type
  if (!file.type.startsWith('image/')) {
    throw new Error('Please select an image file');
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    throw new Error('Image size should be less than 5MB');
  }

  // Create a unique file name
  const fileExt = file.name.split('.').pop();
  const fileName = `avatar-${userId}-${Date.now()}.${fileExt}`;
  const filePath = `avatars/${fileName}`;

  // Upload to Supabase Storage using our utility function
  // This will handle bucket creation/verification for us
  const publicUrl = await uploadToStorage('avatars', filePath, file);

  // Update the user's profile with the new avatar URL
  await updateProfile(userId, { avatar_url: publicUrl });

  // Update the user's metadata
  await supabase.auth.updateUser({
    data: { avatar_url: publicUrl }
  });

  return publicUrl;
}

/**
 * Generate a preview of an image file
 * @param file The image file
 * @returns A data URL for the image preview
 */
export async function generateImagePreview(file: File): Promise<string> {
  if (!file) {
    throw new Error('File is required');
  }

  if (!file.type.startsWith('image/')) {
    throw new Error('Please select an image file');
  }

  return await fileToDataUrl(file);
}

/**
 * Get the avatar URL for a user
 * First checks the profile, then falls back to user metadata
 * @param profile The user's profile
 * @param user The user object from auth
 * @returns The avatar URL or null
 */
export function getAvatarUrl(
  profile: { avatar_url?: string | null } | null,
  user: { user_metadata?: { avatar_url?: string | null } } | null
): string | null {
  if (profile?.avatar_url) {
    return profile.avatar_url;
  }

  if (user?.user_metadata?.avatar_url) {
    return user.user_metadata.avatar_url;
  }

  return null;
}
