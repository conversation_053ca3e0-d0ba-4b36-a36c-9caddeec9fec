/**
 * Connection manager for handling network operations with retry logic
 */

import { toast } from 'sonner';

// Cache to store last error time for specific error types
const errorCache = {
  serviceUnavailable: {
    lastErrorTime: 0,
    notificationShown: false,
    consecutiveFailures: 0
  }
};

// Constants for error handling
const ERROR_NOTIFICATION_COOLDOWN = 30000; // 30 seconds
const MAX_BACKOFF_TIME = 30000; // Maximum backoff time (30 seconds)
const INITIAL_BACKOFF = 1000; // Initial backoff time (1 second)

/**
 * Checks if an error is a service unavailability error
 */
export function isServiceUnavailableError(error: any): boolean {
  if (!error) return false;

  // Check for common service unavailability patterns
  return (
    (error.status === 503 || error.code === '503') ||
    (error.statusCode === 503 || error.status_code === 503) ||
    (typeof error.status === 'number' && error.status === 503) ||
    (error.message && (
      error.message.includes('service unavailable') ||
      error.message.includes('503') ||
      error.message.includes('failed after') ||
      error.message.toLowerCase().includes('service unavailable')
    ))
  );
}

/**
 * Handles service unavailability errors - notifications disabled per user request
 */
function handleServiceUnavailableError() {
  const now = Date.now();
  errorCache.serviceUnavailable.consecutiveFailures++;

  // Update the error cache but don't show notifications
  errorCache.serviceUnavailable = {
    lastErrorTime: now,
    notificationShown: true, // Mark as shown even though we don't show it
    consecutiveFailures: errorCache.serviceUnavailable.consecutiveFailures
  };
}

/**
 * Reset error counters when successful connections occur
 * Notifications disabled per user request
 */
function resetErrorCounters() {
  if (errorCache.serviceUnavailable.consecutiveFailures > 0) {
    errorCache.serviceUnavailable.consecutiveFailures = 0;

    // If we had previously shown error notifications, reset the state
    if (errorCache.serviceUnavailable.notificationShown) {
      errorCache.serviceUnavailable.notificationShown = false;

      // Attempt to reload user session in the background
      try {
        import('@/integrations/supabase/client').then(({ supabase }) => {
          supabase.auth.refreshSession();
        });
      } catch (error) {
        console.error('Failed to refresh session:', error);
      }
    }
  }
}

/**
 * Calculate exponential backoff with jitter
 */
function calculateBackoff(attempt: number, baseDelay: number = INITIAL_BACKOFF): number {
  // Exponential backoff: baseDelay * 2^attempt
  const exponentialDelay = baseDelay * Math.pow(2, attempt);

  // Add jitter (random factor between 0.8 and 1.2)
  const jitter = 0.8 + Math.random() * 0.4;

  // Limit to maximum backoff time
  return Math.min(MAX_BACKOFF_TIME, exponentialDelay * jitter);
}

/**
 * Generic function to execute a Supabase query with retry logic
 * T is the return type of the function being executed
 */
export async function executeWithRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 5,
  delay: number = 1000
): Promise<T> {
  let lastError: Error | null = null;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const result = await fn();

      // Reset error counters on success
      resetErrorCounters();

      return result;
    } catch (error: any) {
      lastError = error;

      // For logging, truncate any large response bodies to avoid console spam
      const truncatedError = { ...error };
      if (truncatedError.body && typeof truncatedError.body === 'string' && truncatedError.body.length > 500) {
        truncatedError.body = truncatedError.body.substring(0, 500) + '... [truncated]';
      }

      // Check for service unavailability
      if (isServiceUnavailableError(error)) {
        retries++;

        // Calculate appropriate backoff delay
        const backoffDelay = calculateBackoff(retries, delay);

        await new Promise(resolve => setTimeout(resolve, backoffDelay));
        continue;
      }

      // If error is related to network issues, retry after delay
      if (
        error.message &&
        (error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('connection') ||
          error.message.includes('Failed to fetch'))
      ) {
        retries++;
        // Linear backoff for network issues
        const networkBackoff = delay * retries;
        await new Promise((resolve) => setTimeout(resolve, networkBackoff));
        continue;
      }

      // Not a retriable error, don't retry
      break;
    }
  }

  // Handle errors but don't show notifications (per user request)
  if (lastError && retries >= maxRetries) {
    if (isServiceUnavailableError(lastError)) {
      handleServiceUnavailableError();
    }
  }

  throw lastError || new Error('Operation failed after multiple retries');
}

