/**
 * Utility functions for converting between different content formats
 */

/**
 * Checks if a string is likely HTML content
 * @param content The content to check
 * @returns True if the content appears to be HTML
 */
export function isHtmlContent(content: string): boolean {
  if (!content) return false;
  const trimmed = content.trim();
  return trimmed.startsWith('<') && trimmed.includes('</');
}

/**
 * Checks if a string is likely JSON content
 * @param content The content to check
 * @returns True if the content appears to be JSON
 */
export function isJsonContent(content: string): boolean {
  if (!content) return false;
  const trimmed = content.trim();
  return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
         (trimmed.startsWith('[') && trimmed.endsWith(']'));
}

/**
 * Process markdown for dropdown content without causing recursion
 * @param content Markdown content to process
 * @returns Simple HTML converted from markdown
 */
function processDropdownContent(content: string): string {
  if (!content) return '';
  
  let html = content;
  
  // Headers
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
  
  // Bold
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Italic
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Links
  html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
  
  // Code blocks
  html = html.replace(/```([\s\S]*?)```/gm, '<pre><code>$1</code></pre>');
  
  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
  
  // Lists
  html = html.replace(/^\s*[\*\-]\s(.+)$/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>\n)+/g, '<ul>$&</ul>');
  
  // Paragraphs for remaining text
  html = html.split('\n\n').map(para => {
    // Skip if it's already a block element
    if (para.trim().startsWith('<') && 
        !para.trim().startsWith('<span') && 
        !para.trim().startsWith('<strong') && 
        !para.trim().startsWith('<em')) return para;
    return `<p>${para}</p>`;
  }).join('\n');
  
  return html;
}

/**
 * Converts markdown-like syntax to HTML
 * Enhanced version with support for code blocks, task lists, callouts, and more
 * @param content The markdown content to convert
 * @returns HTML content
 */
import { getCachedMarkdown } from './content-cache';

export function markdownToHtml(content: string): string {
  if (!content) return '';
  if (isHtmlContent(content)) return content;

  try {
    // Use cached processing for better performance
    const result = getCachedMarkdown(content, processMarkdownToHtml);
    return result;
  } catch (error) {
    console.error('Error in markdownToHtml:', error);
    // Fallback to direct processing if cache fails
    return processMarkdownToHtml(content);
  }
}

import { cleanEscapeCharacters } from './content-security';

function processMarkdownToHtml(content: string): string {
  if (!content) return '';

  try {
    // First, clean any escape characters that shouldn't be displayed literally
    let html = cleanEscapeCharacters(content);

  // Preserve code blocks and inline HTML - simplified approach
  const codeBlocks: Array<{ language: string; code: string }> = [];
  const inlineHtmlBlocks: string[] = [];

  // Extract code blocks first to prevent processing their contents
  html = html.replace(/```(\w*)\n?([\s\S]*?)```/gm, (match, language, code) => {
    const id = `__CODE_BLOCK_${codeBlocks.length}__`;
    codeBlocks.push({ language: language || '', code: code.trim() });
    return id;
  });

  // Extract inline HTML (like YouTube embeds)
  html = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>([\s\S]*?)<\/\1>/gi, (match) => {
    const id = `__HTML_BLOCK_${inlineHtmlBlocks.length}__`;
    inlineHtmlBlocks.push(match);
    return id;
  });

  // Headers with anchor links - simplified
  html = html.replace(/^#{6}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h6 id="${id}">${text}</h6>`;
  });
  html = html.replace(/^#{5}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h5 id="${id}">${text}</h5>`;
  });
  html = html.replace(/^#{4}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h4 id="${id}">${text}</h4>`;
  });
  html = html.replace(/^#{3}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h3 id="${id}">${text}</h3>`;
  });
  html = html.replace(/^#{2}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h2 id="${id}">${text}</h2>`;
  });
  html = html.replace(/^#{1}\s+(.*$)/gm, (match, text) => {
    const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h1 id="${id}">${text}</h1>`;
  });

  // Text formatting - order matters to avoid conflicts
  // Use non-greedy matching for better handling
  html = html.replace(/\*\*\*([^*]+)\*\*\*/g, '<strong><em>$1</em></strong>');
  html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*([^*\n]+)\*/g, '<em>$1</em>');
  html = html.replace(/~~([^~]+)~~/g, '<del>$1</del>');
  html = html.replace(/==([^=]+)==/g, '<mark>$1</mark>');

  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

  // Images - enhanced processing with better error handling
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
    // Clean up the src URL
    const cleanSrc = src.trim();
    const cleanAlt = (alt || '').trim();

    // Add comprehensive styling and attributes
    return `<img src="${cleanSrc}" alt="${cleanAlt}" loading="lazy" class="lesson-image max-w-full h-auto rounded-lg shadow-sm mx-auto my-4 block" style="display: block; margin: 1.5rem auto; max-width: 100%; height: auto;" />`;
  });

  // Blockquotes - simplified
  html = html.replace(/^>\s*(.+)$/gm, '<blockquote>$1</blockquote>');

  // Horizontal rule
  html = html.replace(/^---+$/gm, '<hr />');

  // Task lists - simplified
  html = html.replace(/^-\s*\[\s*\]\s*(.+)$/gm, '<div class="task-item"><input type="checkbox" disabled /> $1</div>');
  html = html.replace(/^-\s*\[x\]\s*(.+)$/gm, '<div class="task-item"><input type="checkbox" checked disabled /> $1</div>');

  // Lists - proper processing with distinction between ul and ol
  const lines = html.split('\n');
  const processedLines: string[] = [];
  let inUnorderedList = false;
  let inOrderedList = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Check for unordered list items (-, *, +)
    const unorderedMatch = trimmedLine.match(/^[-*+]\s+(.+)$/);
    // Check for ordered list items (1., 2., etc.)
    const orderedMatch = trimmedLine.match(/^\d+\.\s+(.+)$/);

    if (unorderedMatch) {
      // Close ordered list if we were in one
      if (inOrderedList) {
        processedLines.push('</ol>');
        inOrderedList = false;
      }
      // Open unordered list if not already in one
      if (!inUnorderedList) {
        processedLines.push('<ul>');
        inUnorderedList = true;
      }
      processedLines.push(`<li>${unorderedMatch[1]}</li>`);
    } else if (orderedMatch) {
      // Close unordered list if we were in one
      if (inUnorderedList) {
        processedLines.push('</ul>');
        inUnorderedList = false;
      }
      // Open ordered list if not already in one
      if (!inOrderedList) {
        processedLines.push('<ol>');
        inOrderedList = true;
      }
      processedLines.push(`<li>${orderedMatch[1]}</li>`);
    } else {
      // Close any open lists
      if (inUnorderedList) {
        processedLines.push('</ul>');
        inUnorderedList = false;
      }
      if (inOrderedList) {
        processedLines.push('</ol>');
        inOrderedList = false;
      }
      processedLines.push(line);
    }
  }

  // Close any remaining open lists
  if (inUnorderedList) {
    processedLines.push('</ul>');
  }
  if (inOrderedList) {
    processedLines.push('</ol>');
  }

  html = processedLines.join('\n');

  // Tables - simplified GitHub-style processing
  html = html.replace(/^\|(.+)\|\s*\n\|[-:\s|]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm, (match) => {
    const lines = match.trim().split('\n');
    if (lines.length < 3) return match;

    const headerCells = lines[0].split('|').slice(1, -1).map(cell => cell.trim());
    const dataRows = lines.slice(2).map(row =>
      row.split('|').slice(1, -1).map(cell => cell.trim())
    );

    let tableHtml = '<table class="w-full border-collapse"><thead><tr>';
    headerCells.forEach(cell => {
      tableHtml += `<th>${cell}</th>`;
    });
    tableHtml += '</tr></thead><tbody>';

    dataRows.forEach(row => {
      tableHtml += '<tr>';
      row.forEach(cell => {
        tableHtml += `<td>${cell}</td>`;
      });
      tableHtml += '</tr>';
    });

    tableHtml += '</tbody></table>';
    return tableHtml;
  });

  // Inline code - clean processing without backticks
  html = html.replace(/`([^`\n]+)`/g, '<code>$1</code>');

  // Restore code blocks with clean syntax highlighting
  codeBlocks.forEach((blockData, index) => {
    const { language, code } = blockData;

    // Escape HTML entities in code
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');

    const highlightedCode = `<pre class="language-${language}"><code class="language-${language}">${escapedCode}</code></pre>`;
    html = html.replace(`__CODE_BLOCK_${index}__`, highlightedCode);
  });

  // Restore inline HTML blocks
  inlineHtmlBlocks.forEach((block, index) => {
    html = html.replace(`__HTML_BLOCK_${index}__`, block);
  });

  // Convert paragraphs - simplified approach
  html = html.split('\n\n').map(para => {
    para = para.trim();
    if (!para) return '';
    if (para.startsWith('<')) return para; // Already HTML
    return `<p>${para.replace(/\n/g, '<br>')}</p>`;
  }).filter(Boolean).join('\n');

    // Clean up any empty paragraphs or malformed HTML
    html = html.replace(/<p>\s*<\/p>/g, '');
    html = html.replace(/<p>(<[^>]+>.*<\/[^>]+>)<\/p>/g, '$1');

    return html;
  } catch (error) {
    // Return the original content if processing fails
    return `<p>${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</p>`;
  }
}

/**
 * Extracts HTML content from a JSON string if possible
 * @param content The JSON string
 * @returns The HTML content or null if not found
 */
export function extractHtmlFromJson(content: string): string | null {
  try {
    const parsed = JSON.parse(content);
    if (parsed.html) {
      return parsed.html;
    }
    return null;
  } catch (e) {
    return null;
  }
}

/**
 * Normalizes content to HTML format
 * @param content The content to normalize
 * @returns HTML content
 */
export function normalizeToHtml(content: string): string {
  if (!content) return '';

  // Check if it's already HTML
  if (isHtmlContent(content)) {
    return content;
  }

  // Check if it's JSON with HTML
  if (isJsonContent(content)) {
    const htmlFromJson = extractHtmlFromJson(content);
    if (htmlFromJson) {
      return htmlFromJson;
    }
  }

  // Convert from markdown-like syntax
  return markdownToHtml(content);
}
