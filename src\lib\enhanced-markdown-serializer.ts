/**
 * Enhanced TipTap to Markdown serializer with full GFM support
 * Supports tables, task lists, strikethrough, highlights, and more
 */

import { Node } from '@tiptap/pm/model';

export interface EnhancedSerializerOptions {
  tightLists?: boolean;
  bulletListMarker?: string;
  orderedListMarker?: string;
  codeBlockLanguagePrefix?: string;
}

export class EnhancedMarkdownSerializer {
  private options: EnhancedSerializerOptions;

  constructor(options: EnhancedSerializerOptions = {}) {
    this.options = {
      tightLists: false,
      bulletListMarker: '-',
      orderedListMarker: '.',
      codeBlockLanguagePrefix: '',
      ...options,
    };
  }

  serialize(doc: Node): string {
    return this.serializeNode(doc).trim();
  }

  private serializeNode(node: Node): string {
    // Debug logging to see what node types we're processing (only in development)
    if (typeof window !== 'undefined' && window.console && import.meta.env.DEV && node.type.name !== 'text' && node.type.name !== 'doc') {
      console.log('Processing node type:', node.type.name, 'attrs:', node.attrs);
    }

    const serializer = this.getNodeSerializer(node.type.name);
    if (serializer) {
      return serializer(node);
    }

    // Special fallback for YouTube videos that might not match expected node names
    if (node.attrs && node.attrs.src && node.attrs.src.includes('youtube.com')) {
      return this.serializeYoutube(node);
    }

    // Fallback: serialize children
    return this.serializeChildren(node);
  }

  private getNodeSerializer(nodeType: string): ((node: Node) => string) | null {
    const serializers: Record<string, (node: Node) => string> = {
      doc: (node) => this.serializeChildren(node),
      paragraph: (node) => this.serializeParagraph(node),
      heading: (node) => this.serializeHeading(node),
      text: (node) => this.serializeText(node),
      hardBreak: () => '\\\n',
      horizontalRule: () => '\n---\n',
      
      // Lists
      bulletList: (node) => this.serializeBulletList(node),
      orderedList: (node) => this.serializeOrderedList(node),
      listItem: (node) => this.serializeListItem(node),
      
      // Task lists (GFM extension)
      taskList: (node) => this.serializeTaskList(node),
      taskItem: (node) => this.serializeTaskItem(node),
      
      // Formatting
      strong: (node) => `**${this.serializeChildren(node)}**`,
      em: (node) => `*${this.serializeChildren(node)}*`,
      code: (node) => `\`${node.textContent}\``,
      strike: (node) => `~~${this.serializeChildren(node)}~~`,
      underline: (node) => `<u>${this.serializeChildren(node)}</u>`,
      highlight: (node) => `==${this.serializeChildren(node)}==`,
      
      // Links and images
      link: (node) => this.serializeLink(node),
      image: (node) => this.serializeImage(node),
      
      // Code blocks
      codeBlock: (node) => this.serializeCodeBlock(node),
      
      // Blockquotes
      blockquote: (node) => this.serializeBlockquote(node),
      
      // Tables (GFM extension)
      table: (node) => this.serializeTable(node),
      tableRow: (node) => this.serializeTableRow(node),
      tableCell: (node) => this.serializeTableCell(node),
      tableHeader: (node) => this.serializeTableHeader(node),

      // YouTube videos (try different possible node names)
      youtube: (node) => this.serializeYoutube(node),
      youtubeVideo: (node) => this.serializeYoutube(node),
      'youtube-video': (node) => this.serializeYoutube(node),
    };

    return serializers[nodeType] || null;
  }

  private serializeChildren(node: Node): string {
    let result = '';
    node.forEach((child) => {
      result += this.serializeNode(child);
    });
    return result;
  }

  private serializeParagraph(node: Node): string {
    const content = this.serializeChildren(node);
    if (!content.trim()) return '';
    
    // Check if this paragraph is inside a list item
    const isInListItem = this.isNodeInListItem(node);
    if (isInListItem) {
      return content;
    }
    
    return content + '\n\n';
  }

  private serializeHeading(node: Node): string {
    const level = node.attrs.level || 1;
    const content = this.serializeChildren(node);
    const hashes = '#'.repeat(level);
    return `${hashes} ${content}\n\n`;
  }

  private serializeText(node: Node): string {
    let text = node.text || '';
    
    // Escape special markdown characters
    text = text.replace(/([\\`*_{}[\]()#+\-.!])/g, '\\$1');
    
    return text;
  }

  private serializeBulletList(node: Node): string {
    const items = this.serializeListItems(node, this.options.bulletListMarker);
    return items + '\n';
  }

  private serializeOrderedList(node: Node): string {
    const items = this.serializeListItems(node, this.options.orderedListMarker, true);
    return items + '\n';
  }

  private serializeListItems(node: Node, marker: string, ordered = false): string {
    let result = '';
    let index = 1;
    
    node.forEach((child) => {
      if (child.type.name === 'listItem') {
        const prefix = ordered ? `${index}${marker} ` : `${marker} `;
        const content = this.serializeListItem(child, prefix);
        result += content;
        index++;
      }
    });
    
    return result;
  }

  private serializeListItem(node: Node, prefix = '- '): string {
    let content = '';
    let isFirst = true;
    
    node.forEach((child) => {
      const childContent = this.serializeNode(child);
      if (isFirst) {
        content += prefix + childContent.replace(/^\n+/, '');
        isFirst = false;
      } else {
        // Indent nested content
        const indentedContent = childContent.replace(/^/gm, '  ');
        content += indentedContent;
      }
    });
    
    return content + '\n';
  }

  private serializeTaskList(node: Node): string {
    let result = '';
    node.forEach((child) => {
      if (child.type.name === 'taskItem') {
        result += this.serializeTaskItem(child);
      }
    });
    return result + '\n';
  }

  private serializeTaskItem(node: Node): string {
    const checked = node.attrs.checked || false;
    const checkbox = checked ? '[x]' : '[ ]';
    const content = this.serializeChildren(node);
    return `- ${checkbox} ${content}\n`;
  }

  private serializeLink(node: Node): string {
    const href = node.attrs.href || '';
    const title = node.attrs.title;
    const content = this.serializeChildren(node);
    
    if (title) {
      return `[${content}](${href} "${title}")`;
    }
    return `[${content}](${href})`;
  }

  private serializeImage(node: Node): string {
    const src = node.attrs.src || '';
    const alt = node.attrs.alt || '';
    const title = node.attrs.title;
    
    if (title) {
      return `![${alt}](${src} "${title}")`;
    }
    return `![${alt}](${src})`;
  }

  private serializeCodeBlock(node: Node): string {
    const language = node.attrs.language || '';
    const content = node.textContent || '';
    const prefix = this.options.codeBlockLanguagePrefix + language;
    return `\`\`\`${prefix}\n${content}\n\`\`\`\n\n`;
  }

  private serializeBlockquote(node: Node): string {
    const content = this.serializeChildren(node);
    const lines = content.split('\n');
    const quotedLines = lines.map(line => line.trim() ? `> ${line}` : '>');
    return quotedLines.join('\n') + '\n\n';
  }

  private serializeTable(node: Node): string {
    let result = '';
    let headerRow = '';
    let separatorRow = '';
    let bodyRows = '';
    let isFirstRow = true;
    
    node.forEach((child) => {
      if (child.type.name === 'tableRow') {
        const rowContent = this.serializeTableRow(child);
        
        if (isFirstRow) {
          headerRow = rowContent;
          // Create separator row based on number of columns
          const columnCount = this.getTableColumnCount(child);
          separatorRow = '|' + ' --- |'.repeat(columnCount) + '\n';
          isFirstRow = false;
        } else {
          bodyRows += rowContent;
        }
      }
    });
    
    result = headerRow + separatorRow + bodyRows + '\n';
    return result;
  }

  private serializeTableRow(node: Node): string {
    let result = '|';
    
    node.forEach((child) => {
      if (child.type.name === 'tableCell' || child.type.name === 'tableHeader') {
        const cellContent = this.serializeTableCell(child);
        result += ` ${cellContent} |`;
      }
    });
    
    return result + '\n';
  }

  private serializeTableCell(node: Node): string {
    const content = this.serializeChildren(node).trim();
    // Escape pipe characters in cell content
    return content.replace(/\|/g, '\\|').replace(/\n/g, ' ');
  }

  private serializeTableHeader(node: Node): string {
    return this.serializeTableCell(node);
  }

  private serializeYoutube(node: Node): string {
    const src = node.attrs.src || '';
    const width = node.attrs.width || 560;
    const height = node.attrs.height || 315;

    // Extract YouTube video ID from the src URL
    const videoId = this.extractYouTubeId(src);

    if (!videoId) {
      return ''; // Invalid YouTube URL
    }

    // Always use the proper YouTube embed URL format
    const embedUrl = `https://www.youtube.com/embed/${videoId}`;

    // Return the iframe HTML directly since markdown doesn't have native video support
    // This will be preserved when converting back to HTML
    return `<div data-youtube-video>\n  <iframe src="${embedUrl}" width="${width}" height="${height}" frameborder="0" allowfullscreen></iframe>\n</div>\n\n`;
  }

  private extractYouTubeId(url: string): string {
    if (!url) return '';

    // Handle various YouTube URL formats
    const patterns = [
      /(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/,
      /youtube\.com\/watch\?v=([^&]+)/,
      /youtu\.be\/([^?&]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return '';
  }

  private getTableColumnCount(row: Node): number {
    let count = 0;
    row.forEach((child) => {
      if (child.type.name === 'tableCell' || child.type.name === 'tableHeader') {
        count++;
      }
    });
    return count;
  }

  private isNodeInListItem(node: Node): boolean {
    // This is a simplified check - in a real implementation,
    // you'd traverse up the node tree to check for list item ancestors
    return false;
  }
}

/**
 * Converts TipTap editor content to enhanced Markdown with full GFM support
 */
export function tiptapToMarkdown(doc: Node, options?: EnhancedSerializerOptions): string {
  const serializer = new EnhancedMarkdownSerializer(options);
  return serializer.serialize(doc);
}

/**
 * Enhanced HTML to Markdown converter for fallback scenarios
 */
export function htmlToMarkdown(html: string): string {
  if (!html) return '';
  
  let markdown = html;
  
  // Headers
  markdown = markdown.replace(/<h([1-6])>(.*?)<\/h[1-6]>/gi, (match, level, content) => {
    return '#'.repeat(parseInt(level)) + ' ' + content + '\n\n';
  });
  
  // Bold and italic
  markdown = markdown.replace(/<strong>(.*?)<\/strong>/gi, '**$1**');
  markdown = markdown.replace(/<b>(.*?)<\/b>/gi, '**$1**');
  markdown = markdown.replace(/<em>(.*?)<\/em>/gi, '*$1*');
  markdown = markdown.replace(/<i>(.*?)<\/i>/gi, '*$1*');
  
  // Strikethrough
  markdown = markdown.replace(/<del>(.*?)<\/del>/gi, '~~$1~~');
  markdown = markdown.replace(/<s>(.*?)<\/s>/gi, '~~$1~~');
  
  // Underline (not standard markdown, use HTML)
  markdown = markdown.replace(/<u>(.*?)<\/u>/gi, '<u>$1</u>');
  
  // Code
  markdown = markdown.replace(/<code>(.*?)<\/code>/gi, '`$1`');
  
  // Links
  markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)');
  
  // Images
  markdown = markdown.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)');
  markdown = markdown.replace(/<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*>/gi, '![$1]($2)');
  
  // Lists
  markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/gis, (match, content) => {
    return content.replace(/<li[^>]*>(.*?)<\/li>/gis, '- $1\n') + '\n';
  });
  
  markdown = markdown.replace(/<ol[^>]*>(.*?)<\/ol>/gis, (match, content) => {
    let index = 1;
    return content.replace(/<li[^>]*>(.*?)<\/li>/gis, () => `${index++}. $1\n`) + '\n';
  });
  
  // Blockquotes
  markdown = markdown.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gis, (match, content) => {
    return content.split('\n').map(line => line.trim() ? `> ${line}` : '>').join('\n') + '\n\n';
  });
  
  // Paragraphs
  markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gis, '$1\n\n');
  
  // Line breaks
  markdown = markdown.replace(/<br[^>]*>/gi, '\n');

  // Preserve YouTube iframes (don't remove them)
  // This regex preserves the entire YouTube iframe structure
  const youtubeIframes: string[] = [];
  markdown = markdown.replace(/<div[^>]*data-youtube-video[^>]*>[\s\S]*?<\/div>/gi, (match) => {
    const placeholder = `__YOUTUBE_IFRAME_${youtubeIframes.length}__`;
    youtubeIframes.push(match);
    return placeholder;
  });

  // Remove remaining HTML tags (but not YouTube iframes)
  markdown = markdown.replace(/<[^>]*>/g, '');

  // Restore YouTube iframes
  youtubeIframes.forEach((iframe, index) => {
    markdown = markdown.replace(`__YOUTUBE_IFRAME_${index}__`, iframe);
  });

  // Clean up extra whitespace
  markdown = markdown.replace(/\n{3,}/g, '\n\n');
  
  return markdown.trim();
}
