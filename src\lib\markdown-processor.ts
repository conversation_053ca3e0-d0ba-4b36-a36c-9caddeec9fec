import Prism from 'prismjs';

// Helper for safely checking window existence
const isClient = typeof window !== 'undefined';

/**
 * Request processing when the browser is idle
 */
export const requestIdleProcessing = (callback: () => void): number => {
  if (!isClient) return 0;
  
  // Cast window to any to avoid type checking
  const win = window as any;
  
  if ('requestIdleCallback' in win) {
    return win.requestIdleCallback(callback, { timeout: 2000 }) as number;
  } else {
    return win.setTimeout(callback, 50);
  }
};

/**
 * Cancel an idle processing request
 */
export const cancelIdleProcessing = (id: number): void => {
  if (!isClient) return;
  
  // Cast window to any to avoid type checking
  const win = window as any;
  
  if ('cancelIdleCallback' in win) {
    win.cancelIdleCallback(id);
  } else {
    win.clearTimeout(id);
  }
};

/**
 * Process markdown content in phases to prevent UI freezing
 */
export const processMarkdownContent = (
  rootElement: HTMLElement | null,
  callbacks: {
    onHighlightCode?: (codeElement: Element) => void;
    onEnhanceTables?: (tableElement: HTMLTableElement) => void;
    onEnhanceLinks?: (linkElement: HTMLAnchorElement) => void;
    onComplete?: () => void;
  }
): (() => void) => {
  if (!rootElement) return () => {};
  
  const { onHighlightCode, onEnhanceTables, onEnhanceLinks, onComplete } = callbacks;
  
  // Start the processing pipeline
  const idleCallbackId = requestIdleProcessing(() => {
    if (!rootElement) return;
    
    // Process in phases
    const processInPhases = () => {
      // Phase 1: Highlight code blocks progressively
      const phase1 = () => {
        if (!rootElement) return;
        
        const codeBlocks = rootElement.querySelectorAll('pre code');
        if (codeBlocks.length > 0) {
          let blockIndex = 0;
          
          const processNextBlocks = () => {
            const batchSize = 3; // Process 3 blocks at a time
            const endIndex = Math.min(blockIndex + batchSize, codeBlocks.length);
            
            // Process a batch of code blocks
            for (let i = blockIndex; i < endIndex; i++) {
              try {
                if (onHighlightCode) {
                  onHighlightCode(codeBlocks[i]);
                } else {
                  Prism.highlightElement(codeBlocks[i]);
                }
              } catch (e) {
                console.error('Error highlighting code block:', e);
              }
            }
            
            blockIndex = endIndex;
            
            // If there are more blocks to process, schedule next batch
            if (blockIndex < codeBlocks.length) {
              window.setTimeout(processNextBlocks, 10);
            } else {
              // Move to next phase after short delay
              window.setTimeout(phase2, 10);
            }
          };
          
          processNextBlocks();
        } else {
          // No code blocks, move to next phase
          phase2();
        }
      };
      
      // Phase 2: Enhance tables
      const phase2 = () => {
        if (!rootElement || !onEnhanceTables) {
          phase3();
          return;
        }
        
        const tables = rootElement.querySelectorAll('table:not(.enhanced-table)');
        if (tables.length > 0) {
          // Process all tables (usually there aren't many)
          tables.forEach((table) => {
            if (table instanceof HTMLTableElement) {
              onEnhanceTables(table);
            }
          });
        }
        
        // Move to next phase
        phase3();
      };
      
      // Phase 3: Enhance links 
      const phase3 = () => {
        if (!rootElement || !onEnhanceLinks) {
          phase4();
          return;
        }
        
        const links = rootElement.querySelectorAll('a:not(.enhanced-link)');
        if (links.length > 0) {
          let linkIndex = 0;
          
          const processNextLinks = () => {
            const batchSize = 10; // Process 10 links at a time
            const endIndex = Math.min(linkIndex + batchSize, links.length);
            
            // Process a batch of links
            for (let i = linkIndex; i < endIndex; i++) {
              if (links[i] instanceof HTMLAnchorElement) {
                onEnhanceLinks(links[i] as HTMLAnchorElement);
              }
            }
            
            linkIndex = endIndex;
            
            // If there are more links to process, schedule next batch
            if (linkIndex < links.length) {
              window.setTimeout(processNextLinks, 0);
            } else {
              // Move to next phase
              phase4();
            }
          };
          
          processNextLinks();
        } else {
          // No links, move to next phase
          phase4();
        }
      };
      
      // Phase 4: Finalize processing
      const phase4 = () => {
        if (onComplete) {
          onComplete();
        }
      };
      
      // Start the pipeline
      if (isClient) {
        window.requestAnimationFrame(() => phase1());
      } else {
        phase1();
      }
    };
    
    // Start the multi-phase processing
    processInPhases();
  });
  
  // Return cleanup function
  return () => cancelIdleProcessing(idleCallbackId);
}; 