
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export type UserRole = 'student' | 'teacher';

export async function assignRole(userId: string, role: UserRole): Promise<boolean> {
  try {
    console.log(`Attempting to assign role '${role}' to user '${userId}'`);

    // First, check if the user already has a role
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing role:', checkError);
      // Continue with insert anyway
    }

    if (existingRole) {
      console.log('User already has a role, updating instead');
      // If role exists, update it instead
      return await changeRole(userId, role);
    }

    // Otherwise, insert a new role
    console.log('Inserting new role record');
    const { error } = await supabase
      .from('user_roles')
      .insert([
        { user_id: userId, role }
      ]);

    if (error) {
      console.error('Error assigning role:', error);
      toast.error(`Failed to assign role: ${error.message}`);
      return false;
    }

    console.log('Role assigned successfully');
    toast.success(`Role ${role} assigned successfully`);
    return true;
  } catch (error: any) {
    console.error('Error in assignRole:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

export async function changeRole(userId: string, role: UserRole): Promise<boolean> {
  try {
    console.log(`Attempting to change role to '${role}' for user '${userId}'`);

    // Check if the user already has a role record
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing role:', checkError);
    }

    if (!existingRole) {
      console.log('No existing role found, assigning instead');
      // If no role exists, use the assignRole function instead
      return await assignRole(userId, role);
    }

    // Otherwise update the existing role
    console.log('Updating existing role');
    const { error } = await supabase
      .from('user_roles')
      .update({ role })
      .eq('user_id', userId);

    if (error) {
      console.error('Error changing role:', error);
      toast.error(`Failed to change role: ${error.message}`);
      return false;
    }

    console.log('Role changed successfully');
    toast.success(`Role changed to ${role} successfully`);
    return true;
  } catch (error: any) {
    console.error('Error in changeRole:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

/**
 * @deprecated This function is deprecated and should not be used.
 * Only teachers should be able to assign teacher roles through the admin interface.
 */
export async function becomeTeacher(userId: string): Promise<boolean> {
  console.log('Attempting direct teacher role assignment for user:', userId);

  try {
    if (!userId) {
      console.error('No user ID provided to becomeTeacher');
      toast.error('User ID is required');
      return false;
    }

    // Now using the user_id constraint we added to the database
    const { error } = await supabase
      .from('user_roles')
      .upsert(
        { user_id: userId, role: 'teacher' },
        { onConflict: 'user_id' }
      );

    if (error) {
      console.error('Error in becomeTeacher:', error);
      toast.error(`Failed to become a teacher: ${error.message}`);
      return false;
    }

    console.log('Teacher role successfully assigned');
    return true;
  } catch (error: any) {
    console.error('Unexpected error in becomeTeacher:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

export async function getUsersWithRoles() {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        first_name,
        last_name,
        user_roles(role)
      `);

    if (error) {
      console.error('Error fetching users with roles:', error);
      toast.error(`Failed to fetch users: ${error.message}`);
      return [];
    }

    return data || [];
  } catch (error: any) {
    console.error('Error:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return [];
  }
}
