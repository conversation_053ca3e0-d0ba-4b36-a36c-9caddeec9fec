/**
 * Simple image processing utility
 * This provides a minimal approach to image handling with fixed dimensions
 */

/**
 * Process an image with fixed dimensions and quality
 * This creates a small, reliable image that can be stored in the database
 *
 * @param file The image file to process
 * @returns A Promise that resolves to a small data URL
 */
/**
 * Debug mode - disabled in production
 */
const DEBUG = import.meta.env.DEV || false;

/**
 * Create a small image from a file
 * This function has been completely rewritten for maximum reliability
 */
export async function createSmallImage(file: File): Promise<string> {
  if (DEBUG) console.log('Starting image processing for file:', file.name, `(${Math.round(file.size / 1024)}KB)`);

  return new Promise((resolve, reject) => {
    try {
      // Use FileReader to convert the file to a data URL directly
      // This is the most reliable approach
      const reader = new FileReader();

      reader.onload = function(event) {
        try {
          if (!event.target?.result) {
            reject(new Error('Failed to read file'));
            return;
          }

          const originalDataUrl = event.target.result as string;
          if (DEBUG) console.log('File converted to data URL successfully');

          // Create an image element to load the data URL
          const img = new Image();

          // Set up image load handler
          img.onload = function() {
            try {
              if (DEBUG) console.log('Image loaded successfully, dimensions:', img.width, 'x', img.height);

              // Create a canvas for resizing
              const canvas = document.createElement('canvas');
              canvas.width = 400;  // Slightly larger dimensions for better quality
              canvas.height = 300;

              // Get the canvas context
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                reject(new Error('Failed to get canvas context'));
                return;
              }

              // Fill with white background (for transparent images)
              ctx.fillStyle = '#FFFFFF';
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // Draw the image to the canvas
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

              // Convert the canvas to a data URL with medium quality
              const dataUrl = canvas.toDataURL('image/jpeg', 0.7);

              // Log the size for debugging
              if (DEBUG) {
                console.log(`Processed image size: ${Math.round(dataUrl.length / 1024)}KB`);
                console.log('Data URL starts with:', dataUrl.substring(0, 50) + '...');
              }

              // Return the data URL
              resolve(dataUrl);
            } catch (canvasError) {
              console.error('Error processing image in canvas:', canvasError);

              // Fallback: just use the original data URL
              if (DEBUG) console.log('Using original data URL as fallback');
              resolve(originalDataUrl);
            }
          };

          // Handle image load errors
          img.onerror = function(imgError) {
            console.error('Failed to load image:', imgError);

            // Fallback: just use the original data URL
            if (DEBUG) console.log('Using original data URL as fallback due to load error');
            resolve(originalDataUrl);
          };

          // Set the image source to the data URL
          img.src = originalDataUrl;
        } catch (processError) {
          console.error('Error in reader.onload:', processError);
          reject(processError);
        }
      };

      // Handle reader errors
      reader.onerror = function(readerError) {
        console.error('Error reading file:', readerError);
        reject(new Error('Failed to read file'));
      };

      // Read the file as a data URL
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Unexpected error in createSmallImage:', error);
      reject(error);
    }
  });
}
