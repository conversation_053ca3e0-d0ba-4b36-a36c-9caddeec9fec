/**
 * Custom TipTap extension for HTML details/summary elements (collapsible sections)
 * Provides GitHub Flavored Markdown support for collapsible content
 */

import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import React from 'react';
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react';
import { ChevronDown, ChevronRight } from 'lucide-react';

// React component for rendering the details element
const DetailsComponent = ({ node, updateAttributes, deleteNode }: any) => {
  const { open, summary } = node.attrs;

  return (
    <NodeViewWrapper className="details-wrapper">
      <details 
        open={open}
        className="border border-border rounded-lg p-4 my-4 bg-muted/30"
        onToggle={(e) => {
          updateAttributes({ open: (e.target as HTMLDetailsElement).open });
        }}
      >
        <summary 
          className="cursor-pointer flex items-center gap-2 font-medium text-foreground hover:text-primary transition-colors"
          contentEditable={false}
        >
          {open ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
          <span
            contentEditable
            suppressContentEditableWarning
            onBlur={(e) => {
              updateAttributes({ summary: e.target.textContent });
            }}
          >
            {summary || 'Click to expand'}
          </span>
        </summary>
        <div className="mt-3 pl-6">
          <NodeViewContent />
        </div>
      </details>
    </NodeViewWrapper>
  );
};

export interface DetailsOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    details: {
      /**
       * Insert a details/summary block
       */
      setDetails: (attributes?: { summary?: string; open?: boolean }) => ReturnType;
      /**
       * Toggle a details block
       */
      toggleDetails: () => ReturnType;
      /**
       * Unset details formatting
       */
      unsetDetails: () => ReturnType;
    };
  }
}

export const Details = Node.create<DetailsOptions>({
  name: 'details',

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  group: 'block',

  content: 'block+',

  defining: true,

  addAttributes() {
    return {
      summary: {
        default: 'Click to expand',
        parseHTML: (element) => {
          const summary = element.querySelector('summary');
          return summary?.textContent || 'Click to expand';
        },
        renderHTML: (attributes) => {
          return {};
        },
      },
      open: {
        default: false,
        parseHTML: (element) => element.hasAttribute('open'),
        renderHTML: (attributes) => {
          if (!attributes.open) {
            return {};
          }
          return { open: '' };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'details',
      },
    ];
  },

  renderHTML({ HTMLAttributes, node }) {
    const { summary, open } = node.attrs;
    
    return [
      'details',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        ...(open && { open: '' }),
      }),
      [
        'summary',
        {},
        summary || 'Click to expand',
      ],
      ['div', {}, 0],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(DetailsComponent);
  },

  addCommands() {
    return {
      setDetails:
        (attributes = {}) =>
        ({ commands }) => {
          return commands.wrapIn(this.name, attributes);
        },
      toggleDetails:
        () =>
        ({ commands }) => {
          return commands.toggleWrap(this.name);
        },
      unsetDetails:
        () =>
        ({ commands }) => {
          return commands.lift(this.name);
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-d': () => this.editor.commands.toggleDetails(),
    };
  },
});

export default Details;
