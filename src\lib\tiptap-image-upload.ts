/**
 * TipTap editor image upload utilities
 * Provides functions for uploading, validating, and previewing images in TipTap editors
 */

import { supabase } from '@/lib/supabase';

export interface ImageValidationResult {
  valid: boolean;
  message?: string;
}

export interface UploadOptions {
  courseId?: string;
  moduleId?: string;
  onProgress?: (progress: number) => void;
}

/**
 * Validates an image file for TipTap editor use
 * @param file The file to validate
 * @returns Validation result with status and optional error message
 */
export function validateImageFile(file: File): ImageValidationResult {
  // Check if file exists
  if (!file) {
    return { valid: false, message: 'No file provided' };
  }
  
  // Validate file type (only accept images)
  if (!file.type.startsWith('image/')) {
    return { valid: false, message: 'Please upload an image file' };
  }
  
  // Validate specific image formats
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!validTypes.includes(file.type)) {
    return { valid: false, message: 'Please upload a JPEG, PNG, WebP, or GIF image' };
  }
  
  // Check file size (max 5MB for editor images)
  const MAX_SIZE = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > MAX_SIZE) {
    return { 
      valid: false, 
      message: `Image is too large. Maximum size is ${MAX_SIZE / (1024 * 1024)}MB` 
    };
  }
  
  return { valid: true };
}

/**
 * Creates a preview data URL for an image file
 * @param file The image file
 * @returns A promise that resolves to a data URL for the image preview
 */
export async function createImagePreview(file: File): Promise<string> {
  if (!file) {
    throw new Error('File is required');
  }

  if (!file.type.startsWith('image/')) {
    throw new Error('Please select an image file');
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === 'string') {
        resolve(result);
      } else {
        reject(new Error('Failed to create image preview'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read image file'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * Uploads an image file to Supabase storage for use in TipTap editor
 * @param file The image file to upload
 * @param options Upload options including courseId, moduleId, and progress callback
 * @returns A promise that resolves to the public URL of the uploaded image
 */
export async function uploadEditorImage(
  file: File, 
  options: UploadOptions = {}
): Promise<string> {
  const { courseId, moduleId, onProgress } = options;

  try {
    // Validate the image first
    const validation = validateImageFile(file);
    if (!validation.valid) {
      throw new Error(validation.message || 'Invalid image');
    }

    // Create a structured path for better organization
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    
    let fileName: string;
    if (courseId && moduleId) {
      fileName = `editor-images/courses/${courseId}/modules/${moduleId}/${timestamp}.${fileExtension}`;
    } else if (courseId) {
      fileName = `editor-images/courses/${courseId}/${timestamp}.${fileExtension}`;
    } else {
      fileName = `editor-images/general/${timestamp}.${fileExtension}`;
    }
    
    // Upload to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('course-images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Error uploading image:', uploadError);
      throw new Error(`Failed to upload image: ${uploadError.message}`);
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('course-images')
      .getPublicUrl(fileName);

    if (!publicUrl) {
      throw new Error('Failed to get public URL for uploaded image');
    }

    // Call progress callback if provided
    if (onProgress) {
      onProgress(100);
    }

    return publicUrl;
  } catch (error) {
    console.error('Error in uploadEditorImage:', error);
    throw error;
  }
}

/**
 * Uploads an image with retry logic for better reliability
 * @param file The file to upload
 * @param path The storage path
 * @param maxRetries Maximum number of retry attempts
 * @returns The public URL of the uploaded file
 */
export async function uploadWithRetry(
  file: File, 
  path: string, 
  maxRetries = 3
): Promise<string> {
  let retries = 0;
  let lastError: Error;

  while (retries < maxRetries) {
    try {
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('course-images')
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('course-images')
        .getPublicUrl(path);

      if (!publicUrl) {
        throw new Error('Failed to get public URL');
      }

      return publicUrl;
    } catch (error) {
      lastError = error as Error;
      retries++;
      
      if (retries < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retries) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError!;
}
