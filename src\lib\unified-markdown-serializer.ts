/**
 * Unified Markdown Serializer for TipTap
 * Comprehensive GitHub Flavored Markdown support with proper serialization
 */

import { Node } from '@tiptap/pm/model';
import TurndownService from 'turndown';
import { gfm } from 'turndown-plugin-gfm';

export interface UnifiedSerializerOptions {
  tightLists?: boolean;
  preserveWhitespace?: boolean;
  gfmTables?: boolean;
  taskLists?: boolean;
  details?: boolean;
  callouts?: boolean;
  strikethrough?: boolean;
  codeBlocks?: boolean;
}

export class UnifiedMarkdownSerializer {
  private options: UnifiedSerializerOptions;
  private turndownService: TurndownService;

  constructor(options: UnifiedSerializerOptions = {}) {
    this.options = {
      tightLists: false,
      preserveWhitespace: false,
      gfmTables: true,
      taskLists: true,
      details: true,
      callouts: true,
      strikethrough: true,
      codeBlocks: true,
      ...options,
    };

    // Initialize Turndown with optimal settings
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      hr: '---',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
      preformattedCode: false,
    });

    // Add GFM plugin for tables, strikethrough, etc.
    this.turndownService.use(gfm);

    // Add custom rules
    this.addCustomRules();
  }

  private addCustomRules() {
    // Task Lists
    if (this.options.taskLists) {
      this.turndownService.addRule('taskList', {
        filter: (node) => {
          return node.nodeName === 'UL' && 
                 node.classList.contains('task-list');
        },
        replacement: (content) => content
      });

      this.turndownService.addRule('taskItem', {
        filter: (node) => {
          return node.nodeName === 'LI' && 
                 node.classList.contains('task-item');
        },
        replacement: (content, node) => {
          const checkbox = node.querySelector('input[type="checkbox"]');
          const checked = checkbox?.checked ? 'x' : ' ';
          const cleanContent = content.replace(/^\s*\[[ x]\]\s*/, '').trim();
          return `- [${checked}] ${cleanContent}\n`;
        }
      });
    }

    // Details/Summary (collapsible sections)
    if (this.options.details) {
      this.turndownService.addRule('details', {
        filter: 'details',
        replacement: (content, node) => {
          const summary = node.querySelector('summary');
          const summaryText = summary?.textContent?.trim() || 'Details';
          const detailsContent = content.replace(summaryText, '').trim();
          return `<details>\n<summary>${summaryText}</summary>\n\n${detailsContent}\n</details>\n\n`;
        }
      });
    }

    // Callouts/Admonitions
    if (this.options.callouts) {
      this.turndownService.addRule('callout', {
        filter: (node) => {
          return node.nodeName === 'DIV' && 
                 node.hasAttribute('data-callout');
        },
        replacement: (content, node) => {
          const type = node.getAttribute('data-type') || 'info';
          const title = node.getAttribute('data-title') || type.charAt(0).toUpperCase() + type.slice(1);
          
          // Format as GitHub-style callout
          const lines = content.trim().split('\n');
          const quotedLines = lines.map(line => `> ${line}`).join('\n');
          return `> [!${type.toUpperCase()}] ${title}\n${quotedLines}\n\n`;
        }
      });
    }

    // Strikethrough (if not handled by GFM plugin)
    if (this.options.strikethrough) {
      this.turndownService.addRule('strikethrough', {
        filter: ['del', 's', 'strike'],
        replacement: (content) => `~~${content}~~`
      });
    }

    // Code blocks with language detection
    if (this.options.codeBlocks) {
      this.turndownService.addRule('codeBlock', {
        filter: (node) => {
          return node.nodeName === 'PRE' && 
                 node.querySelector('code');
        },
        replacement: (content, node) => {
          const codeElement = node.querySelector('code');
          const language = this.extractLanguage(codeElement);
          const code = codeElement?.textContent || content;
          return `\n\`\`\`${language}\n${code}\n\`\`\`\n\n`;
        }
      });
    }

    // Highlight/Mark elements
    this.turndownService.addRule('highlight', {
      filter: ['mark', 'span.highlight'],
      replacement: (content) => `==${content}==`
    });

    // Underline elements
    this.turndownService.addRule('underline', {
      filter: 'u',
      replacement: (content) => `<u>${content}</u>`
    });

    // YouTube embeds
    this.turndownService.addRule('youtube', {
      filter: (node) => {
        return node.nodeName === 'DIV' && 
               node.classList.contains('youtube-embed');
      },
      replacement: (content, node) => {
        const iframe = node.querySelector('iframe');
        const src = iframe?.getAttribute('src') || '';
        const youtubeId = this.extractYouTubeId(src);
        return youtubeId ? `[![YouTube](https://img.youtube.com/vi/${youtubeId}/0.jpg)](https://www.youtube.com/watch?v=${youtubeId})\n\n` : '';
      }
    });

    // Images with alt text and titles
    this.turndownService.addRule('image', {
      filter: 'img',
      replacement: (content, node) => {
        const src = node.getAttribute('src') || '';
        const alt = node.getAttribute('alt') || '';
        const title = node.getAttribute('title');
        
        if (title) {
          return `![${alt}](${src} "${title}")`;
        }
        return `![${alt}](${src})`;
      }
    });
  }

  private extractLanguage(codeElement: Element | null): string {
    if (!codeElement) return '';
    
    // Check for language class (e.g., language-javascript, hljs-javascript)
    const classList = Array.from(codeElement.classList);
    for (const className of classList) {
      if (className.startsWith('language-')) {
        return className.replace('language-', '');
      }
      if (className.startsWith('hljs-')) {
        return className.replace('hljs-', '');
      }
    }
    
    // Check data attributes
    const dataLang = codeElement.getAttribute('data-language');
    if (dataLang) return dataLang;
    
    return '';
  }

  private extractYouTubeId(url: string): string {
    const match = url.match(/(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/);
    return match?.[1] || '';
  }

  serialize(doc: Node): string {
    // Convert TipTap document to HTML first
    const html = this.nodeToHtml(doc);
    
    // Then convert HTML to Markdown using Turndown
    const markdown = this.turndownService.turndown(html);
    
    // Clean up the markdown
    return this.cleanMarkdown(markdown);
  }

  private nodeToHtml(node: Node): string {
    // This would ideally use TipTap's built-in HTML serializer
    // For now, we'll use a simplified approach
    return this.serializeNodeToHtml(node);
  }

  private serializeNodeToHtml(node: Node): string {
    switch (node.type.name) {
      case 'doc':
        return this.serializeChildrenToHtml(node);
      
      case 'paragraph':
        const content = this.serializeChildrenToHtml(node);
        return content ? `<p>${content}</p>` : '';
      
      case 'heading':
        const level = node.attrs.level || 1;
        const headingContent = this.serializeChildrenToHtml(node);
        return `<h${level}>${headingContent}</h${level}>`;
      
      case 'bulletList':
        const listContent = this.serializeChildrenToHtml(node);
        const isTaskList = node.attrs.tight === false; // Simplified check
        return `<ul${isTaskList ? ' class="task-list"' : ''}>${listContent}</ul>`;
      
      case 'orderedList':
        const orderedContent = this.serializeChildrenToHtml(node);
        return `<ol>${orderedContent}</ol>`;
      
      case 'listItem':
        const itemContent = this.serializeChildrenToHtml(node);
        return `<li>${itemContent}</li>`;
      
      case 'taskItem':
        const checked = node.attrs.checked;
        const taskContent = this.serializeChildrenToHtml(node);
        return `<li class="task-item"><input type="checkbox"${checked ? ' checked' : ''}> ${taskContent}</li>`;
      
      case 'codeBlock':
      case 'codeBlockLowlight':
        const language = node.attrs.language || '';
        const codeContent = node.textContent;
        return `<pre><code${language ? ` class="language-${language}"` : ''}>${codeContent}</code></pre>`;
      
      case 'blockquote':
        const quoteContent = this.serializeChildrenToHtml(node);
        return `<blockquote>${quoteContent}</blockquote>`;
      
      case 'horizontalRule':
        return '<hr>';

      case 'hardBreak':
        return '<br>';

      case 'image':
        const src = node.attrs.src || '';
        const alt = node.attrs.alt || '';
        const title = node.attrs.title;
        return `<img src="${src}" alt="${alt}"${title ? ` title="${title}"` : ''}>`;

      case 'table':
        const tableContent = this.serializeChildrenToHtml(node);
        return `<table>${tableContent}</table>`;

      case 'tableRow':
        const rowContent = this.serializeChildrenToHtml(node);
        return `<tr>${rowContent}</tr>`;

      case 'tableHeader':
        const headerContent = this.serializeChildrenToHtml(node);
        return `<th>${headerContent}</th>`;

      case 'tableCell':
        const cellContent = this.serializeChildrenToHtml(node);
        return `<td>${cellContent}</td>`;

      case 'details':
        const summary = node.attrs.summary || 'Details';
        const detailsContent = this.serializeChildrenToHtml(node);
        return `<details${node.attrs.open ? ' open' : ''}><summary>${summary}</summary>${detailsContent}</details>`;

      case 'callout':
        const type = node.attrs.type || 'info';
        const calloutTitle = node.attrs.title || type.charAt(0).toUpperCase() + type.slice(1);
        const calloutContent = this.serializeChildrenToHtml(node);
        return `<div data-callout data-type="${type}" data-title="${calloutTitle}">${calloutContent}</div>`;
      
      case 'text':
        let text = node.text || '';
        
        // Apply marks
        if (node.marks) {
          for (const mark of node.marks) {
            switch (mark.type.name) {
              case 'bold':
                text = `<strong>${text}</strong>`;
                break;
              case 'italic':
                text = `<em>${text}</em>`;
                break;
              case 'underline':
                text = `<u>${text}</u>`;
                break;
              case 'strike':
                text = `<del>${text}</del>`;
                break;
              case 'code':
                text = `<code>${text}</code>`;
                break;
              case 'highlight':
                text = `<mark>${text}</mark>`;
                break;
              case 'link':
                const href = mark.attrs.href || '';
                const linkTitle = mark.attrs.title || '';
                text = `<a href="${href}"${linkTitle ? ` title="${linkTitle}"` : ''}>${text}</a>`;
                break;
            }
          }
        }
        
        return text;
      
      default:
        return this.serializeChildrenToHtml(node);
    }
  }

  private serializeChildrenToHtml(node: Node): string {
    let result = '';
    node.forEach((child) => {
      result += this.serializeNodeToHtml(child);
    });
    return result;
  }

  private cleanMarkdown(markdown: string): string {
    // Remove excessive line breaks
    markdown = markdown.replace(/\n{3,}/g, '\n\n');

    // Ensure proper spacing around headers
    markdown = markdown.replace(/^(#{1,6}\s.+)$/gm, '\n$1\n');

    // Clean up list formatting
    markdown = markdown.replace(/^(\s*[-*+]\s)/gm, '$1');

    // Fix task list formatting
    markdown = markdown.replace(/^(\s*-\s*\[[ x]\]\s*)/gm, '$1');

    // Clean up table formatting
    markdown = markdown.replace(/\|\s*\|\s*\|/g, '| |');

    // Ensure proper line breaks before and after tables
    markdown = markdown.replace(/(\n\|.*\|.*\n)/g, '\n$1\n');

    return markdown.trim();
  }
}

/**
 * Converts TipTap editor content to unified Markdown
 */
export function tiptapToUnifiedMarkdown(doc: Node, options?: UnifiedSerializerOptions): string {
  const serializer = new UnifiedMarkdownSerializer(options);
  return serializer.serialize(doc);
}

/**
 * Enhanced HTML to Markdown converter using unified serializer
 */
export function htmlToUnifiedMarkdown(html: string, options?: UnifiedSerializerOptions): string {
  const serializer = new UnifiedMarkdownSerializer(options);
  return serializer.turndownService.turndown(html);
}
