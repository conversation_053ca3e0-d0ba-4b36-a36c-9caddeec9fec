/* Course Content Styling */

/* Module styling */

.dark .module-container {
  background-color: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.06);
}

.dark .module-header {
  background-color: #222;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark .module-title {
  color: #fff;
}

.dark .module-info {
  color: #aaa;
}

.dark .module-badge-completed {
  background-color: rgba(74, 222, 128, 0.25);
  color: #4ade80;
}

.dark .module-badge-completed:hover {
  background-color: rgba(74, 222, 128, 0.3);
}

.dark .module-badge-locked {
  background-color: rgba(255, 255, 255, 0.1);
  color: #aaa;
}

/* Lesson styling */

.dark .lesson-item {
  background-color: rgba(30, 30, 30, 0.7);
  border-color: rgba(255, 255, 255, 0.06);
}

.dark .lesson-item:hover {
  background-color: rgba(35, 35, 35, 0.9);
  border-color: rgba(74, 222, 128, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.dark .lesson-title {
  color: #ddd;
}

.dark .lesson-item:hover .lesson-title {
  color: #4ade80;
}

.dark .lesson-info {
  color: #aaa;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Media queries */
@media (max-width: 768px) {
  
  /* Lesson page mobile optimizations */
  
  .lesson-content-wrapper {
    padding: 0.5rem 0.75rem;
  }
  
  .lesson-content {
    font-size: 0.9375rem;
    line-height: 1.6;
  }
  
  .lesson-content h1 {
    font-size: 1.5rem;
  }
  
  .lesson-content h2 {
    font-size: 1.25rem;
  }
  
  .lesson-content h3 {
    font-size: 1.125rem;
  }
  
  .lesson-content img,
  .lesson-content video {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
  }
  
  /* Content with padding for fixed navigation */
}

/* Course details section */
.course-details {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .course-details {
  background-color: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .course-detail-item {
  background-color: #222;
}

/* Module content */

.dark .module-content {
  background-color: #1a1a1a;
}
