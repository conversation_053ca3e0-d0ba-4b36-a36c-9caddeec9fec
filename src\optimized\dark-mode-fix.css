/* Dark Mode Fix CSS */

/* Ensure dark mode is properly applied */
.dark {
  color-scheme: dark;
}

/* Force dark mode styles */
.dark-mode,
.dark {
  --background: 0 0% 10%; /* #1A1A1A - iOS dark background */
  --foreground: 0 0% 100%;

  --card: 0 0% 15%; /* #262626 - iOS dark cards */
  --card-foreground: 0 0% 100%;

  --popover: 0 0% 15%;
  --popover-foreground: 0 0% 100%;

  --primary: 0 70% 50%; /* Red #E63946 */
  --primary-foreground: 0 0% 100%;

  --secondary: 0 15% 20%;
  --secondary-foreground: 0 0% 100%;

  --muted: 0 0% 20%;
  --muted-foreground: 0 0% 70%;

  --accent: 0 30% 20%;
  --accent-foreground: 0 0% 100%;

  --destructive: 0 63% 31%;
  --destructive-foreground: 0 0% 100%;

  --border: 0 0% 20%;
  --input: 0 0% 20%;
  --ring: 0 70% 50%;
}

/* Dark mode specific overrides */
.dark .bg-white,
.dark-mode .bg-white {
  background-color: hsl(0 0% 15%) !important;
}

.dark .text-black,
.dark-mode .text-black {
  color: hsl(0 0% 100%) !important;
}

.dark .border-gray-200,
.dark-mode .border-gray-200 {
  border-color: hsl(0 0% 20%) !important;
}

/* Dark mode card styles */
.dark .bg-card,
.dark-mode .bg-card {
  background-color: hsl(0 0% 15%) !important;
}

/* Dark mode text styles */
.dark .text-foreground,
.dark-mode .text-foreground {
  color: hsl(0 0% 100%) !important;
}

/* Dark mode sidebar styles */
.dark aside,
.dark [class*="sidebar"],
.dark .bg-sidebar,
.dark-mode aside,
.dark-mode [class*="sidebar"],
.dark-mode .bg-sidebar {
  background-color: #c1121f !important;
}

/* Dark mode button styles */
.dark .bg-primary,
.dark-mode .bg-primary {
  background-color: hsl(0 70% 40%) !important;
}

/* Dark mode input styles */
.dark .bg-input,
.dark-mode .bg-input {
  background-color: hsl(0 0% 20%) !important;
}

/* Dark mode border styles */
.dark .border-border,
.dark-mode .border-border {
  border-color: hsl(0 0% 20%) !important;
}
