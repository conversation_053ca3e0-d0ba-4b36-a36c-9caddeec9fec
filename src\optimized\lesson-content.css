/* Lesson Content Styling */

.lesson-content-container {
  max-width: 100%;
  margin: 0 auto;
  font-family: 'Poppins', system-ui, sans-serif;
  padding: 0;
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Full-width lesson content wrapper */
.lesson-content-wrapper-full-width {
  width: 100%;
  padding: 0;
  background-color: white;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 768px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1024px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1280px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1536px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

/* Full-width prose content area with optimized reading width */
.prose-content-area-full-width {
  max-width: 75ch; /* Optimal reading line length */
  margin: 0 auto;
  position: relative;
  padding: 1rem; /* Add padding for content readability */
}

/* Truly full-width variant for when maximum width is desired */
.prose-content-area-full-width.no-max-width {
  max-width: none;
  width: 100%;
  padding: 1rem; /* Add padding for content readability */
}

@media (min-width: 640px) {
  .prose-content-area-full-width {
    padding: 1.5rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .prose-content-area-full-width {
    padding: 2rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width {
    padding: 2.5rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 2.5rem;
  }
}

/* Full-width lesson content container - no width constraints */
.lesson-content-container-full-width {
  width: 100%;
  margin: 0;
  font-family: 'Poppins', system-ui, sans-serif;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

@media (min-width: 768px) {
  .lesson-content-container {
    padding: 0;
    border-radius: 0;
    box-shadow: none;
  }
}

/* Dark mode support for full-width layout */
.dark .lesson-content-wrapper-full-width {
  background-color: #000;
  color: #e0e0e0;
}

.dark .lesson-content-container-full-width {
  background-color: transparent;
  color: #e0e0e0;
}

/* Compact spacing for full-width prose elements */
.prose-content-area-full-width .prose {
  line-height: 1.6; /* Compact line spacing */
}

.prose-content-area-full-width .prose p {
  margin: 0.75rem 0; /* Reduced paragraph spacing */
  line-height: 1.6;
}

.prose-content-area-full-width .prose h1 {
  margin: 1.25rem 0 0.75rem; /* Compact heading spacing */
  line-height: 1.2;
}

.prose-content-area-full-width .prose h2 {
  margin: 1rem 0 0.5rem; /* Compact heading spacing */
  line-height: 1.25;
}

.prose-content-area-full-width .prose h3 {
  margin: 0.875rem 0 0.5rem; /* Compact heading spacing */
  line-height: 1.3;
}

.prose-content-area-full-width .prose ul,
.prose-content-area-full-width .prose ol {
  margin: 0.75rem 0; /* Compact list spacing */
}

.prose-content-area-full-width .prose li {
  margin: 0.25rem 0; /* Compact list item spacing */
  line-height: 1.5;
}

.prose-content-area-full-width .prose img,
.prose-content-area-full-width .prose pre,
.prose-content-area-full-width .prose table,
.prose-content-area-full-width .prose blockquote {
  margin: 1rem 0; /* Standardized compact spacing */
}

.prose-content-area-full-width .prose > * + * {
  margin-top: 0.75rem; /* Standardized element spacing */
}

.prose-content-area-full-width .prose h1 + p,
.prose-content-area-full-width .prose h2 + p,
.prose-content-area-full-width .prose h3 + p {
  margin-top: 0.5rem; /* Tight spacing after headings */
}

@media (max-width: 768px) {
  .lesson-content-container {
    padding: 12px 12px 96px; /* More bottom padding on mobile */
  }
  
  /* Ensure images don't overflow */
  .lesson-content-container img,
  .lesson-content-container .prose img {
    max-width: calc(100vw - 32px);
    height: auto;
    margin: 12px auto;
  }

  /* Adjust table containers for mobile */
  .lesson-content-container table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Improve code block readability on mobile */
  .lesson-content-container pre {
    margin: 12px -12px;
    padding: 12px;
    border-radius: 0;
    font-size: 13px;
    line-height: 1.4;
  }
}

.dark .lesson-content-container {
  background-color: #000;
  color: #e0e0e0;
}

/* Mobile-first text sizing with smaller text */
.lesson-content-container .prose {
  font-size: 0.8125rem; /* 13px base size for mobile */
  line-height: 1.5;
  letter-spacing: -0.011em;
  color: #333;
  max-width: 100%;
  padding: 0;
}

@media (min-width: 768px) {
  .lesson-content-container .prose {
    font-size: 1rem; /* 16px for desktop */
    line-height: 1.8;
  }
}

.dark .lesson-content-container .prose {
  color: #e0e0e0;
}

.lesson-content-container .prose p {
  font-size: 0.8125rem; /* 13px to match body text */
  margin: 6px 0;
  line-height: 1.5;
  padding: 0;
}

/* Even smaller mobile heading sizes */
.lesson-content-container .prose h1 {
  font-size: 1.125rem; /* 18px */
  margin: 16px 0 10px;
  line-height: 1.3;
  letter-spacing: -0.022em;
  font-weight: 600;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h1 {
    font-size: 1.875rem; /* 30px */
  }
}

.dark .lesson-content-container .prose h1 {
  color: #fff;
  border-bottom: 2px solid rgba(230, 57, 70, 0.3);
}

.lesson-content-container .prose h2 {
  font-size: 1rem; /* 16px */
  margin: 16px 0 8px;
  line-height: 1.35;
  letter-spacing: -0.021em;
  font-weight: 600;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h2 {
    font-size: 1.5rem; /* 24px */
  }
}

.lesson-content-container .prose h3 {
  font-size: 0.9375rem; /* 15px */
  margin: 14px 0 8px;
  line-height: 1.4;
  letter-spacing: -0.019em;
  font-weight: 600;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h3 {
    font-size: 1.25rem; /* 20px */
  }
}

.lesson-content-container .prose h4 {
  font-size: 1.25rem;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.lesson-content-container .prose ul,
.lesson-content-container .prose ol {
  margin: 12px 0;
  padding-left: 24px;
}

@media (min-width: 768px) {
  .lesson-content-container .prose ul,
  .lesson-content-container .prose ol {
    margin: 1.25rem 0;
    padding-left: 1.75rem;
  }
}

/* Adjust list and paragraph text size */
.lesson-content-container .prose li,
.lesson-content-container .prose p {
  font-size: 0.8125rem; /* 13px to match body text */
  margin: 6px 0;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .lesson-content-container .prose li {
    font-size: 1rem;
    margin: 8px 0;
  }
}

.lesson-content-container .prose li p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* Code blocks with better spacing */
.lesson-content-container .prose pre {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-size: 0.75rem; /* 12px for technical content */
  line-height: 1.45;
  -webkit-overflow-scrolling: touch;
}

@media (min-width: 768px) {
  .lesson-content-container .prose pre {
    font-size: 0.875rem; /* 14px on desktop */
  }
}

.dark .lesson-content-container .prose pre {
  background-color: #1a1a1a;
}

/* Code block text size */
.lesson-content-container .prose pre code {
  font-size: 0.75rem; /* 12px for technical content */
  line-height: 1.45;
}

@media (min-width: 768px) {
  .lesson-content-container .prose pre code {
    font-size: 0.875rem; /* 14px on desktop */
  }
}

/* Callouts with better spacing */
.lesson-content-container .prose .callout {
  margin: 2em 0;
  padding: 1.5em;
  border-radius: 0.5em;
  border-left-width: 4px;
}

/* Tables with better spacing */
.lesson-content-container .prose table {
  margin: 16px 0;
  width: 100%;
  font-size: 0.75rem; /* 12px for technical content */
  line-height: 1.45;
  border-collapse: collapse;
  overflow-x: auto;
  display: block;
  -webkit-overflow-scrolling: touch;
}

@media (min-width: 768px) {
  .lesson-content-container .prose table {
    font-size: 0.875rem; /* 14px on desktop */
  }
}

.lesson-content-container .prose th,
.lesson-content-container .prose td {
  padding: 8px 12px;
  border: 1px solid #eee;
}

/* Optimize images for lesson content */
.lesson-content-container img,
.lesson-content-container .prose img,
.prose-content-area-full-width img,
.prose-content-area-full-width .prose img,
.lesson-prose img {
  max-width: 100%;
  height: auto;
  margin: 1.5rem auto;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  display: block;
}

.lesson-content-container img:hover,
.lesson-content-container .prose img:hover,
.prose-content-area-full-width img:hover,
.prose-content-area-full-width .prose img:hover,
.lesson-prose img:hover {
  transform: scale(1.01);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Ensure images in markdown content are visible */
.lesson-prose .markdown-preview img,
.lesson-prose .wmde-markdown img,
.lesson-prose .w-md-editor-text img {
  max-width: 100%;
  height: auto;
  margin: 1.5rem auto;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  display: block;
}

/* Responsive image sizing */
@media (max-width: 640px) {
  .lesson-content-container img,
  .lesson-content-container .prose img {
    margin: 1.5rem auto;
  }
}

/* Dark mode adjustments */
.dark .lesson-content-container img,
.dark .lesson-content-container .prose img {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .lesson-content-container img:hover,
.dark .lesson-content-container .prose img:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* Optimize blockquotes for mobile */
.lesson-content-container .prose blockquote {
  font-size: 0.8125rem; /* 13px */
  line-height: 1.5;
  margin: 12px 0;
  padding: 10px 14px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border-left: 3px solid #e63946;
  font-style: normal;
}

@media (min-width: 768px) {
  .lesson-content-container .prose blockquote {
    font-size: 1rem;
    margin: 16px 0;
    padding: 12px 16px;
  }
}

.dark .lesson-content-container .prose blockquote {
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.05);
  border-left-color: #f87171;
}

/* Horizontal rule with better spacing */
.lesson-content-container .prose hr {
  margin: 3em 0;
  border-color: rgba(0, 0, 0, 0.1);
}

.dark .lesson-content-container .prose hr {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Image caption support */

/* Caption and auxiliary text */

/* Responsive adjustments */
@media (max-width: 640px) {
  .lesson-content-container img,
  .lesson-content-container .prose img {
    margin: 1.5rem auto;
  }
}

/* Improved spacing between elements */
.lesson-content-container .prose > * + * {
  margin-top: 12px;
}

/* Custom spacing for better readability */
.lesson-content-container .prose h1 + p,
.lesson-content-container .prose h2 + p,
.lesson-content-container .prose h3 + p {
  margin-top: 6px;
}

/* Fixed footer container styles */

.dark .lesson-footer-container {
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Clean lesson accordion styles */
.lesson-accordion {
  border: none;
  background: transparent;
}

.lesson-accordion-item {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.lesson-accordion-trigger {
  background: transparent !important;
  border: none !important;
  padding: 1rem 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
  transition: color 0.2s ease !important;
}

.lesson-accordion-trigger:hover {
  background: transparent !important;
  color: hsl(var(--primary)) !important;
}

.lesson-accordion-trigger:focus {
  background: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

.lesson-accordion-trigger[data-state="open"] {
  background: transparent !important;
  color: hsl(var(--primary)) !important;
}

.lesson-accordion-content {
  background: transparent !important;
  border: none !important;
  padding: 0 0 1rem 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
}

.lesson-accordion-content > div {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

/* Modern plus/minus icon styling */
.lesson-accordion-trigger .h-5 {
  background: hsl(var(--muted));
  border-radius: 50%;
  padding: 2px;
  transition: all 0.2s ease;
}

.lesson-accordion-trigger:hover .h-5 {
  background: hsl(var(--primary));
  color: white;
}

.lesson-accordion-trigger[data-state="open"] .h-5 {
  background: hsl(var(--primary));
  color: white;
}

/* Safe area insets for modern mobile devices */
