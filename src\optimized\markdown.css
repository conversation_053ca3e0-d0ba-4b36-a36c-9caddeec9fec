/* Modern Markdown Styling */

/* Editor */
.modern-markdown-editor {
  font-family: 'Poppins', system-ui, sans-serif;
}

.modern-markdown-editor textarea {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  line-height: 1.8;
  padding: 1.25rem;
  font-size: 1rem;
  letter-spacing: 0.01em;
}

/* Toolbar */

/* Preview */
.markdown-preview {
  padding: 1.5rem;
}

/* Improve overall prose readability */
.prose {
  font-size: 1.05rem;
  line-height: 1.8;
  letter-spacing: 0.01em;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.prose h1 {
  font-size: 2.25rem;
  margin-top: 0;
}

.prose h2 {
  font-size: 1.75rem;
}

.prose h3 {
  font-size: 1.5rem;
}

.prose h4 {
  font-size: 1.25rem;
}

.prose ul,
.prose ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  padding-left: 0.375em;
}

.prose > ul > li p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose > ul > li > *:first-child,
.prose > ol > li > *:first-child {
  margin-top: 1.25em;
}

.prose > ul > li > *:last-child,
.prose > ol > li > *:last-child {
  margin-bottom: 1.25em;
}

.prose blockquote {
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding: 1em 1.5em;
  border-radius: 0.5rem;
}

/* Code blocks */
.prose pre {
  position: relative;
  margin: 2rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.prose pre code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.95rem;
  line-height: 1.8;
  display: block;
  overflow-x: auto;
  tab-size: 2;
  padding: 0.5em 0;
}

.prose pre .code-language {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  font-size: 0.75rem;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  color: var(--muted-foreground);
  background-color: var(--muted);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  opacity: 0.8;
}

.prose pre .copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--muted-foreground);
  background-color: var(--muted);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.prose pre:hover .copy-button {
  opacity: 0.8;
}

.prose pre .copy-button:hover {
  opacity: 1;
  background-color: var(--accent);
  color: var(--accent-foreground);
}

/* Callouts */
.prose .callout {
  margin: 2rem 0;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left-width: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 1rem;
  line-height: 1.7;
}

.prose .callout.info {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: rgb(59, 130, 246);
}

.prose .callout.warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: rgb(245, 158, 11);
}

.prose .callout.tip {
  background-color: rgba(230, 57, 70, 0.1);
  border-left-color: rgb(230, 57, 70);
}

.prose .callout.note {
  background-color: rgba(139, 92, 246, 0.1);
  border-left-color: rgb(139, 92, 246);
}

/* Dropdowns */
.prose .dropdown {
  margin: 0;
  border: none;
  border-radius: 0;
  background-color: transparent;
  box-shadow: none;
  outline: none;
}

.prose .dropdown summary {
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  user-select: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  border: none;
}

.prose .dropdown summary::-webkit-details-marker {
  display: none;
}

.prose .dropdown summary .dropdown-icon {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--primary);
  opacity: 0.8;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.prose .dropdown summary .dropdown-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  transform: translate(-50%, -70%) rotate(45deg);
}

.prose .dropdown[open] summary .dropdown-icon {
  transform: rotate(180deg);
  background-color: var(--primary-dark, var(--primary));
}

.prose .dropdown > div {
  padding: 0.25rem 0 0 0.75rem;
  font-size: inherit;
}

/* Dark mode adjustments */
.dark .prose .callout.info {
  background-color: rgba(59, 130, 246, 0.15);
  border-left-color: rgba(59, 130, 246, 0.5);
}

.dark .prose .callout.warning {
  background-color: rgba(245, 158, 11, 0.15);
  border-left-color: rgba(245, 158, 11, 0.5);
}

.dark .prose .callout.tip {
  background-color: rgba(230, 57, 70, 0.15);
  border-left-color: rgba(230, 57, 70, 0.5);
}

.dark .prose .callout.note {
  background-color: rgba(139, 92, 246, 0.15);
  border-left-color: rgba(139, 92, 246, 0.5);
}

/* Task lists */
.prose input[type="checkbox"] {
  margin-right: 0.5rem;
  accent-color: var(--primary);
}

/* Tables */
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  overflow: hidden;
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 0.95rem;
}

.prose table th {
  background-color: var(--muted);
  font-weight: 600;
  text-align: left;
  padding: 1rem 1.25rem;
  border: 1px solid var(--border);
}

.prose table td {
  padding: 1rem 1.25rem;
  border: 1px solid var(--border);
  line-height: 1.6;
}

.prose table tr:nth-child(even) {
  background-color: var(--muted-foreground/5);
}

.prose table tr:hover {
  background-color: var(--muted-foreground/10);
  transition: background-color 0.2s ease;
}

/* External links */
.prose a .external-link-icon {
  display: inline-block;
  margin-left: 0.25rem;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.prose a:hover .external-link-icon {
  opacity: 1;
}

/* Line numbers in code blocks */
.prose pre.line-numbers {
  padding-left: 3.8rem;
  counter-reset: linenumber;
}

.prose pre.line-numbers > code {
  position: relative;
  white-space: inherit;
}

/* Math blocks */
.prose .math-block {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  padding: 1rem;
  margin: 1.5rem 0;
  background-color: var(--muted/30);
  border-radius: 0.5rem;
  overflow-x: auto;
  text-align: center;
}

/* Highlighted text */
.prose mark {
  background-color: rgba(230, 57, 70, 0.2);
  padding: 0.1em 0.2em;
  border-radius: 0.2em;
}

/* Headings with anchor links */
.prose h1[id],
.prose h2[id],
.prose h3[id],
.prose h4[id],
.prose h5[id],
.prose h6[id] {
  position: relative;
}

.prose h1[id]:hover::before,
.prose h2[id]:hover::before,
.prose h3[id]:hover::before,
.prose h4[id]:hover::before,
.prose h5[id]:hover::before,
.prose h6[id]:hover::before {
  content: "#";
  position: absolute;
  left: -1.5rem;
  color: var(--primary);
  font-weight: normal;
  opacity: 0.5;
}

/* Scrollbar styling */
.prose pre::-webkit-scrollbar {
  height: 8px;
}

.prose pre::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

.prose pre::-webkit-scrollbar-thumb {
  background: var(--muted-foreground/50);
  border-radius: 4px;
}

.prose pre::-webkit-scrollbar-thumb:hover {
  background: var(--muted-foreground);
}

/* Base image container */

/* Image caption */

/* Image zoom overlay */
