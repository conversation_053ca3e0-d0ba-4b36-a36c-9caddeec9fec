/* Mobile Utility Classes */

/* Hide elements on mobile */

/* Hide elements on desktop */

/* Improved touch targets */

/* Mobile-friendly spacing */

/* Mobile-friendly text */

/* Mobile-friendly grid */

/* Mobile-friendly flex */

/* Mobile-friendly card */

/* Mobile-friendly button */

/* Mobile-friendly input */

/* Mobile-friendly scrolling */

/* Mobile-friendly list */

/* Mobile-friendly divider */

/* Mobile-friendly container */

/* Mobile-friendly section */

/* Mobile-friendly form */

/* Mobile-friendly animations */

/* Mobile-friendly focus states */

/* Mobile reading optimizations */

/* Content spacing for mobile */

/* Image optimizations for mobile */

/* Tables for mobile */

/* Touch-friendly buttons */

/* Readable font sizes */

/* Dark mode adjustments */

/* Reduced motion */

/* Mobile Utilities and Overrides */

/* Better tap targets */

/* Improved scrolling */

/* Remove tap highlight on mobile */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Mobile-specific paddings */

/* Mobile menu adjustments */

/* Mobile-friendly buttons */

/* Improved form inputs on mobile */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="number"],
textarea {
  font-size: 16px !important;
  min-height: 44px;
  border-radius: 9999px;
}

/* Mobile scrolling */

/* Mobile active states */

/* Mobile-specific breakpoints */
@media (max-width: 768px) {

  .text-mobile {
    font-size: 0.9375rem;
  }
}

/* iOS-specific fixes */