/* Professional UI Enhancements for i-can-iv e-learning platform */

/* Font standardization */
:root {
  --font-primary: '<PERSON><PERSON><PERSON>', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Font size system */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.8125rem; /* 13px */
  --font-size-md: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */

  /* Custom navbar font sizes - unified size for all sidebar elements */
  --font-size-navbar: 1rem; /* 16px - unified size for all sidebar elements */
  --font-size-navbar-sm: 1rem; /* 16px - same as main navbar */
  --font-size-navbar-header: 1rem; /* 16px - same as main navbar */
  --font-size-navbar-main: 1rem; /* 16px - same as main navbar */

  /* Line heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Base styles */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --content-max-width: 1400px;
  --grid-gap: 1.5rem;
  --card-padding: 1.5rem;
  --section-spacing: 2rem;
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global font application */
html, body {
  font-family: var(--font-primary);
  font-size: 16px;
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

/* Global typography refinements */
body {
  letter-spacing: -0.011em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Heading standardization */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: var(--font-primary);
  letter-spacing: -0.02em;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: #111827;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6,
.dark .h1, .dark .h2, .dark .h3, .dark .h4, .dark .h5, .dark .h6 {
  color: #f3f4f6;
}

h1, .h1 { font-size: var(--font-size-4xl); }
h2, .h2 { font-size: var(--font-size-3xl); }
h3, .h3 { font-size: var(--font-size-2xl); }
h4, .h4 { font-size: var(--font-size-xl); }
h5, .h5 { font-size: var(--font-size-lg); }
h6, .h6 { font-size: var(--font-size-base); }

/* Paragraph standardization */
p, .p {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: #374151;
}

.dark p, .dark .p {
  color: #d1d5db;
}

small, .small {
  font-size: var(--font-size-sm);
}

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }

/* Monospace text */
code, pre, .code, .pre {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

/* Font weight utilities */
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Card refinements */
.card,
div[class*="rounded-lg"],
div[class*="shadow-"],
div[class*="border"] {
  font-family: var(--font-primary);
  border-radius: 0.5rem !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05),
              0 1px 2px rgba(0, 0, 0, 0.06) !important;
  border-color: rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
}

.dark .card,
.dark div[class*="rounded-lg"],
.dark div[class*="shadow-"],
.dark div[class*="border"] {
  border-color: rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2),
              0 1px 2px rgba(0, 0, 0, 0.12) !important;
}

/* Button refinements */
button,
[role="button"],
[type="button"],
[type="submit"] {
  font-family: var(--font-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  letter-spacing: -0.01em;
  transition: all 0.2s ease !important;
}

button:hover,
[role="button"]:hover,
[type="button"]:hover,
[type="submit"]:hover {
  transform: translateY(-1px);
}

button:active,
[role="button"]:active,
[type="button"]:active,
[type="submit"]:active {
  transform: translateY(0);
}

/* Input and form control refinements */
input,
select,
textarea {
  font-family: var(--font-primary);
  font-size: var(--font-size-md) !important;
  border-radius: 0.375rem !important;
  border-width: 1px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.dark input,
.dark select,
.dark textarea {
  background-color: rgba(30, 30, 30, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.08) !important;
}

/* Label standardization */
label {
  font-family: var(--font-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: #4b5563;
}

.dark label {
  color: #9ca3af;
}

/* Table refinements */
table {
  font-family: var(--font-primary);
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  font-size: var(--font-size-md);
}

th {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-semibold);
  letter-spacing: -0.01em;
  text-align: left;
  text-transform: uppercase;
  font-size: var(--font-size-xs);
  color: #4b5563;
}

.dark th {
  color: #9ca3af;
}

td {
  font-family: var(--font-primary);
  font-size: var(--font-size-md);
  color: #374151;
}

.dark td {
  color: #d1d5db;
}

td, th {
  padding: 0.75rem 1rem;
  vertical-align: middle;
}

tbody tr {
  border-bottom-width: 1px;
  border-color: rgba(0, 0, 0, 0.06);
}

.dark tbody tr {
  border-color: rgba(255, 255, 255, 0.05);
}

/* Modern components */
.badge {
  font-family: var(--font-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xs);
  line-height: 1;
  border-radius: 9999px;
  padding: 0.25rem 0.5rem;
  white-space: nowrap;
}

.dark .badge-primary {
  background-color: #f87171;
}

.dark .badge-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

/* Layout refinements */
.container {
  max-width: 1200px;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
}

/* Sidebar refinements */
.sidebar {
  font-family: var(--font-primary);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  z-index: 50;
}

.dark .sidebar {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Navigation refinements */

.dark .nav-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark .nav-item.active {
  background-color: rgba(255, 255, 255, 0.08);
}

/* Dropdown and popover refinements */
.dropdown,
[role="menu"],
[class*="popover"] {
  font-family: var(--font-primary);
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08),
              0 2px 4px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.dark .dropdown,
.dark [role="menu"],
.dark [class*="popover"] {
  border-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
              0 2px 4px rgba(0, 0, 0, 0.12);
}

/* Dashboard cards - inspired by professional UI */

.dark .dashboard-card {
  background-color: rgba(30, 30, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .dashboard-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.dark .dashboard-card-title {
  color: rgba(255, 255, 255, 0.9);
}

.dark .dashboard-card-value {
  color: white;
}

.dark .dashboard-card-caption {
  color: rgba(255, 255, 255, 0.5);
}

/* Course cards - professional style */

.dark .course-card {
  background-color: rgba(30, 30, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .course-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.dark .course-card-title {
  color: white;
}

.dark .course-card-description {
  color: rgba(255, 255, 255, 0.7);
}

/* Module specific styling */

/* Professional animation effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Loading states - professional */
.skeleton {
  background: linear-gradient(90deg,
    rgba(0, 0, 0, 0.06) 25%,
    rgba(0, 0, 0, 0.08) 37%,
    rgba(0, 0, 0, 0.06) 63%
  );
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.06) 25%,
    rgba(255, 255, 255, 0.08) 37%,
    rgba(255, 255, 255, 0.06) 63%
  );
  background-size: 400% 100%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* Sidebar specific navigation */
.sidebar a,
nav a {
  font-family: var(--font-primary);
  font-size: var(--font-size-navbar) !important;
  font-weight: var(--font-weight-medium);
  letter-spacing: -0.01em;
}

/* Main navigation */

header a,
header nav a {
  font-size: var(--font-size-navbar) !important;
}

/* Navigation labels */

.dark .nav-label,
.dark .sidebar-label,
.dark .sidebar-heading,
.dark .sidebar-section-title {
  color: rgba(255, 255, 255, 0.5);
}

/* Directly target the sidebar navigation in Layout.tsx */

.w-64 a[href],
div[class*="sidebar"] a {
  font-size: var(--font-size-navbar) !important;
  font-weight: 500 !important;
}

/* Target specific sidebar elements by class patterns */
[class*="sidebar"] [class*="text-"] {
  font-size: var(--font-size-navbar) !important;
}

/* Target the icon labels */

svg + span {
  font-size: var(--font-size-navbar) !important;
  font-weight: 500 !important;
}

/* Additional selectors for navigation - higher specificity */
.sidebar li a,
header nav li a,
header nav a,
nav a {
  font-size: var(--font-size-navbar) !important;
}

/* Specific override for any potentially small sidebar text */
[class*="sidebar"] [class*="text-xs"],
[class*="sidebar"] [class*="text-sm"],
[class*="sidebar"] .text-xs,
[class*="sidebar"] .text-sm,
header [class*="text-xs"],
header [class*="text-sm"],
header .text-xs,
header .text-sm,
nav [class*="text-xs"],
nav [class*="text-sm"],
nav .text-xs,
nav .text-sm {
  font-size: var(--font-size-navbar-sm) !important;
}

/* Make text in the sidebar navigation header slightly larger and more prominent */

.w-64 .font-bold,
.sidebar .text-lg {
  font-size: var(--font-size-navbar-header) !important;
  font-weight: var(--font-weight-semibold) !important;
}

/* Specific target for sidebar headings like "Navigation" and "Resources" */
.sidebar .uppercase.text-xs,
.sidebar .mb-2.px-3.text-xs,
.sidebar h2.text-xs {
  font-size: var(--font-size-navbar-sm) !important;
  font-weight: var(--font-weight-semibold) !important;
  letter-spacing: 0.05em;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Ensure the Navigation section labels are clear */
.uppercase,
[class*="uppercase"],
[class*="tracking-wider"],
.tracking-wider {
  letter-spacing: 0.05em;
}

/* Specifically target the app name in the sidebar */

.w-64 a.flex.items-center.gap-2.text-lg,
.sidebar a.flex.items-center.gap-2.text-lg {
  font-size: var(--font-size-navbar-header) !important;
  font-weight: var(--font-weight-bold) !important;
}

/* Ensure readable display name and role */
.sidebar .text-sm.font-medium,
.sidebar .text-xs.text-white\/70 {
  font-size: var(--font-size-navbar-sm) !important;
  line-height: 1.3;
}

/* Enhanced styles for sidebar navigation items */

.sidebar .NavLink,
.sidebar a[href],
.sidebar li a,
div[class*="sidebar"] li a {
  font-size: var(--font-size-navbar-main) !important; /* Make main sidebar links even larger */
  font-weight: var(--font-weight-medium) !important;
  padding: 0.625rem 0.75rem !important; /* Slightly more padding for better touch targets */
  line-height: 1.3 !important;
  letter-spacing: -0.01em !important;
  transition: all 0.2s ease !important;
}

/* Improve readability of specific main navigation links in sidebar */

/* Make the sidebar icons more visible */

.sidebar svg {
  width: 1.25rem !important;
  height: 1.25rem !important;
  flex-shrink: 0 !important;
}

/* Improve sidebar section titles */
.sidebar .mb-2.px-3.text-xs.font-semibold.text-white\/70,
.sidebar h2.text-xs {
  margin-top: 1.25rem !important;
  margin-bottom: 0.5rem !important;
  font-size: var(--font-size-navbar-sm) !important;
  font-weight: var(--font-weight-semibold) !important;
  letter-spacing: 0.05em !important;
  color: rgba(255, 255, 255, 0.8) !important; /* Improved visibility */
  text-transform: uppercase !important;
}

/* Specifically target navigation items by their position in the DOM */

.sidebar nav ul li a {
  font-size: var(--font-size-navbar-main) !important;
  padding: 0.625rem 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

/* Ensure the nav items transition smoothly */

.sidebar nav ul li a:hover,
.sidebar a[href]:hover {
  background-color: rgba(255, 255, 255, 0.12) !important;
  transform: translateX(4px);
}

/* Add focus styles for keyboard navigation */

.sidebar a:focus,
.sidebar a:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.4) !important;
  outline-offset: 2px !important;
}

/* Ensure user display name is clearly visible */
.sidebar .text-sm.font-medium.text-white {
  font-size: var(--font-size-navbar) !important;
  font-weight: var(--font-weight-semibold) !important;
}

/* User role indicator - make more visible */
.sidebar .text-xs.text-white\/70 {
  font-size: var(--font-size-navbar-sm) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Main navigation bar enhancements */
header .container {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 0.75rem 1rem !important;
}

/* Main navigation buttons and links */
header button,
header a,
header [role="button"] {
  font-size: var(--font-size-navbar) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all 0.2s ease !important;
}

/* Main navbar items and links */
header nav a,
header nav button,
header nav [role="button"] {
  font-size: var(--font-size-navbar) !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.15s ease-in-out !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Main navigation item hover effect */
header nav a:hover,
header nav button:hover,
header nav [role="button"]:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

.dark header nav a:hover,
.dark header nav button:hover,
.dark header nav [role="button"]:hover,
.dark .main-nav a:hover,
.dark .main-nav button:hover,
.dark .main-nav [role="button"]:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* Main navigation active state */
header nav a.active,
header nav [role="button"].active {
  font-weight: var(--font-weight-semibold) !important;
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.dark header nav a.active,
.dark header nav [role="button"].active,
.dark .main-nav a.active,
.dark .main-nav [role="button"].active {
  background-color: rgba(255, 255, 255, 0.12) !important;
}

/* MainNavigation component specific styles */
/* This targets the component we identified in the code */
header .justify-center,
header .flex.justify-center,
header .flex-1,
header .flex.flex-1 {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Main nav dropdown items */
[role="menu"] a,
[role="menu"] button,
.dropdown-menu a,
.dropdown-menu button {
  font-size: var(--font-size-navbar) !important;
  font-weight: var(--font-weight-medium) !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.375rem !important;
  transition: background-color 0.15s ease-in-out !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Ensure buttons in the header have proper text size */
header button svg + span,
header [role="button"] svg + span {
  font-size: var(--font-size-navbar) !important;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  /* Slightly smaller text on mobile but still accessible */
  header nav a,
  header nav button,
  header nav [role="button"] {
    font-size: calc(var(--font-size-navbar) - 0.0625rem) !important; /* 16px */
    padding: 0.4375rem 0.625rem !important;
  }

  /* Ensure proper sizing for buttons on mobile */
  header button,
  header [role="button"] {
    padding: 0.4375rem !important;
  }
}

/* Desktop layout improvements */
@media (min-width: 1024px) {
  /* Container sizing */
  .container {
    max-width: var(--content-max-width);
    padding-left: 2rem;
    padding-right: 2rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* Grid layouts */

  /* Card styles */
  .card {
    background: var(--background);
    border-radius: 1rem;
    border: 1px solid var(--border);
    padding: var(--card-padding);
    transition: var(--transition-medium);
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
  }

  /* Section spacing */
  .section {
    margin-bottom: var(--section-spacing);
  }

  /* Navigation improvements */

  /* Form elements */

  /* Button improvements */
  .button {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: var(--transition-fast);
  }

  .button:hover {
    transform: translateY(-1px);
  }

  /* Typography scale */
  h1 { font-size: 2.5rem; line-height: 1.2; }
  h2 { font-size: 2rem; line-height: 1.25; }
  h3 { font-size: 1.5rem; line-height: 1.3; }
  h4 { font-size: 1.25rem; line-height: 1.35; }

  /* Spacing utilities */
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .card:hover {
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.2);
  }
}

/* UNIFIED SIDEBAR FONT SIZE - Force ALL sidebar text to be the same size */
.sidebar *,
aside *,
[class*="sidebar"] *,
div[class*="bg-sidebar"] *,
.w-64 * {
  font-size: var(--font-size-navbar) !important;
}

/* Override any specific text size classes in sidebar */
.sidebar .text-xs,
.sidebar .text-sm,
.sidebar .text-base,
.sidebar .text-lg,
.sidebar .text-xl,
aside .text-xs,
aside .text-sm,
aside .text-base,
aside .text-lg,
aside .text-xl,
[class*="sidebar"] .text-xs,
[class*="sidebar"] .text-sm,
[class*="sidebar"] .text-base,
[class*="sidebar"] .text-lg,
[class*="sidebar"] .text-xl,
.w-64 .text-xs,
.w-64 .text-sm,
.w-64 .text-base,
.w-64 .text-lg,
.w-64 .text-xl {
  font-size: var(--font-size-navbar) !important;
}

/* Force all sidebar elements to use unified font size */
.sidebar span,
.sidebar div,
.sidebar p,
.sidebar a,
.sidebar button,
.sidebar h1,
.sidebar h2,
.sidebar h3,
.sidebar h4,
.sidebar h5,
.sidebar h6,
aside span,
aside div,
aside p,
aside a,
aside button,
aside h1,
aside h2,
aside h3,
aside h4,
aside h5,
aside h6,
[class*="sidebar"] span,
[class*="sidebar"] div,
[class*="sidebar"] p,
[class*="sidebar"] a,
[class*="sidebar"] button,
[class*="sidebar"] h1,
[class*="sidebar"] h2,
[class*="sidebar"] h3,
[class*="sidebar"] h4,
[class*="sidebar"] h5,
[class*="sidebar"] h6 {
  font-size: var(--font-size-navbar) !important;
}

