/* Top Navigation and Mobile Menu Fixes */

[role="button"],
a,
button {
  -webkit-tap-highlight-color: transparent !important;
  cursor: pointer !important;
  touch-action: manipulation !important; 
  position: relative !important;
  pointer-events: auto !important;
}

/* Mobile menu specific fixes */

/* Enable pointer events on mobile navigation */
nav, 
.flex.justify-center.flex-1,
header,
.sticky {
  pointer-events: auto !important;
}

/* Mobile Sidebar Backdrop */
.fixed.inset-0.bg-black\/50.z-40 {
  touch-action: none;
  -webkit-tap-highlight-color: transparent;
  pointer-events: auto;
}

/* Modern Red Sidebar Styles */

/* Main sidebar container - Only target our custom floating sidebar */
.floating-sidebar,
.modern-sidebar {
  background-color: #E63946 !important;
  color: white !important;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.08) !important;
}

/* Force ALL text to be white - Only for our custom sidebar */
.floating-sidebar *,
.modern-sidebar * {
  color: white !important;
}

/* Specific text elements that need white text - Only for our custom sidebar */
.floating-sidebar span,
.modern-sidebar span,
.floating-sidebar div,
.modern-sidebar div,
.floating-sidebar p,
.modern-sidebar p {
  color: white !important;
}

/* Dark mode sidebar */
.dark .sidebar, 
.dark aside, 
.dark [class*="sidebar"], 
.dark .bg-sidebar,
.dark div[class*="bg-sidebar"] {
  background-color: #c1121f !important;
  color: white !important;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.2) !important;
}

/* Mobile sidebar - Only target our custom floating sidebar */
@media (max-width: 768px) {
  .floating-sidebar,
  .modern-sidebar {
    width: 100% !important;
    max-width: 260px !important;
  }

  /* Remove conflicting styles - let modern-sidebar.css handle sizing */
}

/* Sidebar item transitions */
.sidebar a, 
aside a, 
[class*="sidebar"] a,
.sidebar button,
aside button,
[class*="sidebar"] button {
  transition: all 0.2s ease-in-out !important;
  color: white !important;
  position: relative;
  overflow: hidden;
}

/* Force links and buttons to have white text */
.sidebar a *, 
aside a *, 
[class*="sidebar"] a *,
.sidebar button *,
aside button *,
[class*="sidebar"] button * {
  color: white !important;
}

/* Sidebar active links */
.sidebar a.active, 
aside a.active, 
[class*="sidebar"] a.active {
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: white !important;
  font-weight: 500 !important;
}

/* Sidebar hover states with subtle shine effect */
.sidebar a:hover, 
aside a:hover, 
[class*="sidebar"] a:hover,
.sidebar button:hover,
aside button:hover,
[class*="sidebar"] button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-1px);
  color: white !important;
}

/* Remove hover transform on mobile - better for touch */
@media (max-width: 768px) {
  .sidebar a:hover, 
  aside a:hover, 
  [class*="sidebar"] a:hover,
  .sidebar button:hover,
  aside button:hover,
  [class*="sidebar"] button:hover {
    transform: none !important;
  }
  
  .sidebar a:active, 
  aside a:active, 
  [class*="sidebar"] a:active,
  .sidebar button:active,
  aside button:active,
  [class*="sidebar"] button:active {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}

/* Active state hover */
.sidebar a.active:hover, 
aside a.active:hover, 
[class*="sidebar"] a.active:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Sidebar borders */
.sidebar [class*="border"], 
aside [class*="border"], 
[class*="sidebar"] [class*="border"] {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Sidebar headings/section names */
.sidebar .uppercase,
aside .uppercase,
[class*="sidebar"] .uppercase {
  letter-spacing: 0.05em !important;
  opacity: 1 !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  color: white !important;
}

/* Avatar styling */
.sidebar .avatar,
aside .avatar,
[class*="sidebar"] .avatar {
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.2s ease-in-out !important;
}

.sidebar .avatar:hover,
aside .avatar:hover,
[class*="sidebar"] .avatar:hover {
  border-color: rgba(255, 255, 255, 0.4) !important;
}

/* Override any opacity modifiers */
.sidebar [class*="opacity-"],
aside [class*="opacity-"],
[class*="sidebar"] [class*="opacity-"] {
  opacity: 1 !important;
}

/* Icons in sidebar */
.sidebar svg,
aside svg,
[class*="sidebar"] svg {
  transition: all 0.2s ease-in-out !important;
  color: white !important;
  fill: white !important;
}

.sidebar a:hover svg,
aside a:hover svg,
[class*="sidebar"] a:hover svg,
.sidebar button:hover svg,
aside button:hover svg,
[class*="sidebar"] button:hover svg {
  transform: scale(1.1);
  color: white !important;
}

/* Enhanced focus styles */
.sidebar a:focus-visible,
aside a:focus-visible,
[class*="sidebar"] a:focus-visible,
.sidebar button:focus-visible,
aside button:focus-visible,
[class*="sidebar"] button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.5) !important;
  outline-offset: -2px !important;
}

/* Font weight consistency */
.sidebar a,
aside a,
[class*="sidebar"] a,
.sidebar button,
aside button,
[class*="sidebar"] button {
  font-weight: 500 !important;
  color: white !important;
}

/* Logo styling */
.sidebar a.flex.items-center.gap-2,
aside a.flex.items-center.gap-2 {
  font-weight: 700 !important;
  color: white !important;
}

/* User profile area */
.sidebar .px-4.py-4.border-b,
aside .px-4.py-4.border-b {
  padding: 1rem !important;
}

/* Footer items */
.sidebar .mt-auto.border-t,
aside .mt-auto.border-t {
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Mobile overlay styling */
.fixed.inset-0.bg-black\/50 {
  -webkit-tap-highlight-color: transparent;
}

/* Custom Theme Toggle Styles */
.sidebar [role="switch"],
aside [role="switch"] {
  display: none !important;
}

/* Theme toggle Container */
.sidebar .flex.flex-col.items-center.bg-white\/10,
aside .flex.flex-col.items-center.bg-white\/10 {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease-in-out !important;
  border-radius: 0.75rem !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Mobile optimizations for theme toggle */
@media (max-width: 768px) {
  .sidebar .flex.flex-col.items-center.bg-white\/10,
  aside .flex.flex-col.items-center.bg-white\/10 {
    padding: 0.75rem 1rem !important;
  }
  
  .sidebar .w-full.h-7.bg-white\/20,
  aside .w-full.h-7.bg-white\/20 {
    height: 2rem !important;
  }
  
  .sidebar .flex.justify-between.px-3 span,
  aside .flex.justify-between.px-3 span {
    font-size: 0.875rem !important;
  }
}

.sidebar .flex.flex-col.items-center.bg-white\/10:hover,
aside .flex.flex-col.items-center.bg-white\/10:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Animated background for theme toggle */
.sidebar .flex.flex-col.items-center.bg-white\/10::before,
aside .flex.flex-col.items-center.bg-white\/10::before {
  content: '' !important;
  position: absolute !important;
  top: -50% !important;
  left: -50% !important;
  width: 200% !important;
  height: 200% !important;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.025),
    rgba(255, 255, 255, 0)
  ) !important;
  transform: rotate(30deg) !important;
  z-index: 0 !important;
  pointer-events: none !important;
  transition: all 0.5s ease-in-out !important;
}

.sidebar .flex.flex-col.items-center.bg-white\/10:hover::before,
aside .flex.flex-col.items-center.bg-white\/10:hover::before {
  transform: rotate(30deg) translate(10%, 10%) !important;
}

/* Theme toggle slider track */
.sidebar .w-full.h-7.bg-white\/20,
aside .w-full.h-7.bg-white\/20 {
  position: relative !important;
  overflow: hidden !important;
  border-radius: 9999px !important;
}

/* Theme toggle slider thumb */

/* Override text colors for toggle labels */
.sidebar .flex.justify-between.px-3 span.text-black,
aside .flex.justify-between.px-3 span.text-black {
  color: black !important;
  font-weight: 600 !important;
  mix-blend-mode: difference !important;
}

/* Mobile menu toggle button styling */

/* Mobile specific styling for the hamburger button */

/* Force red sidebar regardless of other styles */
.bg-sidebar {
  background-color: #E63946 !important;
}

.dark .bg-sidebar {
  background-color: #c1121f !important;
}
