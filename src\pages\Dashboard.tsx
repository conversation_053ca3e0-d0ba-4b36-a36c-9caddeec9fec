import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import Layout from '../components/Layout';
import CourseCard from '../components/CourseCard';
import { BookOpen, Zap, Star, AlertTriangle, GraduationCap, TrendingUp } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { CourseCardSkeleton } from '@/components/ui/loading-skeleton';
import { motion } from 'framer-motion';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';

import { getCoursePlaceholderImage, getCourseImageSource, isDataUrl } from '@/utils/imageUtils';
import { calculateCourseProgress } from '@/services/course/progressApi';
import { getEnrollmentStatus } from '@/services/course/enrollmentApi';
import { toast } from 'sonner';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { GoogleFormPrompt } from '@/components/GoogleFormPrompt';
import { useLocation, useNavigate, Link } from 'react-router-dom';

// Import test function for debugging
import '@/utils/testEnrollment';

interface Course {
  id: string;
  slug: string;
  title: string;
  description: string;
  instructor: string;
  total_modules: number;
  completed_modules: number;
  created_at: string;
  updated_at: string;
  image_url?: string;
  image?: string;
}

interface CourseWithProgress {
  id: string;
  title: string;
  description: string;
  progress: number;
  startDate: string;
  image?: string;
  hasVideo?: boolean;
  status?: string;
  isEnrolled?: boolean;
}

const Dashboard = () => {
  const { user, setUser } = useAuth();
  const [hasCompletedDemographics, setHasCompletedDemographics] = useState<boolean | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Check if the user has completed the demographic questionnaire
  useEffect(() => {
    if (user) {
      const demographicsCompleted = user.user_metadata?.demographic_questionnaire_completed === true;
      setHasCompletedDemographics(demographicsCompleted);
    }
  }, [user]);

  // Handle demographic questionnaire completion
  const handleDemographicsComplete = () => {
    setHasCompletedDemographics(true);
    // Refresh course data
    refetch();
  };

  // Fetch courses with progress
  const { data: coursesData = [], isLoading, error, refetch } = useQuery({
    queryKey: ['dashboard-courses', user?.id],
    queryFn: async () => {
      try {
        console.log('Fetching courses for user:', user?.id);

        // Fetch all courses
        const { data: coursesData, error: coursesError } = await supabase
          .from('courses')
          .select('*') as { data: Course[] | null, error: any };

        if (coursesError) {
          console.error('Error fetching courses:', coursesError);
          throw coursesError;
        }

        // Transform course data and fetch enrollment status for each course
        const coursesWithEnrollment = await Promise.all(
          (coursesData || []).map(async (course) => {
            let isEnrolled = false;
            let status = 'not_started';
            let startDate = 'Not Started';
            let progress = 0;

            // Fetch enrollment status if user is logged in
            if (user?.id) {
              try {
                const enrollment = await getEnrollmentStatus(course.id, user.id);
                if (enrollment) {
                  isEnrolled = true;
                  status = enrollment.status;

                  // Set startDate based on enrollment status
                  switch (enrollment.status) {
                    case 'in_progress':
                      startDate = 'Active';
                      break;
                    case 'completed':
                      startDate = 'Completed';
                      progress = 100;
                      break;
                    case 'not_started':
                      startDate = 'Not Started';
                      break;
                    default:
                      startDate = 'Not Started';
                  }
                }
              } catch (enrollmentError) {
                console.error('Error fetching enrollment for course:', course.id, enrollmentError);
                // Continue with default values
              }
            }

            return {
              id: course.id,
              title: course.title || 'Untitled Course',
              description: course.description || 'No description available',
              progress,
              startDate,
              image: course.image_url || course.image,
              status,
              isEnrolled,
              hasVideo: false
            };
          })
        );

        return coursesWithEnrollment;
      } catch (error) {
        console.error('Error in dashboard query:', error);
        throw error;
      }
    },
    enabled: !!user?.id,
    retry: 1
  });

  const courses = coursesData || [];

  // Check if we're on the /my-courses route
  useEffect(() => {
    const handleMyCourses = async () => {
      if (location.pathname === '/my-courses' && courses.length > 0) {
        // Get the first enrolled course or just the first course if none are enrolled
        const enrolledCourse = courses.find(course => course.isEnrolled);
        const targetCourse = enrolledCourse || courses[0];

        // Navigate to the modules page of the course
        navigate(`/course/${targetCourse.id}/modules`);
      }
    };

    handleMyCourses();
  }, [location.pathname, courses, navigate]);

  // Calculate stats
  const stats = {
    totalCourses: courses.length,
    inProgress: courses.filter(c => c.status === 'in_progress').length,
    completed: courses.filter(c => c.status === 'completed').length,
    enrolled: courses.filter(c => c.isEnrolled).length
  };

  // Show loading state while checking demographics
  if (hasCompletedDemographics === null) {
    return (
      <Layout>
        <PageContainer pageType="dashboard">
          <ContentSection spacing="lg">
            <div className="flex items-center justify-center p-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading...</p>
              </div>
            </div>
          </ContentSection>
        </PageContainer>
      </Layout>
    );
  }

  // Show message if demographics not completed
  if (hasCompletedDemographics === false) {
    return (
      <Layout>
        <PageContainer pageType="dashboard">
          <ContentSection spacing="lg">
            <div className="flex flex-col items-center justify-center p-12 text-center">
              <div className="w-16 h-16 rounded-full bg-amber-100 dark:bg-amber-900/20 flex items-center justify-center mb-4">
                <AlertTriangle className="w-8 h-8 text-amber-600 dark:text-amber-400" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Complete Your Profile</h2>
              <p className="text-muted-foreground mb-6 max-w-md">
                Please complete the demographic questionnaire to access your dashboard and courses.
              </p>
              <Button onClick={() => window.location.reload()}>
                Complete Questionnaire
              </Button>
            </div>
          </ContentSection>
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer pageType="dashboard">
        <ContentSection spacing="lg">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent mb-2">
              Welcome to e4mi
            </h1>
            <p className="text-muted-foreground text-lg">
              Your learning journey starts here
            </p>
          </motion.div>

          {/* Modern Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-blue-200/50 dark:border-blue-800/50"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-xl bg-blue-500/10 dark:bg-blue-400/20 flex items-center justify-center">
                    <BookOpen className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <span className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.totalCourses}</span>
                </div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Programmes</p>
              </div>
            </div>
            <div className="absolute -right-4 -bottom-4 w-20 h-20 bg-blue-500/5 rounded-full"></div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/50 dark:to-orange-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-orange-200/50 dark:border-orange-800/50"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-xl bg-orange-500/10 dark:bg-orange-400/20 flex items-center justify-center">
                    <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <span className="text-2xl font-bold text-orange-900 dark:text-orange-100">{stats.inProgress}</span>
                </div>
                <p className="text-sm font-medium text-orange-700 dark:text-orange-300">In Progress</p>
              </div>
            </div>
            <div className="absolute -right-4 -bottom-4 w-20 h-20 bg-orange-500/5 rounded-full"></div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-green-200/50 dark:border-green-800/50"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-xl bg-green-500/10 dark:bg-green-400/20 flex items-center justify-center">
                    <Star className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.completed}</span>
                </div>
                <p className="text-sm font-medium text-green-700 dark:text-green-300">Completed</p>
              </div>
            </div>
            <div className="absolute -right-4 -bottom-4 w-20 h-20 bg-green-500/5 rounded-full"></div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-purple-200/50 dark:border-purple-800/50"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-xl bg-purple-500/10 dark:bg-purple-400/20 flex items-center justify-center">
                    <GraduationCap className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <span className="text-2xl font-bold text-purple-900 dark:text-purple-100">{stats.enrolled}</span>
                </div>
                <p className="text-sm font-medium text-purple-700 dark:text-purple-300">Enrolled</p>
              </div>
            </div>
            <div className="absolute -right-4 -bottom-4 w-20 h-20 bg-purple-500/5 rounded-full"></div>
          </motion.div>
          </div>

          {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load course data. Please try again later.
          </AlertDescription>
          </Alert>
          )}

          {/* Courses Section */}
          <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-foreground">Your Programmes</h2>
              <div className="text-sm text-muted-foreground">
                {courses.length} {courses.length === 1 ? 'programme' : 'programmes'} available
              </div>
            </div>

            {/* Course Grid */}
            <div className="grid grid-cols-1 gap-6">
              {isLoading ? (
                // Loading skeletons with modern styling
                <div className="space-y-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <CourseCardSkeleton />
                    </motion.div>
                  ))}
                </div>
              ) : courses.length > 0 ? (
                <div className="space-y-6">
                  {courses.map((course, index) => (
                    <motion.div
                      key={course.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <CourseCard course={course} />
                    </motion.div>
                  ))}
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex flex-col items-center justify-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/50 dark:to-gray-800/50 rounded-2xl border border-border/40 text-center"
                >
                  <div className="w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mb-4">
                    <BookOpen className="w-8 h-8 text-primary dark:text-primary-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No programmes available</h3>
                  <p className="text-muted-foreground max-w-md">
                    Check back later for new learning programmes and courses.
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default Dashboard;
