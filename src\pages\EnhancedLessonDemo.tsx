import React from 'react';
import Layout from '../components/Layout';
import LessonContent from '@/components/course/LessonContent';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const EnhancedLessonDemo = () => {
  // Sample content with video to demonstrate enhanced UI
  const sampleContentWithVideo = JSON.stringify({
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    videoTitle: "Sample Video - Enhanced UI Demo",
    mainContent: `# 🎨 Enhanced Lesson Content UI

## ✨ Welcome to the New Experience

This lesson showcases the **enhanced UI improvements** made to the lesson content system, featuring a clean, modern design with improved video responsiveness.

## 🎯 Key Improvements

### 1. Enhanced Video Experience
- **Fully responsive video containers** that adapt to all screen sizes
- **Improved aspect ratio management** (16:9 on desktop, 16:10 on mobile)
- **Modern styling** with rounded corners, shadows, and hover effects
- **Better loading states** with shimmer animations
- **Maximum width constraints** on large screens for optimal viewing

### 2. Clean Modern Design
- **Glass morphism effects** with backdrop blur for modern aesthetics
- **Subtle gradients and shadows** for visual depth
- **Enhanced typography** with better spacing and hierarchy
- **Consistent color scheme** with full dark mode support

### 3. Improved Content Layout
- **Enhanced prose wrapper** with subtle background and borders
- **Better spacing** between all content elements
- **Responsive padding** that adapts to screen size
- **Professional visual hierarchy** throughout

## 📱 Responsive Features

The enhanced design includes:

1. **Mobile-First Approach**
   - Optimized touch targets
   - Appropriate spacing for mobile devices
   - Readable font sizes across all screens

2. **Tablet Optimization**
   - Balanced layout for medium screens
   - Proper video sizing and positioning
   - Enhanced readability

3. **Desktop Enhancement**
   - Maximum content width for optimal reading
   - Hover effects and animations
   - Professional spacing and layout

## 🎨 Visual Enhancements

### Modern Styling
- **Rounded corners** (0.75rem) for all major elements
- **Subtle shadows** with proper depth hierarchy
- **Gradient backgrounds** for visual interest
- **Backdrop blur effects** for modern glass morphism

### Typography Improvements
- **Enhanced font weights** and sizes
- **Better line heights** for improved readability
- **Consistent spacing** using CSS custom properties
- **Professional color contrast** ratios

### Animation & Transitions
- **Smooth entrance animations** with staggered delays
- **Hover effects** on interactive elements
- **Scale transforms** for button interactions
- **Fade transitions** for content loading

## 🔧 Technical Features

### Performance Optimized
- **CSS custom properties** for consistent theming
- **Efficient animations** with hardware acceleration
- **Lazy loading** for media content
- **Minimal bundle impact** with optimized styles

### Accessibility Enhanced
- **Proper semantic structure** with ARIA labels
- **Keyboard navigation** support
- **Screen reader** compatibility
- **High contrast** support for accessibility

## 📊 Content Types Supported

1. **Video Content** - YouTube embeds and direct video files
2. **Rich Markdown** - Full GFM support with enhanced styling
3. **Images** - Responsive images with modern presentation
4. **References** - Collapsible accordion sections
5. **External Resources** - Enhanced call-to-action buttons

## 🎉 Result

The enhanced lesson content UI provides:
- **Professional appearance** that matches modern LMS standards
- **Excellent user experience** across all devices
- **Improved engagement** through better visual design
- **Maintained performance** with optimized code

This represents a significant upgrade to the lesson viewing experience!`,
    referencesContent: `## 📚 Additional Resources

### Technical Documentation
- [CSS Custom Properties Guide](https://developer.mozilla.org/en-US/docs/Web/CSS/Using_CSS_custom_properties)
- [Responsive Design Best Practices](https://web.dev/responsive-web-design-basics/)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

### Design Resources
- [Glass Morphism Design Trends](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)
- [Modern UI Animation Principles](https://material.io/design/motion/understanding-motion.html)
- [Typography in Web Design](https://www.smashingmagazine.com/2020/07/css-techniques-legibility/)

### Performance Optimization
- [CSS Performance Best Practices](https://developer.mozilla.org/en-US/docs/Learn/Performance/CSS)
- [Web Animation Performance](https://developers.google.com/web/fundamentals/design-and-ux/animations/animations-and-performance)
- [Responsive Images Guide](https://developer.mozilla.org/en-US/docs/Learn/HTML/Multimedia_and_embedding/Responsive_images)

### Tools & Libraries
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [Tailwind CSS Utilities](https://tailwindcss.com/docs)
- [React Performance Optimization](https://react.dev/learn/render-and-commit)`
  });

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h2 className="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-200">
            🎨 Enhanced Lesson Content Demo
          </h2>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            This page demonstrates the enhanced lesson content UI with improved video responsiveness, modern styling, and clean design.
          </p>
        </div>
        
        <LessonContent content={sampleContentWithVideo} />
      </PageContainer>
    </Layout>
  );
};

export default EnhancedLessonDemo;
