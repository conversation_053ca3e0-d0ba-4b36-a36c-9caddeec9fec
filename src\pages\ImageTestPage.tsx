import React from 'react';
import Layout from '../components/Layout';
import LessonContent from '@/components/course/LessonContent';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const ImageTestPage = () => {
  const testContent = `# Image Display Test

This page tests whether images are displaying correctly after our fixes.

## Test 1: External Image (Placeholder)

![Test Image 1](https://via.placeholder.com/600x300/e63946/ffffff?text=Test+Image+1)

This should show a red placeholder image.

## Test 2: Another External Image

![Test Image 2](https://via.placeholder.com/400x200/0066cc/ffffff?text=Test+Image+2)

This should show a blue placeholder image.

## Test 3: Supabase Storage Image

![Supabase Test](https://jibspqwieubavucdtccv.supabase.co/storage/v1/object/public/course-images/editor-images/general/1749397228641.jpg)

This should show an image from Supabase storage (if it exists).

## Test 4: Multiple Images in Sequence

![Image A](https://via.placeholder.com/300x150/28a745/ffffff?text=Image+A)

![Image B](https://via.placeholder.com/300x150/ffc107/000000?text=Image+B)

![Image C](https://via.placeholder.com/300x150/dc3545/ffffff?text=Image+C)

## Test Results

If you can see all the images above, then the image display functionality is working correctly!

### What was fixed:

1. ✅ Removed debug information from lesson content
2. ✅ Added Supabase domains to allowed domains in ContentSecurityService
3. ✅ Added specific Supabase URL pattern to SAFE_URL_PATTERNS
4. ✅ Updated MarkdownPreview component to include all necessary domains

### Technical Details

The images are processed through:
- Markdown parsing in MarkdownPreview component
- Content security validation in ContentSecurityService
- CSS styling from unified-lesson-content.css

All images should display with proper styling, rounded corners, shadows, and responsive behavior.
`;

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Image Display Test</h1>
          <p className="text-muted-foreground mb-8">
            This page verifies that the image display fixes are working correctly.
          </p>
          
          <LessonContent content={testContent} />
        </div>
      </PageContainer>
    </Layout>
  );
};

export default ImageTestPage;
