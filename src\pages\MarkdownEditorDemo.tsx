import React, { useState } from 'react';
import { AdvancedMarkdownEditor } from '@/components/ui/advanced-markdown-editor';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getMarkdownStats, validateMarkdown } from '@/lib/markdown-utils';
import { FileText, Eye, Code, Palette, Zap } from 'lucide-react';

const DEMO_CONTENT = `# Professional Lesson Content Demo

Welcome to our **professional-grade** markdown editor designed specifically for educational content creation. This demo showcases the advanced features that make lessons engaging and visually appealing.

## 📚 Course Overview

This lesson demonstrates the comprehensive features available in our advanced markdown editor, designed to create professional, engaging educational content.

### 🎯 Learning Objectives

By the end of this lesson, you will understand:
- How to create professional-looking lesson content
- Advanced formatting techniques for educational materials
- Interactive elements that enhance student engagement
- Best practices for content organization

## 📝 Text Formatting & Typography

Our editor supports comprehensive text formatting options:

- **Bold text** for emphasis and key concepts
- *Italic text* for subtle emphasis and definitions
- ~~Strikethrough~~ for corrections or outdated information
- ==Highlighted text== for important takeaways
- \`inline code\` for technical terms and commands
- <u>Underlined text</u> for additional emphasis

### Professional Lists

#### Bullet Points for Key Concepts
- Primary learning objectives
- Important terminology
- Key takeaways
  - Supporting details
  - Additional context
    - Deeper explanations
    - Examples and illustrations

#### Numbered Steps for Procedures
1. **Preparation Phase**
   - Gather required materials
   - Set up your workspace
2. **Implementation Phase**
   - Follow the step-by-step process
   - Monitor progress carefully
3. **Review Phase**
   - Evaluate results
   - Document lessons learned

## ✅ Interactive Task Lists

Track your progress through the lesson with interactive checkboxes:

- [x] Read the introduction and objectives
- [x] Review text formatting options
- [ ] Complete the practice exercises
- [ ] Submit your assignment
- [ ] Participate in the discussion forum
  - [ ] Post your initial response
  - [ ] Reply to at least two classmates
  - [ ] Engage in meaningful dialogue

## 📊 Professional Data Tables

Our tables are designed for clarity and professional presentation:

| Learning Module | Duration | Difficulty | Prerequisites |
|-----------------|----------|------------|---------------|
| Introduction to Concepts | 45 minutes | Beginner | None |
| Practical Applications | 90 minutes | Intermediate | Module 1 |
| Advanced Techniques | 120 minutes | Advanced | Modules 1-2 |
| Final Project | 180 minutes | Expert | All previous |

| Assessment Type | Weight | Due Date | Format |
|----------------|--------|----------|---------|
| Quiz 1 | 15% | Week 3 | Multiple Choice |
| Assignment 1 | 25% | Week 5 | Written Report |
| Midterm Exam | 30% | Week 8 | Comprehensive |
| Final Project | 30% | Week 12 | Presentation |

## 💻 Code Examples with Syntax Highlighting

### JavaScript Example
\`\`\`javascript
// Professional code example with proper formatting
class LearningModule {
  constructor(title, duration, difficulty) {
    this.title = title;
    this.duration = duration;
    this.difficulty = difficulty;
    this.completed = false;
  }

  markComplete() {
    this.completed = true;
    console.log(\`Module "\${this.title}" completed!\`);
  }

  getProgress() {
    return {
      title: this.title,
      status: this.completed ? 'Completed' : 'In Progress',
      timeSpent: this.duration
    };
  }
}

// Usage example
const module1 = new LearningModule('Introduction', '45 minutes', 'Beginner');
module1.markComplete();
\`\`\`

### Python Data Analysis
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt

# Load and analyze student performance data
def analyze_student_performance(data_file):
    """
    Analyze student performance metrics and generate insights.

    Args:
        data_file (str): Path to the CSV file containing student data

    Returns:
        dict: Analysis results with key metrics
    """
    df = pd.read_csv(data_file)

    # Calculate key metrics
    metrics = {
        'average_score': df['score'].mean(),
        'pass_rate': (df['score'] >= 70).mean() * 100,
        'top_performers': df[df['score'] >= 90]['student_name'].tolist()
    }

    # Generate visualization
    plt.figure(figsize=(10, 6))
    plt.hist(df['score'], bins=20, alpha=0.7, color='skyblue')
    plt.title('Distribution of Student Scores')
    plt.xlabel('Score')
    plt.ylabel('Frequency')
    plt.show()

    return metrics

# Example usage
results = analyze_student_performance('student_scores.csv')
print(f"Class average: {results['average_score']:.2f}")
print(f"Pass rate: {results['pass_rate']:.1f}%")
\`\`\`

## 📋 Professional Callouts & Alerts

### Information Callouts
> [!INFO]
> **Important Information**: This callout provides essential context and background information that students need to understand before proceeding with the lesson content.

> [!NOTE]
> **Study Note**: Take special note of this information as it will be covered in the upcoming assessment. Consider adding this to your study notes.

### Warning & Caution Callouts
> [!WARNING]
> **Attention Required**: This section contains critical information that could affect your understanding of subsequent topics. Please read carefully.

> [!CAUTION]
> **Proceed with Care**: The following steps require careful attention to detail. Double-check your work before moving forward.

### Success & Achievement Callouts
> [!SUCCESS]
> **Well Done!** You've successfully completed this section. Your understanding of these concepts will serve as a foundation for more advanced topics.

### Error Prevention
> [!ERROR]
> **Common Mistake**: Many students struggle with this concept. Avoid the common pitfall of confusing correlation with causation in your analysis.

### Tips & Best Practices
> [!TIP]
> **Pro Tip**: Use keyboard shortcuts to speed up your workflow. Press Ctrl+B for bold, Ctrl+I for italic, and Ctrl+K to insert links quickly.

> [!IMPORTANT]
> **Key Takeaway**: This concept is fundamental to understanding the entire course. Make sure you fully grasp it before moving on.

## 🔽 Expandable Content Sections

<details>
<summary>📖 Additional Reading Materials</summary>

This section contains supplementary materials that provide deeper insights into the topic:

### Recommended Books
- "The Art of Learning" by Josh Waitzkin
- "Make It Stick" by Peter Brown
- "Peak: Secrets from the New Science of Expertise" by Anders Ericsson

### Online Resources
- Khan Academy courses on related topics
- Coursera specializations
- MIT OpenCourseWare lectures

### Research Papers
- Recent studies on effective learning techniques
- Peer-reviewed articles on educational psychology
- Meta-analyses of teaching methodologies

</details>

<details>
<summary>🧪 Practice Exercises</summary>

Complete these exercises to reinforce your understanding:

### Exercise 1: Concept Application
Apply the concepts learned in this lesson to solve the following problem:

**Scenario**: You are designing a learning management system for a university. How would you implement the features discussed in this lesson?

**Requirements**:
- Create a user interface mockup
- Define the data structure
- Outline the key functionalities

### Exercise 2: Critical Analysis
Analyze the effectiveness of different content presentation methods:

1. Compare traditional text-based content with interactive elements
2. Evaluate the impact of visual aids on learning retention
3. Propose improvements to existing educational platforms

</details>

<details>
<summary>🎯 Assessment Criteria</summary>

Your work will be evaluated based on the following criteria:

| Criterion | Excellent (4) | Good (3) | Satisfactory (2) | Needs Improvement (1) |
|-----------|---------------|----------|------------------|----------------------|
| Understanding | Demonstrates deep comprehension | Shows good understanding | Basic understanding evident | Limited understanding |
| Application | Skillfully applies concepts | Applies concepts well | Some application shown | Minimal application |
| Analysis | Thorough and insightful | Good analytical skills | Basic analysis | Superficial analysis |
| Communication | Clear and professional | Well communicated | Adequate communication | Poor communication |

</details>

## 🖼️ Professional Image Handling

Images are automatically optimized for professional presentation with captions and zoom functionality:

![Learning Analytics Dashboard](https://via.placeholder.com/800x400/2563eb/ffffff?text=Learning+Analytics+Dashboard "Interactive dashboard showing student progress and engagement metrics")

![Course Structure Diagram](https://via.placeholder.com/600x350/059669/ffffff?text=Course+Structure "Hierarchical view of course modules and lessons")

## 💬 Professional Blockquotes

> "The beautiful thing about learning is that no one can take it away from you. Education is the most powerful weapon which you can use to change the world."
>
> — Nelson Mandela

> "Tell me and I forget, teach me and I may remember, involve me and I learn."
>
> — Benjamin Franklin

---

## 🚀 Advanced Features Summary

This professional lesson preview system includes:

- 🎨 **Beautiful Typography** with proper hierarchy and spacing
- 📊 **Interactive Tables** with hover effects and professional styling
- 🔽 **Collapsible Sections** for organized content delivery
- 💡 **Professional Callouts** with contextual icons and styling
- ✅ **Interactive Task Lists** for student engagement
- 🖼️ **Enhanced Image Handling** with captions and zoom functionality
- 💻 **Syntax-Highlighted Code** with copy functionality
- 📱 **Responsive Design** that works on all devices
- 🌙 **Dark Mode Support** for comfortable viewing
- ♿ **Accessibility Features** for inclusive learning

## 📚 Next Steps

1. **Practice**: Try creating your own lesson content using these features
2. **Experiment**: Test different combinations of elements
3. **Feedback**: Share your experience with the development team
4. **Iterate**: Continuously improve your content based on student feedback

Ready to create professional, engaging educational content? Start building your lessons today!`;

export default function MarkdownEditorDemo() {
  const [content, setContent] = useState(DEMO_CONTENT);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const stats = getMarkdownStats(content);
  const validation = validateMarkdown(content);

  const features = [
    {
      icon: <FileText className="h-5 w-5" />,
      title: "GitHub Flavored Markdown",
      description: "Full GFM support including tables, task lists, and strikethrough"
    },
    {
      icon: <Eye className="h-5 w-5" />,
      title: "Live Preview",
      description: "Real-time preview with split-pane and full-screen modes"
    },
    {
      icon: <Code className="h-5 w-5" />,
      title: "Syntax Highlighting",
      description: "Code blocks with syntax highlighting for 20+ languages"
    },
    {
      icon: <Palette className="h-5 w-5" />,
      title: "Obsidian-inspired Design",
      description: "Clean, readable typography with dark mode support"
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: "Advanced Features",
      description: "Image upload, callouts, collapsible sections, and more"
    }
  ];

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Advanced Markdown Editor</h1>
        <p className="text-xl text-muted-foreground mb-6">
          A powerful, Notion-like markdown editor with GitHub Flavored Markdown support, 
          live preview, and advanced features for content creation.
        </p>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {features.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg text-primary">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats and Validation */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Content Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Words:</span>
                <Badge variant="secondary">{stats.wordCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Characters:</span>
                <Badge variant="secondary">{stats.characterCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Headings:</span>
                <Badge variant="secondary">{stats.headingCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Images:</span>
                <Badge variant="secondary">{stats.imageCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Code Blocks:</span>
                <Badge variant="secondary">{stats.codeBlockCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Reading Time:</span>
                <Badge variant="secondary">{stats.readingTimeMinutes} min</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Content Validation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <span>Status:</span>
                <Badge variant={validation.valid ? "default" : "destructive"}>
                  {validation.valid ? "Valid" : "Issues Found"}
                </Badge>
              </div>
              
              {validation.errors.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-destructive">Errors:</span>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {validation.warnings.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-warning">Warnings:</span>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    {validation.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {validation.valid && validation.warnings.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  No issues found. Content is well-formatted!
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        <Separator className="mb-8" />
      </div>

      {/* Editor */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold">Try the Editor</h2>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setContent(DEMO_CONTENT)}
            >
              Reset Demo Content
            </Button>
            <Button
              variant="outline"
              onClick={() => setContent('')}
            >
              Clear Editor
            </Button>
          </div>
        </div>

        <AdvancedMarkdownEditor
          initialContent={content}
          onChange={setContent}
          placeholder="Start writing your markdown content..."
          minHeight={600}
          showToolbar={true}
          showPreview={true}
          enableCodeCopy={true}
          enableExport={true}
          fullscreen={isFullscreen}
          onFullscreenChange={setIsFullscreen}
          className="border-2 border-dashed border-border"
        />
      </div>

      {/* Usage Instructions */}
      <div className="mt-12">
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
            <CardDescription>
              Get started with the advanced markdown editor
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Keyboard Shortcuts</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+B</kbd> - Bold</div>
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+I</kbd> - Italic</div>
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+U</kbd> - Underline</div>
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+Z</kbd> - Undo</div>
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+Y</kbd> - Redo</div>
                <div><kbd className="px-2 py-1 bg-muted rounded">Ctrl+Shift+D</kbd> - Details</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Special Features</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Click images to zoom in</li>
                <li>• Hover over code blocks to see copy button</li>
                <li>• Use the toolbar for quick formatting</li>
                <li>• Switch between editor, split, and preview modes</li>
                <li>• Toggle dark mode with the theme button</li>
                <li>• Use fullscreen mode for distraction-free writing</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
