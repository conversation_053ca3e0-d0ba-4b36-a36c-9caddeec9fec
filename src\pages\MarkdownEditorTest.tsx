/**
 * Test page for the Unified Markdown Editor
 * Demonstrates all features and fixes
 */

import React, { useState } from 'react';
import { UnifiedMarkdownEditor } from '@/components/ui/unified-markdown-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Copy, Download, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { toast } from 'sonner';

const MarkdownEditorTest: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState(`# Unified Markdown Editor Test

Welcome to the **comprehensive test** of our unified TipTap Markdown editor! This editor now supports all GitHub Flavored Markdown features.

## Text Formatting

Here's some text with different formatting:
- **Bold text**
- *Italic text*
- ~~Strikethrough text~~
- ==Highlighted text==
- <u>Underlined text</u>
- \`inline code\`

## Headers

# H1 Header
## H2 Header  
### H3 Header
#### H4 Header
##### H5 Header
###### H6 Header

## Lists

### Bullet Lists
- First item
- Second item
  - Nested item
  - Another nested item
- Third item

### Numbered Lists
1. First numbered item
2. Second numbered item
   1. Nested numbered item
   2. Another nested item
3. Third numbered item

### Task Lists
- [x] Completed task
- [x] Another completed task
- [ ] Incomplete task
- [ ] Another incomplete task
  - [x] Nested completed task
  - [ ] Nested incomplete task

## Code Blocks

Here's a JavaScript code block:

\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to the unified editor!\`;
}

greetUser('Developer');
\`\`\`

And a Python example:

\`\`\`python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

print(calculate_fibonacci(10))
\`\`\`

## Tables

| Feature | Status | Notes |
|---------|--------|-------|
| **Bold text** | ✅ Working | Properly formatted |
| *Italic text* | ✅ Working | Looks great |
| ~~Strikethrough~~ | ✅ Working | Now supported! |
| Task lists | ✅ Working | Interactive checkboxes |
| Tables | ✅ Working | Responsive design |
| Code blocks | ✅ Working | Syntax highlighting |

## Blockquotes

> This is a blockquote with some important information.
> 
> It can span multiple lines and contains **formatted text**.

## Links and Images

Here's a [link to GitHub](https://github.com) and here's an image:

![Sample Image](https://via.placeholder.com/400x200/E63946/FFFFFF?text=Unified+Editor)

## Horizontal Rules

---

## Callouts (Custom Extension)

> [!INFO] Information Callout
> This is an informational callout with important details.

> [!WARNING] Warning Callout  
> This is a warning callout to draw attention.

> [!SUCCESS] Success Callout
> This indicates something was completed successfully.

> [!ERROR] Error Callout
> This indicates an error or problem.

## Collapsible Sections

<details>
<summary>Click to expand this section</summary>

This content is hidden by default and can be expanded by clicking the summary.

You can include any markdown content here:
- Lists
- **Formatted text**
- \`Code\`
- And more!

</details>

## Advanced Features

### Math Expressions (Future)
When LaTeX support is added, you'll be able to write:
$$E = mc^2$$

### Diagrams (Future)
Mermaid diagram support coming soon:
\`\`\`mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
\`\`\`

---

**Test completed!** The unified editor now supports all major GitHub Flavored Markdown features with a consistent, professional interface.`);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdownContent);
      toast.success('Markdown copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadMarkdown = () => {
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'unified-editor-test.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Markdown file downloaded!');
  };

  const features = [
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "GitHub Flavored Markdown",
      description: "Full GFM support including tables, task lists, and strikethrough",
      status: "✅ Implemented"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "Live Preview",
      description: "Real-time preview with split-pane and full-screen modes",
      status: "✅ Implemented"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "Syntax Highlighting",
      description: "Code blocks with syntax highlighting for 20+ languages",
      status: "✅ Implemented"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "Image Upload",
      description: "Drag & drop image upload with Supabase storage integration",
      status: "✅ Implemented"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "Custom Extensions",
      description: "Details/summary collapsible sections and callout blocks",
      status: "✅ Implemented"
    },
    {
      icon: <AlertTriangle className="h-5 w-5 text-yellow-600" />,
      title: "Math Support",
      description: "LaTeX math expressions (planned for future release)",
      status: "🔄 Planned"
    }
  ];

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Unified Markdown Editor Test</h1>
        <p className="text-lg text-muted-foreground mb-6">
          Comprehensive test of the unified TipTap Markdown editor with all GitHub Flavored Markdown features.
        </p>
        
        <div className="flex flex-wrap gap-2 mb-6">
          <Badge variant="default">TipTap v2.12.0</Badge>
          <Badge variant="secondary">GitHub Flavored Markdown</Badge>
          <Badge variant="outline">Live Preview</Badge>
          <Badge variant="outline">Image Upload</Badge>
          <Badge variant="outline">Custom Extensions</Badge>
        </div>
      </div>

      <Tabs defaultValue="editor" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="editor">Editor Test</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="themes">Themes</TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-6">
          {/* Editor Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Unified Markdown Editor</CardTitle>
                  <CardDescription>
                    Test all features including GFM support, custom extensions, and live preview.
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyToClipboard}
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy Markdown
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadMarkdown}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <UnifiedMarkdownEditor
                initialContent={markdownContent}
                onChange={setMarkdownContent}
                placeholder="Start testing the unified editor..."
                minHeight={600}
                autoFocus={false}
                showToolbar={true}
                showPreview={true}
                mode="editor"
                theme="github"
                courseId="test-course"
                moduleId="test-module"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {features.map((feature, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    {feature.icon}
                    <div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                      <CardDescription>{feature.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Badge variant={feature.status.includes('✅') ? 'default' : 'secondary'}>
                    {feature.status}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="themes" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>GitHub Theme</CardTitle>
                <CardDescription>Clean, professional GitHub-style editor</CardDescription>
              </CardHeader>
              <CardContent>
                <UnifiedMarkdownEditor
                  initialContent="# GitHub Theme\n\nThis is the **GitHub-style** theme with clean typography and familiar styling."
                  minHeight={200}
                  showToolbar={false}
                  showPreview={false}
                  theme="github"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Obsidian Theme</CardTitle>
                <CardDescription>Obsidian-inspired theme for note-taking</CardDescription>
              </CardHeader>
              <CardContent>
                <UnifiedMarkdownEditor
                  initialContent="# Obsidian Theme\n\nThis is the **Obsidian-inspired** theme with enhanced readability and modern styling."
                  minHeight={200}
                  showToolbar={false}
                  showPreview={false}
                  theme="obsidian"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Minimal Theme</CardTitle>
                <CardDescription>Clean, distraction-free minimal theme</CardDescription>
              </CardHeader>
              <CardContent>
                <UnifiedMarkdownEditor
                  initialContent="# Minimal Theme\n\nThis is the **minimal** theme with clean, distraction-free styling perfect for focused writing."
                  minHeight={200}
                  showToolbar={false}
                  showPreview={false}
                  theme="minimal"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Status Summary */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Implementation Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold text-green-600 mb-2">✅ Completed Features</h4>
              <ul className="space-y-1 text-sm">
                <li>• Strikethrough extension installed</li>
                <li>• Custom Details/Summary extension</li>
                <li>• Custom Callout extension</li>
                <li>• Unified markdown serializer</li>
                <li>• Consolidated CSS styling</li>
                <li>• Image upload functionality</li>
                <li>• Live preview with split mode</li>
                <li>• Multiple theme support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-600 mb-2">🔄 Next Steps</h4>
              <ul className="space-y-1 text-sm">
                <li>• Replace existing editor implementations</li>
                <li>• Add comprehensive test suite</li>
                <li>• Implement LaTeX math support</li>
                <li>• Add Mermaid diagram support</li>
                <li>• Performance optimization</li>
                <li>• Accessibility improvements</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MarkdownEditorTest;
