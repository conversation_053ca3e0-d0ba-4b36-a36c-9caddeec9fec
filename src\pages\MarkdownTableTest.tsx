import React from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';

const testMarkdown = `# Table Parsing Test

This is a test to verify that markdown tables are being parsed correctly.

## Test Table

| SIZES | COLOR | FLOW RATE | INDICATIONS |
|-------|-------|-----------|-------------|
| 14 G | Orange | Faster flow | Trauma, surgical procedures |
| 16 G | Grey | Faster flow | Trauma, surgical procedures |
| 18 G | Green | Medium flow | Trauma, quick blood transfusion |
| 20 G | Pink | Medium flow | Normal IV or blood transfusion |
| 22 G | Blue | Slower flow | Children, older adults |
| 24 G | Yellow | Slower flow rate | Neonates, children, old elderly |
| 26 G | Purple/violet | Slower flow rate | Neonates |

## Another Test Table

| Feature | Description | Status |
|---------|-------------|--------|
| **Bold text** | This should be bold | ✅ Working |
| *Italic text* | This should be italic | ✅ Working |
| \`Code text\` | This should be code | ✅ Working |
| [Link](https://example.com) | This should be a link | ✅ Working |

## Text After Table

This text should appear normally after the table.
`;

export default function MarkdownTableTest() {
  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">Markdown Table Parsing Test</h1>
        
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold mb-3">Raw Markdown:</h2>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-sm overflow-x-auto">
              {testMarkdown}
            </pre>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-3">Rendered Output:</h2>
            <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4">
              <MarkdownPreview 
                content={testMarkdown}
                className="professional-prose"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
