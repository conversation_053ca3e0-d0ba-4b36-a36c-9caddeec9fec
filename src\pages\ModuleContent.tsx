
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Layout from '../components/Layout';
import CourseHeader from '../components/course/CourseHeader';
import CourseContent from '../components/course/CourseContent';
import CourseDetailPage from '../components/course/CourseDetailPage';
import FinishCourseButton from '../components/course/FinishCourseButton';
import { fetchCourseBySlug, fetchCourseModules } from '../services/course';
import { Skeleton } from '@/components/ui/skeleton';
import { ModuleContentSkeleton } from '@/components/ui/loading-skeleton';
import { useAuth } from '@/context/AuthContext';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';
import { Button } from '@/components/ui/button';
import { Play } from 'lucide-react';
import { enrollInCourse, getEnrollmentStatus } from '@/services/course/enrollmentApi';
import { toast } from 'sonner';


const ModuleContent = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [hasStarted, setHasStarted] = useState(false);

  // Fetch course data
  const { data: course, isLoading: isLoadingCourse, error: courseError } =
    useQuery({
      queryKey: ['course', courseId],
      queryFn: async () => {
        console.log('Fetching course by slug:', courseId);
        const result = await fetchCourseBySlug(courseId || '');
        console.log('Course fetch result:', result);
        return result;
      },
      enabled: !!courseId,
    });

  // Fetch course modules with lessons, passing user ID if available
  const { data: modules, isLoading: isLoadingModules, error: modulesError } =
    useQuery({
      queryKey: ['courseModules', course?.id, user?.id],
      queryFn: async () => {
        console.log('Fetching modules for course ID:', course?.id);
        const result = await fetchCourseModules(course?.id || '', user?.id);
        console.log('Modules fetch result:', result);
        return result;
      },
      enabled: !!course?.id,
    });

  // Fetch enrollment status
  const { data: enrollment } =
    useQuery({
      queryKey: ['enrollment', course?.id, user?.id],
      queryFn: () => getEnrollmentStatus(course?.id || '', user?.id || ''),
      enabled: !!course?.id && !!user?.id,
    });

  // Update hasStarted based on enrollment data
  React.useEffect(() => {
    if (enrollment && enrollment.status !== 'not_started') {
      setHasStarted(true);
    }
  }, [enrollment]);

  // Start course mutation
  const startCourseMutation = useMutation({
    mutationFn: () => enrollInCourse(course?.id || '', user?.id || '', 'in_progress'),
    onSuccess: () => {
      setHasStarted(true);
      toast.success('Successfully enrolled in course!');

      // Refresh data
      queryClient.invalidateQueries({
        queryKey: ['enrollment', course?.id, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['courseModules', course?.id, user?.id],
      });
    },
    onError: (error) => {
      console.error('Error starting course:', error);
      toast.error('Failed to start course. Please try again.');
    }
  });

  const handleStartCourse = () => {
    if (!user) {
      toast.error('Please log in to start this course');
      return;
    }

    startCourseMutation.mutate();
  };

  const isLoading = isLoadingCourse || isLoadingModules;
  const error = courseError || modulesError;

  if (error || (!isLoading && !course)) {
    return (
      <Layout>
        <div className="container max-w-4xl mx-auto px-4 sm:px-6">
          <div className="text-center py-6 md:py-8">
            <h2 className="text-xl md:text-2xl font-bold text-red-600">Error loading course</h2>
            <p className="mt-2 text-sm md:text-base text-gray-600 dark:text-gray-400">
              {error
                ? "There was a problem loading the course content. Please try again later."
                : `Course "${courseId}" not found. Please check the URL and try again.`
              }
            </p>
            <p className="mt-4 text-sm text-gray-500">
              Course ID: {courseId || 'Not provided'}
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer pageType="module" className="animate-fade-in">
        <ContentSection spacing="md">
          {isLoading ? (
          // Loading skeleton using standardized component
          <ModuleContentSkeleton />
        ) : (
          <>
            {/* Use the new CourseDetailPage component */}
            <CourseDetailPage
              course={{
                id: course?.id || courseId || '',
                slug: course?.slug || '',
                title: course?.title || '',
                description: course?.description || '',
                instructor: course?.instructor || '',
                total_modules: course?.total_modules || 0,
                completed_modules: course?.completed_modules || 0,
                image_url: course?.image_url || (course as any)?.image || ''
              }}
              modules={modules || []}
              isEnrolled={hasStarted}
              onStartCourse={handleStartCourse}
              isStarting={startCourseMutation.isPending}
            />

            {/* Finish Course Button */}
            {user && hasStarted && course && (
              <div className="mt-4 md:mt-6">
                <FinishCourseButton
                  courseId={course.id}
                  userId={user.id}
                  modules={modules || []}
                  isEnrolled={hasStarted}
                  courseName={course.title}
                />
              </div>
            )}
          </>
          )}
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default ModuleContent;
