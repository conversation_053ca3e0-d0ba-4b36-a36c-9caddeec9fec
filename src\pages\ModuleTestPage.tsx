import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Layout from '../components/Layout';
import { useAuth } from '@/context/AuthContext';
import { getModuleTestByType, getUserTestResponse } from '@/services/module-test/moduleTestService';
import ModuleTest from '@/components/module/ModuleTest';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, ClipboardCheck, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const ModuleTestPage: React.FC = () => {
  const { courseId, moduleId } = useParams<{ courseId: string; moduleId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const [testType, setTestType] = useState<'pre_test' | 'post_test' | null>(null);

  // Fetch module info
  const { data: module, isLoading: isLoadingModule } = useQuery({
    queryKey: ['module', moduleId],
    queryFn: async () => {
      if (!moduleId) throw new Error('Module ID is required');

      const { data, error } = await supabase
        .from('modules')
        .select('id, title, module_number')
        .eq('id', moduleId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!moduleId
  });

  // Fetch pre-test
  const { data: preTest, isLoading: isLoadingPreTest } = useQuery({
    queryKey: ['module-pre-test', moduleId],
    queryFn: async () => {
      if (!moduleId) return null;
      return getModuleTestByType(moduleId, 'pre_test');
    },
    enabled: !!moduleId
  });

  // Fetch post-test
  const { data: postTest, isLoading: isLoadingPostTest } = useQuery({
    queryKey: ['module-post-test', moduleId],
    queryFn: async () => {
      if (!moduleId) return null;
      return getModuleTestByType(moduleId, 'post_test');
    },
    enabled: !!moduleId
  });

  // Check if user has completed pre-test
  const { data: preTestResponse } = useQuery({
    queryKey: ['pre-test-response', moduleId, user?.id],
    queryFn: async () => {
      if (!moduleId || !user?.id || !preTest) return null;
      return getUserTestResponse(preTest.id, user.id);
    },
    enabled: !!moduleId && !!user?.id && !!preTest
  });

  // Check if user has completed post-test
  const { data: postTestResponse } = useQuery({
    queryKey: ['post-test-response', moduleId, user?.id],
    queryFn: async () => {
      if (!moduleId || !user?.id || !postTest) return null;
      return getUserTestResponse(postTest.id, user.id);
    },
    enabled: !!moduleId && !!user?.id && !!postTest
  });

  const isLoading = isLoadingModule || isLoadingPreTest || isLoadingPostTest;

  const handleTestComplete = () => {
    toast({
      title: "Test Completed",
      description: "Your responses have been recorded successfully.",
    });

    // Navigate back to the modules page
    navigate(`/course/${courseId}/modules`);
  };

  const handleBackToCourse = () => {
    navigate(`/course/${courseId}/modules`);
  };

  if (isLoading) {
    return (
      <Layout>
        <PageContainer pageType="default">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center h-64 space-y-4"
          >
            <div className="relative">
              <Loader2 className="w-12 h-12 animate-spin text-primary" />
              <div className="absolute inset-0 w-12 h-12 border-2 border-primary/20 rounded-full"></div>
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-lg font-medium text-foreground">Loading Module Tests</h3>
              <p className="text-sm text-muted-foreground">Please wait while we prepare your assessment...</p>
            </div>
          </motion.div>
        </PageContainer>
      </Layout>
    );
  }

  if (!module) {
    return (
      <Layout>
        <PageContainer pageType="default">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-destructive/20 bg-destructive/5">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="w-6 h-6 text-destructive" />
                </div>
                <CardTitle className="text-destructive">Module Not Found</CardTitle>
                <CardDescription className="text-destructive/80">
                  The requested module could not be found. This might be due to an invalid link or the module may have been removed.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button onClick={handleBackToCourse} size="lg" className="min-w-[140px]">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Modules
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </PageContainer>
      </Layout>
    );
  }

  // Determine which test to show
  let currentTest = null;
  let currentTestType: 'pre_test' | 'post_test' | null = null;

  if (testType) {
    // User has explicitly selected a test type
    currentTest = testType === 'pre_test' ? preTest : postTest;
    currentTestType = testType;
  } else {
    // Auto-determine which test to show
    if (preTest && !preTestResponse) {
      // Show pre-test if it exists and hasn't been completed
      currentTest = preTest;
      currentTestType = 'pre_test';
    } else if (postTest && !postTestResponse) {
      // Show post-test if it exists and hasn't been completed
      currentTest = postTest;
      currentTestType = 'post_test';
    }
  }

  // If showing a test
  if (currentTest && currentTestType) {
    return (
      <Layout>
        <PageContainer pageType="full-width">
          <ModuleTest
            test={currentTest}
            onComplete={handleTestComplete}
            courseId={courseId}
            showNextLessonButton={currentTestType === 'pre_test'}
          />
        </PageContainer>
      </Layout>
    );
  }

  // Show test selection or completion status
  return (
    <Layout>
      <PageContainer pageType="default">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {/* Enhanced Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={handleBackToCourse}
              className="mb-6 hover:bg-secondary/80 transition-colors"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Modules
            </Button>

            <div className="space-y-2">
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                Module {module.module_number}: {module.title}
              </h1>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-sm">
                  Module Assessments
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Complete assessments to track your progress
                </span>
              </div>
            </div>
          </div>

          <div className="grid gap-6 md:gap-8">
          {/* Pre-test */}
          {preTest && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <Card className={cn(
                "transition-all duration-200 hover:shadow-md border-l-4",
                preTestResponse
                  ? "border-l-green-500 bg-green-50/50 dark:bg-green-950/20"
                  : "border-l-yellow-500 hover:border-l-yellow-600"
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "p-2 rounded-lg",
                        preTestResponse
                          ? "bg-green-100 dark:bg-green-900/30"
                          : "bg-yellow-100 dark:bg-yellow-900/30"
                      )}>
                        {preTestResponse ? (
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                        ) : (
                          <ClipboardCheck className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                        )}
                      </div>
                      <div>
                        <CardTitle className="text-lg font-semibold">
                          {preTest.title}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="secondary"
                            className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                          >
                            Pre-Assessment
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {preTest.questions.length} questions
                          </span>
                        </div>
                      </div>
                    </div>

                    {preTestResponse && (
                      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>

                  <CardDescription className="mt-3 text-sm leading-relaxed">
                    {preTest.description || 'Complete this pre-assessment before starting the module to help us understand your current knowledge level.'}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>~{Math.ceil(preTest.questions.length * 1.5)} min</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      {preTestResponse ? (
                        <Button
                          variant="outline"
                          onClick={() => setTestType('pre_test')}
                          size="sm"
                        >
                          Review Responses
                        </Button>
                      ) : (
                        <Button
                          onClick={() => setTestType('pre_test')}
                          size="sm"
                          className="min-w-[120px]"
                        >
                          Start Assessment
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Post-test */}
          {postTest && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <Card className={cn(
                "transition-all duration-200 hover:shadow-md border-l-4",
                postTestResponse
                  ? "border-l-green-500 bg-green-50/50 dark:bg-green-950/20"
                  : "border-l-blue-500 hover:border-l-blue-600"
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "p-2 rounded-lg",
                        postTestResponse
                          ? "bg-green-100 dark:bg-green-900/30"
                          : "bg-blue-100 dark:bg-blue-900/30"
                      )}>
                        {postTestResponse ? (
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                        ) : (
                          <ClipboardCheck className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        )}
                      </div>
                      <div>
                        <CardTitle className="text-lg font-semibold">
                          {postTest.title}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="secondary"
                            className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                          >
                            Post-Assessment
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {postTest.questions.length} questions
                          </span>
                        </div>
                      </div>
                    </div>

                    {postTestResponse && (
                      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>

                  <CardDescription className="mt-3 text-sm leading-relaxed">
                    {postTest.description || 'Complete this post-assessment after finishing all module lessons to measure your learning progress.'}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>~{Math.ceil(postTest.questions.length * 1.5)} min</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      {postTestResponse ? (
                        <Button
                          variant="outline"
                          onClick={() => setTestType('post_test')}
                          size="sm"
                        >
                          Review Responses
                        </Button>
                      ) : (
                        <Button
                          onClick={() => setTestType('post_test')}
                          size="sm"
                          className="min-w-[120px]"
                        >
                          Start Assessment
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* No tests available */}
          {!preTest && !postTest && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <Card className="border-dashed border-2 border-muted-foreground/20">
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
                    <ClipboardCheck className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <CardTitle className="text-xl">No Assessments Available</CardTitle>
                  <CardDescription className="text-base leading-relaxed max-w-md mx-auto">
                    This module doesn't have any pre-assessments or post-assessments configured yet.
                    You can proceed directly to the module lessons.
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button onClick={handleBackToCourse} size="lg" className="min-w-[160px]">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Modules
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}
          </div>
        </motion.div>
      </PageContainer>
    </Layout>
  );
};

export default ModuleTestPage;
