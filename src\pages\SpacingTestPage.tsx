import React from 'react';
import Layout from '../components/Layout';
import LessonContent from '@/components/course/LessonContent';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const SpacingTestPage = () => {
  const testContent = `# Lesson Content Spacing Test

This is a comprehensive test of the lesson content spacing improvements. We'll test various elements to ensure proper spacing.

## Paragraph Spacing Test

This is the first paragraph. It should have proper spacing above and below. The text should be justified and easy to read with good line height.

This is the second paragraph. Notice the spacing between this paragraph and the previous one. It should be consistent and provide good visual separation.

This is the third paragraph with some **bold text** and *italic text* to test inline formatting. The spacing should remain consistent regardless of inline formatting.

## List Spacing Test

Here's an unordered list to test list spacing:

- First list item with proper spacing
- Second list item that might be longer to test how the spacing works with wrapped text content
- Third list item
  - Nested list item to test indentation
  - Another nested item
    - Third level nesting
- Back to first level

Here's an ordered list:

1. First numbered item
2. Second numbered item with longer content to see how it wraps and maintains proper spacing
3. Third numbered item
   1. Nested numbered item
   2. Another nested numbered item
4. Back to main list

## Heading Hierarchy Test

### This is an H3 Heading

Content after H3 heading should have proper spacing. The heading should have adequate space above and below.

#### This is an H4 Heading

Content after H4 heading to test the spacing hierarchy.

##### This is an H5 Heading

And some content after H5.

## Code Block Test

Here's some inline \`code\` within a paragraph.

Here's a code block:

\`\`\`javascript
function testSpacing() {
  console.log("This code block should have proper margins");
  return "spacing test";
}
\`\`\`

## Blockquote Test

> This is a blockquote to test the spacing and styling.
> It should have proper margins and padding.
> 
> Multiple paragraphs in blockquotes should also work properly.

## Image Test

![Test Image](https://via.placeholder.com/600x300/e63946/ffffff?text=Spacing+Test+Image)

## Table Test

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1, Col 1 | Row 1, Col 2 | Row 1, Col 3 |
| Row 2, Col 1 | Row 2, Col 2 | Row 2, Col 3 |
| Row 3, Col 1 | Row 3, Col 2 | Row 3, Col 3 |

## Horizontal Rule Test

Content before horizontal rule.

---

Content after horizontal rule.

## Mixed Content Test

This section tests how different elements work together:

### Subheading

A paragraph followed by a list:

- List item one
- List item two

Then a code block:

\`\`\`css
.test-class {
  margin: 1rem 0;
  padding: 1rem;
}
\`\`\`

And finally another paragraph to complete the test. This should demonstrate that all elements have consistent and proper spacing throughout the lesson content.

## Conclusion

This test page demonstrates the improved spacing system for lesson content. All elements should have consistent, readable spacing that enhances the learning experience.`;

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h2 className="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-200">
            Spacing Test Page
          </h2>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            This page tests the lesson content spacing improvements. Check that all elements have proper spacing and visual hierarchy.
          </p>
        </div>
        
        <LessonContent content={testContent} />
      </PageContainer>
    </Layout>
  );
};

export default SpacingTestPage;
