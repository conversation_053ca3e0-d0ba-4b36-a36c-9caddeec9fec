/**
 * Table Rendering Test Page
 * Tests table rendering consistency between editor and lesson content display
 */

import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { UnifiedMarkdownEditor } from '@/components/ui/unified-markdown-editor';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import LessonContent from '@/components/course/LessonContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, Download, CheckCircle, AlertTriangle, Table as TableIcon } from 'lucide-react';
import { toast } from 'sonner';

const TableRenderingTest: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState(`# Table Rendering Test

This page tests table rendering consistency between the unified markdown editor and lesson content display.

## Simple Table

| Feature | Status | Notes |
|---------|--------|-------|
| **Bold text** | ✅ Working | Properly formatted |
| *Italic text* | ✅ Working | Looks great |
| ~~Strikethrough~~ | ✅ Working | Now supported! |
| \`Code\` | ✅ Working | Inline code works |

## Complex Table with Alignment

| Left Aligned | Center Aligned | Right Aligned | Mixed Content |
|:-------------|:--------------:|--------------:|:--------------|
| This is left | This is center | This is right | **Bold** |
| Another row | *Italic text* | 123.45 | \`code\` |
| Long content that might wrap | ~~Strike~~ | $99.99 | [Link](https://example.com) |

## Table with Code and Links

| Language | Example | Documentation |
|----------|---------|---------------|
| JavaScript | \`console.log('Hello')\` | [MDN](https://developer.mozilla.org) |
| Python | \`print("Hello")\` | [Python.org](https://python.org) |
| TypeScript | \`const msg: string = "Hello"\` | [TypeScript](https://typescriptlang.org) |

## Large Table for Responsive Testing

| Column 1 | Column 2 | Column 3 | Column 4 | Column 5 | Column 6 |
|----------|----------|----------|----------|----------|----------|
| Data 1.1 | Data 1.2 | Data 1.3 | Data 1.4 | Data 1.5 | Data 1.6 |
| Data 2.1 | Data 2.2 | Data 2.3 | Data 2.4 | Data 2.5 | Data 2.6 |
| Data 3.1 | Data 3.2 | Data 3.3 | Data 3.4 | Data 3.5 | Data 3.6 |
| Data 4.1 | Data 4.2 | Data 4.3 | Data 4.4 | Data 4.5 | Data 4.6 |

## Table with Special Characters

| Symbol | Unicode | HTML Entity | Description |
|--------|---------|-------------|-------------|
| © | U+00A9 | \&copy; | Copyright |
| ® | U+00AE | \&reg; | Registered |
| ™ | U+2122 | \&trade; | Trademark |
| € | U+20AC | \&euro; | Euro |
| £ | U+00A3 | \&pound; | Pound |

## Nested Content Table

| Type | Example | Result |
|------|---------|--------|
| **Bold** | \`**text**\` | **text** |
| *Italic* | \`*text*\` | *text* |
| ~~Strike~~ | \`~~text~~\` | ~~text~~ |
| Code | \`\\\`code\\\`\` | \`code\` |

---

**Test Instructions:**
1. Check that tables render consistently in all three views
2. Verify borders, padding, and styling are applied correctly
3. Test responsive behavior on mobile devices
4. Ensure hover effects work properly
5. Check dark mode compatibility`);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdownContent);
      toast.success('Markdown copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadMarkdown = () => {
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'table-rendering-test.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Markdown file downloaded!');
  };

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center gap-3">
            <TableIcon className="h-10 w-10 text-primary" />
            Table Rendering Test
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            Compare table rendering between the unified markdown editor and lesson content display to ensure consistency.
          </p>
          
          <div className="flex flex-wrap gap-2 mb-6">
            <Badge variant="default">Table Rendering</Badge>
            <Badge variant="secondary">Unified Markdown</Badge>
            <Badge variant="outline">Lesson Content</Badge>
            <Badge variant="outline">Responsive Design</Badge>
          </div>

          <div className="flex gap-2 mb-6">
            <Button
              variant="outline"
              size="sm"
              onClick={copyToClipboard}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              Copy Markdown
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadMarkdown}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
          </div>
        </div>

        <Tabs defaultValue="comparison" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="comparison">Side-by-Side</TabsTrigger>
            <TabsTrigger value="editor">Editor Only</TabsTrigger>
            <TabsTrigger value="preview">Preview Only</TabsTrigger>
            <TabsTrigger value="lesson">Lesson Content</TabsTrigger>
          </TabsList>

          <TabsContent value="comparison" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Unified Editor */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Unified Markdown Editor
                  </CardTitle>
                  <CardDescription>
                    Tables as they appear in the editor with live preview
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <UnifiedMarkdownEditor
                    initialContent={markdownContent}
                    onChange={setMarkdownContent}
                    placeholder="Edit the table markdown..."
                    minHeight={600}
                    autoFocus={false}
                    showToolbar={true}
                    showPreview={true}
                    mode="split"
                    theme="github"
                  />
                </CardContent>
              </Card>

              {/* Lesson Content Display */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    Lesson Content Display
                  </CardTitle>
                  <CardDescription>
                    Tables as they appear in actual lesson pages (using LessonContent component)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-4 bg-card min-h-[600px] overflow-auto">
                    <LessonContent content={markdownContent} />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="editor" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Unified Markdown Editor</CardTitle>
                <CardDescription>
                  Full editor interface with toolbar and preview options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UnifiedMarkdownEditor
                  initialContent={markdownContent}
                  onChange={setMarkdownContent}
                  placeholder="Edit the table markdown..."
                  minHeight={700}
                  autoFocus={false}
                  showToolbar={true}
                  showPreview={true}
                  mode="editor"
                  theme="github"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Markdown Preview Component</CardTitle>
                <CardDescription>
                  Raw MarkdownPreview component with professional-prose styling
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 bg-card min-h-[600px]">
                  <MarkdownPreview
                    content={markdownContent}
                    className="professional-prose"
                    allowHtml={true}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="lesson" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lesson Content Component</CardTitle>
                <CardDescription>
                  Exact same component used in actual lesson pages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-card min-h-[600px]">
                  <LessonContent content={markdownContent} />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Test Results Summary */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TableIcon className="h-5 w-5" />
              Table Rendering Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold text-green-600 mb-2">✅ Expected to Work</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Consistent table borders and styling</li>
                  <li>• Proper header background colors</li>
                  <li>• Alternating row colors</li>
                  <li>• Hover effects on table rows</li>
                  <li>• Responsive table scrolling</li>
                  <li>• Dark mode compatibility</li>
                  <li>• Proper text formatting in cells</li>
                  <li>• Consistent padding and spacing</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-600 mb-2">🔍 Test Checklist</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Compare all four views side-by-side</li>
                  <li>• Test on mobile devices (responsive)</li>
                  <li>• Toggle dark/light mode</li>
                  <li>• Check table overflow behavior</li>
                  <li>• Verify text formatting in cells</li>
                  <li>• Test hover interactions</li>
                  <li>• Check border consistency</li>
                  <li>• Validate color scheme adherence</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default TableRenderingTest;
