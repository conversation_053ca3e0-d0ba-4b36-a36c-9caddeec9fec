import React from 'react';
import Layout from '../components/Layout';
import LessonContent from '@/components/course/LessonContent';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const TestMarkdownImages: React.FC = () => {
  const testContent = `# Test Lesson with Images

This is a test lesson to verify that markdown image processing is working correctly.

## Regular Image Test

Here's a test image from a URL:

![Test Image](https://via.placeholder.com/600x400/0066cc/ffffff?text=Test+Image)

## Markdown Content Test

This lesson contains:

- **Bold text**
- *Italic text*
- \`inline code\`
- [Links](https://example.com)

### Code Block Test

\`\`\`javascript
function testFunction() {
  console.log("This is a test");
  return true;
}
\`\`\`

### List Test

1. First item
2. Second item
3. Third item

### Another Image Test

![Another Test Image](https://via.placeholder.com/400x300/cc6600/ffffff?text=Another+Test)

## References

Here are some additional resources:

- [MDN Web Docs](https://developer.mozilla.org)
- [React Documentation](https://reactjs.org)
`;

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Markdown Image Test Page</h1>
          <p className="text-muted-foreground mb-8">
            This page tests the markdown processing and image display functionality.
          </p>
          
          <LessonContent content={testContent} />
        </div>
      </PageContainer>
    </Layout>
  );
};

export default TestMarkdownImages;
