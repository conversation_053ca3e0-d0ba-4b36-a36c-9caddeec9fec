/**
 * Test page for YouTube functionality in SimpleMarkdownEditor
 */

import React, { useState } from 'react';
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, Download, CheckCircle, Youtube } from 'lucide-react';
import { toast } from 'sonner';

const YouTubeEditorTest: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState(`# YouTube Video Test

This is a test of the YouTube functionality in the SimpleMarkdownEditor.

## Instructions

1. Click the YouTube button in the toolbar (📺)
2. Enter a YouTube URL like: https://www.youtube.com/watch?v=dQw4w9WgXcQ
3. Click "Insert Video"
4. The video should appear in the editor

## Sample Content

Here's some sample content with a YouTube video:

**Bold text** and *italic text* work normally.

- List item 1
- List item 2
- List item 3

\`\`\`javascript
// Code blocks work too
console.log("Hello, world!");
\`\`\`

> This is a blockquote

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Test the YouTube Button

Use the YouTube button in the toolbar above to add a video!
`);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(markdownContent);
    toast.success('Markdown copied to clipboard!');
  };

  const downloadMarkdown = () => {
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'youtube-test-content.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Markdown file downloaded!');
  };

  const testYouTubeUrls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://www.youtube.com/watch?v=9bZkp7q19f0',
    'https://youtu.be/dQw4w9WgXcQ',
    'https://www.youtube.com/embed/dQw4w9WgXcQ'
  ];

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">YouTube Editor Test</h1>
        <p className="text-lg text-muted-foreground mb-6">
          Test the YouTube video functionality in the SimpleMarkdownEditor component.
        </p>
        
        <div className="flex flex-wrap gap-2 mb-6">
          <Badge variant="default">
            <Youtube className="h-3 w-3 mr-1" />
            YouTube Support
          </Badge>
          <Badge variant="secondary">TipTap Editor</Badge>
          <Badge variant="outline">Live Preview</Badge>
          <Badge variant="outline">Markdown Output</Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Test URLs</CardTitle>
              <CardDescription>
                Try these YouTube URLs in the editor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {testYouTubeUrls.map((url, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <code className="text-sm bg-muted px-2 py-1 rounded flex-1">
                      {url}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(url);
                        toast.success('URL copied!');
                      }}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Features to Test</CardTitle>
              <CardDescription>
                Verify these features work correctly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">YouTube button in toolbar</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Dialog opens on button click</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">URL input validation</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Video insertion</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Preview mode display</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Editor Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>YouTube-Enabled Markdown Editor</CardTitle>
              <CardDescription>
                Test the YouTube video insertion functionality. Click the YouTube button in the toolbar
                and enter a YouTube URL to insert a video.
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy Markdown
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadMarkdown}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <SimpleMarkdownEditor
            initialContent={markdownContent}
            onChange={setMarkdownContent}
            placeholder="Start writing and test the YouTube functionality..."
            minHeight={500}
            courseId="test-course"
            moduleId="test-module"
          />
        </CardContent>
      </Card>

      {/* Markdown Output */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Markdown Output</CardTitle>
          <CardDescription>
            This is the raw markdown content that will be saved
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
            {markdownContent}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default YouTubeEditorTest;
