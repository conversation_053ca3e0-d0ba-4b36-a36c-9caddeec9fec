import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Badge definitions for each module
export const MODULE_BADGES = {
  'module1': {
    name: 'Bold Step',
    description: 'Completed Module 1',
    icon: 'bold-step',
  },
  'module2': {
    name: 'Persistence',
    description: 'Completed Module 2',
    icon: 'persistence',
  },
  'module3': {
    name: 'Consistency',
    description: 'Completed Module 3',
    icon: 'consistency',
  },
  'module4': {
    name: 'Perseverance',
    description: 'Completed Module 4',
    icon: 'perseverance',
  },
  'module5': {
    name: 'Discipline',
    description: 'Completed Module 5',
    icon: 'discipline',
  },
  'module6': {
    name: 'Last Straw',
    description: 'Completed Module 6',
    icon: 'last-straw',
  },
  'module7': {
    name: 'Achiever',
    description: 'Completed Module 7',
    icon: 'achiever',
  },
  'course_completion': {
    name: 'Course Master',
    description: 'Completed all modules in a course',
    icon: 'course-master',
  }
};

// Interface for achievement data
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  points: number;
  created_at: string;
}

// Interface for user achievement data
export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  completed_at: string;
  is_claimed: boolean;
  achievement: Achievement;
  module_id?: string;
  course_id?: string;
}

/**
 * Award a badge to a user for completing a module
 */
export const awardModuleBadge = async (
  userId: string,
  moduleId: string,
  moduleIndex: number
): Promise<boolean> => {
  try {
    if (!userId || !moduleId) {
      console.error('Missing required parameters for awarding badge:', { userId, moduleId });
      return false;
    }

    // Get the badge type based on module index (1-based)
    const badgeKey = `module${moduleIndex + 1}`;
    const badgeInfo = MODULE_BADGES[badgeKey as keyof typeof MODULE_BADGES];
    
    if (!badgeInfo) {
      console.error(`No badge defined for module index ${moduleIndex}`);
      return false;
    }

    console.log(`Awarding badge "${badgeInfo.name}" to user ${userId} for module ${moduleId}`);

    // Check if achievement exists in the achievements table
    const { data: achievementData, error: achievementError } = await supabase
      .from('achievements')
      .select('id')
      .eq('name', badgeInfo.name)
      .single();

    if (achievementError && achievementError.code !== 'PGRST116') {
      console.error('Error checking for achievement:', achievementError);
      return false;
    }

    let achievementId = achievementData?.id;

    // If achievement doesn't exist, create it
    if (!achievementId) {
      console.log(`Creating new achievement: ${badgeInfo.name}`);
      const { data: newAchievement, error: createError } = await supabase
        .from('achievements')
        .insert({
          name: badgeInfo.name,
          description: badgeInfo.description,
          icon: badgeInfo.icon,
          points: 100, // Default points value
        })
        .select('id')
        .single();

      if (createError) {
        console.error('Error creating achievement:', createError);
        return false;
      }

      achievementId = newAchievement.id;
    }

    // Check if user already has this achievement
    const { data: existingUserAchievement, error: userAchievementError } = await supabase
      .from('user_achievements')
      .select('id')
      .eq('user_id', userId)
      .eq('achievement_id', achievementId)
      .single();

    if (userAchievementError && userAchievementError.code !== 'PGRST116') {
      console.error('Error checking for user achievement:', userAchievementError);
      return false;
    }

    // If user doesn't have this achievement yet, award it
    if (!existingUserAchievement) {
      const { error: awardError } = await supabase
        .from('user_achievements')
        .insert({
          user_id: userId,
          achievement_id: achievementId,
          completed_at: new Date().toISOString(),
          is_claimed: false,
          module_id: moduleId
        });

      if (awardError) {
        console.error('Error awarding achievement to user:', awardError);
        return false;
      }

      console.log(`Successfully awarded "${badgeInfo.name}" badge to user ${userId}`);
      return true;
    } else {
      console.log(`User ${userId} already has the "${badgeInfo.name}" badge`);
      return true; // Already has the badge, so consider it a success
    }
  } catch (error) {
    console.error('Unexpected error in awardModuleBadge:', error);
    return false;
  }
};

/**
 * Award a certificate badge to a user for completing a course
 */
export const awardCourseBadge = async (
  userId: string,
  courseId: string
): Promise<boolean> => {
  try {
    if (!userId || !courseId) {
      console.error('Missing required parameters for awarding course badge:', { userId, courseId });
      return false;
    }

    const badgeInfo = MODULE_BADGES.course_completion;
    
    console.log(`Awarding badge "${badgeInfo.name}" to user ${userId} for course ${courseId}`);

    // Check if achievement exists in the achievements table
    const { data: achievementData, error: achievementError } = await supabase
      .from('achievements')
      .select('id')
      .eq('name', badgeInfo.name)
      .single();

    if (achievementError && achievementError.code !== 'PGRST116') {
      console.error('Error checking for achievement:', achievementError);
      return false;
    }

    let achievementId = achievementData?.id;

    // If achievement doesn't exist, create it
    if (!achievementId) {
      console.log(`Creating new achievement: ${badgeInfo.name}`);
      const { data: newAchievement, error: createError } = await supabase
        .from('achievements')
        .insert({
          name: badgeInfo.name,
          description: badgeInfo.description,
          icon: badgeInfo.icon,
          points: 500, // Higher points for course completion
        })
        .select('id')
        .single();

      if (createError) {
        console.error('Error creating achievement:', createError);
        return false;
      }

      achievementId = newAchievement.id;
    }

    // Check if user already has this achievement for this course
    const { data: existingUserAchievement, error: userAchievementError } = await supabase
      .from('user_achievements')
      .select('id')
      .eq('user_id', userId)
      .eq('achievement_id', achievementId)
      .eq('course_id', courseId)
      .single();

    if (userAchievementError && userAchievementError.code !== 'PGRST116') {
      console.error('Error checking for user achievement:', userAchievementError);
      return false;
    }

    // If user doesn't have this achievement yet for this course, award it
    if (!existingUserAchievement) {
      const { error: awardError } = await supabase
        .from('user_achievements')
        .insert({
          user_id: userId,
          achievement_id: achievementId,
          completed_at: new Date().toISOString(),
          is_claimed: false,
          course_id: courseId
        });

      if (awardError) {
        console.error('Error awarding achievement to user:', awardError);
        return false;
      }

      console.log(`Successfully awarded "${badgeInfo.name}" badge to user ${userId} for course ${courseId}`);
      return true;
    } else {
      console.log(`User ${userId} already has the "${badgeInfo.name}" badge for course ${courseId}`);
      return true; // Already has the badge, so consider it a success
    }
  } catch (error) {
    console.error('Unexpected error in awardCourseBadge:', error);
    return false;
  }
};

/**
 * Get all achievements for a user
 */
export const getUserAchievements = async (userId: string): Promise<UserAchievement[]> => {
  try {
    if (!userId) {
      console.error('Missing userId for getUserAchievements');
      return [];
    }

    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        id,
        user_id,
        achievement_id,
        completed_at,
        is_claimed,
        module_id,
        course_id,
        achievement:achievements(
          id,
          name,
          description,
          icon,
          points,
          created_at
        )
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: false });

    if (error) {
      console.error('Error fetching user achievements:', error);
      return [];
    }

    return data as UserAchievement[];
  } catch (error) {
    console.error('Unexpected error in getUserAchievements:', error);
    return [];
  }
};

/**
 * Claim a badge (mark it as claimed)
 */
export const claimBadge = async (userId: string, userAchievementId: string): Promise<boolean> => {
  try {
    if (!userId || !userAchievementId) {
      console.error('Missing required parameters for claiming badge:', { userId, userAchievementId });
      return false;
    }

    // Verify the achievement belongs to the user
    const { data: achievement, error: verifyError } = await supabase
      .from('user_achievements')
      .select('id')
      .eq('id', userAchievementId)
      .eq('user_id', userId)
      .single();

    if (verifyError || !achievement) {
      console.error('Error verifying achievement ownership:', verifyError);
      return false;
    }

    // Update the achievement to mark it as claimed
    const { error: updateError } = await supabase
      .from('user_achievements')
      .update({ is_claimed: true })
      .eq('id', userAchievementId);

    if (updateError) {
      console.error('Error claiming achievement:', updateError);
      return false;
    }

    console.log(`Successfully claimed achievement ${userAchievementId} for user ${userId}`);
    return true;
  } catch (error) {
    console.error('Unexpected error in claimBadge:', error);
    return false;
  }
};
