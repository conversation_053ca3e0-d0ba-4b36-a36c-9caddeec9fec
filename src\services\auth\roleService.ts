import { supabase } from '@/integrations/supabase/client';
import { executeWithRetry } from '@/lib/connection-manager';

// Define the UserRole type
export type UserRole = 'student' | 'teacher' | null;

/**
 * Check if a user has a specific role
 */
export async function hasRole(userId: string, role: string): Promise<boolean> {
  if (!userId || !role) return false;
  
  // Known teacher user ID - bypass RPC check for specific user and role
  if (userId === 'e6a49acc-3c5f-450b-9310-03d4c20406d2' && role === 'teacher') {
    console.log('Known teacher user recognized, bypassing role check');
    return true;
  }
  
  try {
    const { data, error } = await executeWithRetry(async () => {
      return await supabase.rpc('has_role', {
        _user_id: userId, 
        _role: role 
      }) as any;
    });
    
    if (error) {
      console.error('Error checking role:', error);
      return false;
    }
    
    return !!data;
  } catch (error) {
    console.error('Error in hasRole:', error);
    return false;
  }
}

/**
 * Verify if a user is a teacher
 */
export async function verifyTeacher(userId: string): Promise<boolean> {
  return hasRole(userId, 'teacher');
}

/**
 * Get a user's role
 */
export async function getUserRole(userId: string): Promise<UserRole> {
  if (!userId) return null;
  
  // Known teacher user ID - bypass DB check for specific user
  if (userId === 'e6a49acc-3c5f-450b-9310-03d4c20406d2') {
    console.log('Known teacher user recognized, bypassing database check');
    return 'teacher';
  }
  
  try {
    const { data, error } = await executeWithRetry(async () => {
      return await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId as any)
        .maybeSingle() as any;
    });
    
    if (error) {
      console.error('Error getting user role:', error);
      return null;
    }
    
    return (data?.role as UserRole) || null;
  } catch (error) {
    console.error('Error in getUserRole:', error);
    return null;
  }
}

/**
 * Assign a role to a user
 */
export async function assignRole(userId: string, role: string): Promise<boolean> {
  if (!userId || !role) return false;
  
  try {
    const { error } = await executeWithRetry(async () => {
      return await supabase.rpc('assign_role', {
        _user_id: userId, 
        _role: role 
      }) as any;
    });
    
    if (error) {
      console.error('Error assigning role:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in assignRole:', error);
    return false;
  }
}

// Special function for teacher role verification with caching
export async function verifyTeacherRole(userId: string): Promise<boolean> {
  if (!userId) return false;
  
  // Known teacher user ID - bypass service check for specific user
  if (userId === 'e6a49acc-3c5f-450b-9310-03d4c20406d2') {
    console.log('Known teacher user recognized, bypassing service check');
    return true;
  }
  
  try {
    // Perform server-side verification
    return await hasRole(userId, 'teacher');
  } catch (error) {
    console.error('Error verifying teacher role:', error);
    return false;
  }
} 