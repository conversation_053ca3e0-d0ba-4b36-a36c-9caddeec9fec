import { supabase } from '@/integrations/supabase/client';
import { getUserRole } from '@/services/auth/userRoleService';

/**
 * Service for handling module and lesson access control
 */

export interface AccessResult {
  hasAccess: boolean;
  reason?: string;
}

export interface ModuleAccessInfo {
  moduleId: string;
  hasAccess: boolean;
  isLocked: boolean;
  reason?: string;
}

export interface LessonAccessInfo {
  lessonId: string;
  hasAccess: boolean;
  isLocked: boolean;
  reason?: string;
}

/**
 * Check if a user has access to a specific module
 */
export async function checkModuleAccess(
  userId: string,
  moduleId: string
): Promise<AccessResult> {
  if (!userId || !moduleId) {
    return { hasAccess: false, reason: 'Missing user ID or module ID' };
  }

  try {
    console.log(`[ACCESS CONTROL] Checking module access for user ${userId}, module ${moduleId}`);

    // Check if user is a teacher
    const userRole = await getUserRole(userId);
    console.log(`[ACCESS CONTROL] User role: ${userRole}`);

    if (userRole === 'teacher') {
      console.log(`[ACCESS CONTROL] Teacher access granted for module ${moduleId}`);
      return { hasAccess: true, reason: 'Teacher access' };
    }

    // Use the database function to check access
    const { data, error } = await supabase.rpc('check_module_access', {
      p_user_id: userId,
      p_module_id: moduleId
    });

    if (error) {
      console.error('[ACCESS CONTROL] Error checking module access:', error);
      return { hasAccess: false, reason: 'Database error' };
    }

    console.log(`[ACCESS CONTROL] Module access result for ${moduleId}: ${data}`);
    return {
      hasAccess: !!data,
      reason: data ? 'Access granted' : 'Previous module not completed'
    };
  } catch (error) {
    console.error('[ACCESS CONTROL] Error in checkModuleAccess:', error);
    return { hasAccess: false, reason: 'Unexpected error' };
  }
}

/**
 * Check if a user has access to a specific lesson
 */
export async function checkLessonAccess(
  userId: string,
  lessonId: string
): Promise<AccessResult> {
  if (!userId || !lessonId) {
    return { hasAccess: false, reason: 'Missing user ID or lesson ID' };
  }

  try {
    // Check if user is a teacher
    const userRole = await getUserRole(userId);
    if (userRole === 'teacher') {
      return { hasAccess: true, reason: 'Teacher access' };
    }

    // Use the database function to check access
    const { data, error } = await supabase.rpc('check_lesson_access', {
      p_user_id: userId,
      p_lesson_id: lessonId
    });

    if (error) {
      console.error('Error checking lesson access:', error);
      return { hasAccess: false, reason: 'Database error' };
    }

    return {
      hasAccess: !!data,
      reason: data ? 'Access granted' : 'Previous lesson not completed or module locked'
    };
  } catch (error) {
    console.error('Error in checkLessonAccess:', error);
    return { hasAccess: false, reason: 'Unexpected error' };
  }
}

/**
 * Get access information for all modules in a course
 */
export async function getCourseModuleAccess(
  userId: string,
  courseId: string
): Promise<ModuleAccessInfo[]> {
  if (!userId || !courseId) {
    return [];
  }

  try {
    // Get all modules for the course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, module_number, title')
      .eq('course_id', courseId)
      .order('module_number');

    if (modulesError || !modules) {
      console.error('Error fetching modules:', modulesError);
      return [];
    }

    // Check access for each module
    const accessPromises = modules.map(async (module) => {
      const accessResult = await checkModuleAccess(userId, module.id);
      return {
        moduleId: module.id,
        hasAccess: accessResult.hasAccess,
        isLocked: !accessResult.hasAccess,
        reason: accessResult.reason
      };
    });

    return await Promise.all(accessPromises);
  } catch (error) {
    console.error('Error in getCourseModuleAccess:', error);
    return [];
  }
}

/**
 * Get access information for all lessons in a module
 */
export async function getModuleLessonAccess(
  userId: string,
  moduleId: string
): Promise<LessonAccessInfo[]> {
  if (!userId || !moduleId) {
    return [];
  }

  try {
    // Get all lessons for the module
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, lesson_number, title')
      .eq('module_id', moduleId)
      .order('lesson_number');

    if (lessonsError || !lessons) {
      console.error('Error fetching lessons:', lessonsError);
      return [];
    }

    // Check access for each lesson
    const accessPromises = lessons.map(async (lesson) => {
      const accessResult = await checkLessonAccess(userId, lesson.id);
      return {
        lessonId: lesson.id,
        hasAccess: accessResult.hasAccess,
        isLocked: !accessResult.hasAccess,
        reason: accessResult.reason
      };
    });

    return await Promise.all(accessPromises);
  } catch (error) {
    console.error('Error in getModuleLessonAccess:', error);
    return [];
  }
}

/**
 * Update access for a user after progress changes
 */
export async function updateUserAccess(
  userId: string,
  courseId?: string
): Promise<boolean> {
  if (!userId) {
    return false;
  }

  try {
    const { error } = await supabase.rpc('update_module_locks_for_user', {
      p_user_id: userId,
      p_course_id: courseId || null
    });

    if (error) {
      console.error('Error updating user access:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in updateUserAccess:', error);
    return false;
  }
}

/**
 * Get cached access information from the database
 */
export async function getCachedModuleAccess(
  userId: string,
  moduleId: string
): Promise<boolean | null> {
  if (!userId || !moduleId) {
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('user_module_access')
      .select('has_access')
      .eq('user_id', userId)
      .eq('module_id', moduleId)
      .maybeSingle();

    if (error) {
      console.error('Error getting cached module access:', error);
      return null;
    }

    return data?.has_access ?? null;
  } catch (error) {
    console.error('Error in getCachedModuleAccess:', error);
    return null;
  }
}

/**
 * Get cached access information for lessons
 */
export async function getCachedLessonAccess(
  userId: string,
  lessonId: string
): Promise<boolean | null> {
  if (!userId || !lessonId) {
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('user_lesson_access')
      .select('has_access')
      .eq('user_id', userId)
      .eq('lesson_id', lessonId)
      .maybeSingle();

    if (error) {
      console.error('Error getting cached lesson access:', error);
      return null;
    }

    return data?.has_access ?? null;
  } catch (error) {
    console.error('Error in getCachedLessonAccess:', error);
    return null;
  }
}
