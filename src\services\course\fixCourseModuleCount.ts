import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Updates the total_modules count for all courses based on the actual number of modules
 * This is a utility function to fix any inconsistencies in the database
 */
export const fixAllCourseModuleCounts = async (): Promise<boolean> => {
  try {
    console.log('Fixing module counts for all courses');
    
    // Get all courses
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title');
      
    if (coursesError) {
      console.error('Error fetching courses:', coursesError);
      toast.error('Failed to fetch courses');
      return false;
    }
    
    console.log(`Found ${courses.length} courses to update`);
    
    // Update each course's module count
    for (const course of courses) {
      await fixCourseModuleCount(course.id, course.title);
    }
    
    toast.success('All course module counts have been updated');
    return true;
  } catch (error: any) {
    console.error('Error fixing course module counts:', error);
    toast.error(`Failed to fix course module counts: ${error.message}`);
    return false;
  }
};

/**
 * Updates the total_modules count for a specific course based on the actual number of modules
 */
export const fixCourseModuleCount = async (courseId: string, courseTitle?: string): Promise<boolean> => {
  try {
    console.log(`Fixing module count for course ${courseId} (${courseTitle || 'Unnamed'})`);
    
    // Count modules for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);
      
    if (modulesError) {
      console.error('Error counting modules:', modulesError);
      toast.error(`Failed to count modules for course ${courseTitle || courseId}`);
      return false;
    }
    
    const totalModules = modules.length;
    console.log(`Course ${courseId} has ${totalModules} modules`);
    
    // Update the course with the correct count
    const { error: updateError } = await supabase
      .from('courses')
      .update({ total_modules: totalModules })
      .eq('id', courseId);
      
    if (updateError) {
      console.error('Error updating course total_modules:', updateError);
      toast.error(`Failed to update module count for course ${courseTitle || courseId}`);
      return false;
    }
    
    console.log(`Successfully updated module count for course ${courseId}`);
    return true;
  } catch (error: any) {
    console.error('Error fixing course module count:', error);
    toast.error(`Failed to fix module count: ${error.message}`);
    return false;
  }
};
