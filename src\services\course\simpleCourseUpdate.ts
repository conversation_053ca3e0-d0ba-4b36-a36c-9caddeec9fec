import { supabase } from '@/integrations/supabase/client';
import { createSmallImage } from '@/lib/simple-image';

/**
 * Debug mode - set to true to enable detailed logging
 */
const DEBUG = true;

/**
 * Simple interface for course update data
 */
interface SimpleCourseUpdateData {
  id: string;
  title: string;
  slug: string;
  description: string;
  instructor: string;
  imageFile?: File | null;
}

/**
 * Update a course with minimal processing
 * This function uses a simplified approach to update courses and handle images
 *
 * @param data The course data to update
 * @returns A Promise that resolves to the updated course
 */
export async function updateCourseSimple(data: SimpleCourseUpdateData) {
  try {
    console.log('Starting simple course update for course ID:', data.id);

    // Prepare the update data
    const updateData: any = {
      title: data.title,
      slug: data.slug,
      description: data.description,
      instructor: data.instructor,
      updated_at: new Date().toISOString()
    };

    // Process the image if provided
    if (data.imageFile) {
      console.log('Processing image for course update');
      try {
        // Create a small image
        const imageDataUrl = await createSmallImage(data.imageFile);

        if (DEBUG) {
          console.log('Image data URL length:', imageDataUrl.length);
          console.log('Image data URL preview:', imageDataUrl.substring(0, 50) + '...');
        }

        // Store the image URL in the update data
        updateData.image_url = imageDataUrl;
        console.log('Image processed successfully and added to update data');
      } catch (imageError) {
        console.error('Failed to process image:', imageError);
        throw new Error('Failed to process image. Please try a smaller image.');
      }
    } else {
      if (DEBUG) console.log('No image file provided for update');
    }

    // Update the course in the database
    console.log('Updating course in database');

    try {
      // First, try to update with the image_url
      const { data: updatedCourse, error } = await supabase
        .from('courses')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        // If there's an error about the image_url column
        if (error.message && error.message.includes('image_url')) {
          console.warn('image_url column issue detected, trying without image');

          // Remove the image_url from the update data
          const { image_url, ...dataWithoutImage } = updateData;

          // Try again without the image_url
          const { data: updatedWithoutImage, error: errorWithoutImage } = await supabase
            .from('courses')
            .update(dataWithoutImage)
            .eq('id', data.id)
            .select()
            .single();

          if (errorWithoutImage) {
            console.error('Database error during course update (without image):', errorWithoutImage);
            throw new Error(`Database error: ${errorWithoutImage.message}`);
          }

          return updatedWithoutImage;
        } else {
          console.error('Database error during course update:', error);
          throw new Error(`Database error: ${error.message}`);
        }
      }

      return updatedCourse;
    } catch (dbError: any) {
      console.error('Unexpected error during database update:', dbError);
      throw new Error(`Database error: ${dbError.message || 'Unknown error'}`);
    }

    // Note: This code is unreachable due to the try/catch/return above
    // It's kept for reference only
  } catch (error: any) {
    console.error('Error in updateCourseSimple:', error);
    throw error;
  }
}

/**
 * Create a new course with minimal processing
 * This function uses a simplified approach to create courses and handle images
 *
 * @param data The course data to create
 * @returns A Promise that resolves to the created course
 */
export async function createCourseSimple(data: Omit<SimpleCourseUpdateData, 'id'>) {
  try {
    console.log('Starting simple course creation');

    // Prepare the insert data
    const insertData: any = {
      title: data.title,
      slug: data.slug,
      description: data.description,
      instructor: data.instructor,
      total_modules: 0,
      completed_modules: 0
    };

    // Process the image if provided
    if (data.imageFile) {
      console.log('Processing image for new course');
      try {
        // Create a small image
        const imageDataUrl = await createSmallImage(data.imageFile);

        if (DEBUG) {
          console.log('Image data URL length:', imageDataUrl.length);
          console.log('Image data URL preview:', imageDataUrl.substring(0, 50) + '...');
        }

        // Store the image URL in the insert data
        insertData.image_url = imageDataUrl;
        console.log('Image processed successfully and added to insert data');
      } catch (imageError) {
        console.error('Failed to process image:', imageError);
        throw new Error('Failed to process image. Please try a smaller image.');
      }
    } else {
      if (DEBUG) console.log('No image file provided for new course');
    }

    // Insert the course into the database
    console.log('Inserting course into database');

    try {
      // First, try to insert with the image_url
      const { data: newCourse, error } = await supabase
        .from('courses')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        // If there's an error about the image_url column
        if (error.message && error.message.includes('image_url')) {
          console.warn('image_url column issue detected, trying without image');

          // Remove the image_url from the insert data
          const { image_url, ...dataWithoutImage } = insertData;

          // Try again without the image_url
          const { data: insertedWithoutImage, error: errorWithoutImage } = await supabase
            .from('courses')
            .insert([dataWithoutImage])
            .select()
            .single();

          if (errorWithoutImage) {
            console.error('Database error during course creation (without image):', errorWithoutImage);
            throw new Error(`Database error: ${errorWithoutImage.message}`);
          }

          return insertedWithoutImage;
        } else {
          console.error('Database error during course creation:', error);
          throw new Error(`Database error: ${error.message}`);
        }
      }

      return newCourse;
    } catch (dbError: any) {
      console.error('Unexpected error during database insert:', dbError);
      throw new Error(`Database error: ${dbError.message || 'Unknown error'}`);
    }

    // Note: This code is unreachable due to the try/catch/return above
    // It's kept for reference only
  } catch (error: any) {
    console.error('Error in createCourseSimple:', error);
    throw error;
  }
}
