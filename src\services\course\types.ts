// Type definitions for course-related data
export interface Course {
  id: string;
  slug: string;
  title: string;
  description: string;
  instructor: string;
  total_modules: number;
  completed_modules: number;
  image_url?: string | null;
  created_at: string;
  updated_at: string;
  enrollment_status?: string;
}

export interface Module {
  id: string;
  course_id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked: boolean;
  is_completed: boolean;
  image_url?: string;
  created_at: string;
  updated_at: string;
  lessons?: Lesson[];
}

export interface Lesson {
  id: string;
  module_id: string;
  slug: string;
  title: string;
  duration: string;
  type: 'lesson' | 'quiz' | 'assignment';
  requirement?: string;
  completed: boolean;
  content?: string;
  videoUrl?: string;
  imageUrl?: string;
  lesson_number?: number;
  is_locked?: boolean;
}

// Raw database types
export interface RawLesson {
  id: string;
  module_id: string;
  slug: string;
  title: string;
  duration: string;
  type: string;
  requirement: string | null;
  completed: boolean;
  content: string | null;
  created_at: string;
  updated_at: string;
  lesson_number?: number;
}

export interface RawModule {
  id: string;
  course_id: string;
  slug: string;
  module_number: number;
  title: string;
  is_locked: boolean;
  is_completed: boolean;
  created_at: string;
  updated_at: string;
  image_url?: string | null;
  lessons?: RawLesson[];
  courses?: {
    title: string;
  };
}

// Input types for creating and updating
export interface CourseCreateInput {
  title: string;
  slug: string;
  description: string;
  instructor: string;
  image_url?: string;
}

export interface ModuleCreateInput {
  course_id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked?: boolean;
  image_url?: string | null;
}

export interface LessonCreateInput {
  module_id: string;
  title: string;
  slug: string;
  duration: string;
  type?: 'lesson' | 'quiz' | 'assignment';
  requirement?: string;
  content?: string;
  videoUrl?: string;
  imageUrl?: string;
  lesson_number?: number;
}

// Enrollment types
export interface CourseEnrollment {
  id: string;
  user_id: string;
  course_id: string;
  status: 'not_started' | 'in_progress' | 'completed';
  enrolled_at: string;
  completed_at?: string;
  updated_at?: string;
  profiles?: any; // Adding profiles for relation with user profiles
}

// Progress tracking types
export interface UserLessonProgress {
  id: string;
  user_id: string;
  lesson_id: string;
  is_completed: boolean;
  progress_percent?: number;
  time_spent?: number;
  last_position?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserCourseProgress {
  id: string;
  user_id: string;
  course_id: string;
  hours_spent: number;
  last_accessed_at: string;
  created_at: string;
  updated_at: string;
}

export interface UserModuleProgress {
  id: string;
  user_id: string;
  module_id: string;
  is_completed: boolean;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}
