import { Lesson, RawLesson } from './types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Convert raw lesson data to typed lesson data
export const convertLesson = (rawLesson: RawLesson): Lesson => {
  let type: 'lesson' | 'quiz' | 'assignment' = 'lesson';

  if (rawLesson.type === 'quiz') {
    type = 'quiz';
  } else if (rawLesson.type === 'assignment') {
    type = 'assignment';
  }

  return {
    id: rawLesson.id,
    module_id: rawLesson.module_id,
    slug: rawLesson.slug,
    title: rawLesson.title,
    duration: rawLesson.duration,
    type,
    requirement: rawLesson.requirement || undefined,
    completed: rawLesson.completed || false,
    content: rawLesson.content || undefined
  };
};

// Check if a content string is quiz content
export const isQuizContent = (content: string | undefined): boolean => {
  if (!content) return false;
  
  try {
    const parsed = JSON.parse(content);
    return !!parsed.questions;
  } catch (e) {
    return false;
  }
};

/**
 * Create a lesson that redirects to an external URL
 * @param moduleId The ID of the module to add the lesson to
 * @param title The title of the lesson
 * @param url The URL to redirect to
 * @returns The created lesson, or null if there was an error
 */
export const createExternalRedirectLesson = async (
  moduleId: string,
  title: string,
  url: string
): Promise<Lesson | null> => {
  try {
    // Create a slug from the title
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
    
    // Create the lesson with content that will trigger a redirect
    const contentJson = JSON.stringify({
      content: `<p>You will be redirected to an external form. <a href="${url}" target="_blank">Click here</a> if you are not redirected automatically.</p>`,
      externalRedirectUrl: url
    });

    const { data, error } = await supabase
      .from('lessons')
      .insert([{
        module_id: moduleId,
        title: title,
        slug: slug,
        duration: '15:00',
        type: 'lesson',
        content: contentJson,
        completed: false
      }])
      .select();

    if (error) {
      console.error('Error creating external redirect lesson:', error);
      return null;
    }

    return convertLesson(data[0] as RawLesson);
  } catch (error) {
    console.error('Error in createExternalRedirectLesson:', error);
    return null;
  }
};

/**
 * Add a FINAL EXAMINATION module with an Exam lesson that redirects to a Google Form
 * @param courseId The ID of the course to add the module to
 * @param googleFormUrl The URL of the Google Form to redirect to
 * @returns True if successful, false otherwise
 */
export const addFinalExamModule = async (
  courseId: string,
  googleFormUrl: string
): Promise<boolean> => {
  try {
    // Get the count of existing modules to determine the module number
    const { data: moduleCount, error: countError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);

    if (countError) {
      console.error('Error counting modules:', countError);
      return false;
    }

    const moduleNumber = (moduleCount?.length || 0) + 1;

    // Create the FINAL EXAMINATION module
    const { data: moduleData, error: moduleError } = await supabase
      .from('modules')
      .insert([{
        course_id: courseId,
        title: 'FINAL EXAMINATION',
        slug: 'final-examination',
        module_number: moduleNumber,
        is_locked: false,
        is_completed: false
      }])
      .select();

    if (moduleError || !moduleData || moduleData.length === 0) {
      console.error('Error creating FINAL EXAMINATION module:', moduleError);
      return false;
    }

    // Create the Exam lesson with the Google Form URL
    const moduleId = moduleData[0].id;
    const lesson = await createExternalRedirectLesson(moduleId, 'Exam', googleFormUrl);

    if (!lesson) {
      console.error('Error creating Exam lesson');
      return false;
    }

    // Update the course's total_modules count
    try {
      // Get the current count of modules for this course
      const { data: updatedModuleCount, error: updatedCountError } = await supabase
        .from('modules')
        .select('id')
        .eq('course_id', courseId);

      if (!updatedCountError && updatedModuleCount) {
        const totalModules = updatedModuleCount.length;
        
        const { error: updateError } = await supabase
          .from('courses')
          .update({ total_modules: totalModules })
          .eq('id', courseId);

        if (updateError) {
          console.error('Error updating course total_modules:', updateError);
        }
      }
    } catch (error) {
      console.error('Error updating course total_modules:', error);
      // Continue anyway as this is not critical
    }

    toast.success('Successfully added FINAL EXAMINATION module with Exam');
    return true;
  } catch (error) {
    console.error('Error in addFinalExamModule:', error);
    return false;
  }
};

// Format duration for display
export const formatDuration = (duration: string): string => {
  // If it's already in mm:ss format, return as is
  if (/^\d+:\d+$/.test(duration)) {
    return duration;
  }

  // Try to parse as a number of minutes
  const minutes = parseInt(duration);
  if (!isNaN(minutes)) {
    return `${minutes}:00`;
  }

  return duration;
};

// Get lesson type display name
export const getLessonTypeLabel = (type: string): string => {
  switch (type) {
    case 'quiz':
      return 'Quiz';
    case 'assignment':
      return 'Assignment';
    default:
      return 'Lesson';
  }
};

// Calculate estimated read time for lesson content
export const calculateReadTime = (content?: string): number => {
  if (!content) return 0;

  // Average reading speed: 200 words per minute
  const wordCount = content.trim().split(/\s+/).length;
  return Math.ceil(wordCount / 200);
};

// Get a full URL for a course image
export const getCourseImageUrl = (imageUrl?: string | null): string => {
  if (!imageUrl) {
    // Return a default image if none is provided
    return '/images/default-course.jpg';
  }

  // If it's already a full URL, return it
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a storage path, construct the full URL
  if (imageUrl.startsWith('course-images/')) {
    // Get the Supabase URL from environment or use a fallback
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
    return `${supabaseUrl}/storage/v1/object/public/${imageUrl}`;
  }

  // If it's a relative path, assume it's in the public folder
  return imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
};

// Get a full URL for a module image
export const getModuleImageUrl = (imageUrl?: string | null): string => {
  if (!imageUrl) {
    // Return a default image if none is provided
    return '/images/default-module.svg';
  }

  // If it's a data URL, return it directly
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }

  // If it's already a full URL, return it
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a storage path, construct the full URL
  if (imageUrl.startsWith('module-images/')) {
    // Get the Supabase URL from environment or use a fallback
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
    return `${supabaseUrl}/storage/v1/object/public/${imageUrl}`;
  }

  // If it's a relative path, assume it's in the public folder
  return imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
};
