import { supabase } from '@/integrations/supabase/client';

/**
 * Service for database schema and RLS policy management
 */
export class SchemaService {
  /**
   * Repairs the lesson progress schema if there are issues
   */
  static async repairLessonProgressSchema(): Promise<boolean> {
    try {
      // Check if the user_lesson_progress table exists correctly
      const { data, error } = await supabase
        .from('user_lesson_progress')
        .select('id')
        .limit(1);
        
      if (error) {
        console.error('Error checking lesson progress schema:', error);
        
        // Try to call our repair function via SQL directly
        // This is a safer approach than modifying schema directly
        await supabase.rpc('repair_completion_tables' as any);
        
        // Wait a moment for schema to refresh
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Verify repair was successful
        const { error: retryError } = await supabase
          .from('user_lesson_progress')
          .select('id')
          .limit(1);
          
        if (retryError) {
          console.error('Schema repair failed:', retryError);
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in repairLessonProgressSchema:', error);
      return false;
    }
  }
  
  /**
   * Ensures RLS policies are properly configured for course tracking
   */
  static async fixRlsPolicies(): Promise<boolean> {
    try {
      // Use our new migration's repair function
      const { error } = await supabase.rpc('ensure_lesson_completion_rls' as any);
      
      if (error) {
        console.error('Error fixing RLS policies:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in fixRlsPolicies:', error);
      return false;
    }
  }
  
  /**
   * Disables RLS policies that might be causing issues with the module tracking
   * Now uses a database function to ensure proper permissions
   */
  static async fixModuleTrackingRLS(): Promise<boolean> {
    try {
      // Use our repair function that handles RLS issues
      const { error } = await supabase.rpc('ensure_module_tracking_rls' as any);
      
      if (error) {
        console.error('Error fixing module tracking RLS:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in fixModuleTrackingRLS:', error);
      return false;
    }
  }
  
  /**
   * Adds performance indexes to improve the system's speed
   */
  static async addPerformanceIndexes(): Promise<boolean> {
    try {
      // Use our database function to add indexes
      const { error } = await supabase.rpc('add_completion_indexes' as any);
      
      if (error) {
        console.error('Error adding performance indexes:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in addPerformanceIndexes:', error);
      return false;
    }
  }
  
  /**
   * Direct update of the module progress without using the problematic tables
   * Now uses our secure database function
   */
  static async updateModuleProgressDirectly(
    userId: string,
    moduleId: string,
    isCompleted: boolean
  ): Promise<boolean> {
    try {
      // Use our secure database function to update module progress
      const { error } = await supabase.rpc('update_module_progress_directly' as any, {
        p_user_id: userId,
        p_module_id: moduleId,
        p_is_completed: isCompleted
      });
      
      if (error) {
        console.error('Error directly updating module progress:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in updateModuleProgressDirectly:', error);
      return false;
    }
  }

  /**
   * A complete repair function that fixes all potential issues
   */
  static async completeSystemRepair(): Promise<boolean> {
    try {
      // Execute a comprehensive repair
      const { error } = await supabase.rpc('repair_completion_system' as any);

      if (error) {
        console.error('Error performing complete system repair:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in completeSystemRepair:', error);
      return false;
    }
  }
} 