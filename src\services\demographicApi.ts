import { supabase } from '@/integrations/supabase/client';
import {
  DemographicQuestionnaire,
  UserDemographicResponse,
  DemographicResponse,
  DemographicAnalytics,
  SchoolDemographicData,
  SchoolGroupedAnalytics
} from '@/types/demographic';

/**
 * Get the active demographic questionnaire
 */
export async function getActiveDemographicQuestionnaire(): Promise<DemographicQuestionnaire | null> {
  const { data, error } = await supabase
    .from('demographic_questionnaires')
    .select('*')
    .eq('is_active', true)
    .single();

  if (error) {
    console.error('Error fetching demographic questionnaire:', error);
    return null;
  }

  return data;
}

/**
 * Get user's demographic response for a questionnaire
 */
export async function getUserDemographicResponse(
  userId: string, 
  questionnaireId: string
): Promise<UserDemographicResponse | null> {
  const { data, error } = await supabase
    .from('user_demographic_responses')
    .select('*')
    .eq('user_id', userId)
    .eq('questionnaire_id', questionnaireId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No response found
      return null;
    }
    console.error('Error fetching user demographic response:', error);
    throw error;
  }

  return data;
}

/**
 * Save user's demographic response
 */
export async function saveDemographicResponse(
  userId: string,
  questionnaireId: string,
  responses: DemographicResponse
): Promise<UserDemographicResponse> {
  const { data, error } = await supabase
    .from('user_demographic_responses')
    .upsert({
      user_id: userId,
      questionnaire_id: questionnaireId,
      responses,
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error saving demographic response:', error);
    throw error;
  }

  return data;
}

/**
 * Check if user has completed demographic questionnaire
 */
export async function hasUserCompletedDemographics(userId: string): Promise<boolean> {
  const questionnaire = await getActiveDemographicQuestionnaire();
  if (!questionnaire) return true; // If no questionnaire, consider completed

  const response = await getUserDemographicResponse(userId, questionnaire.id);
  return response !== null;
}

/**
 * Get demographic analytics (admin only)
 */
export async function getDemographicAnalytics(): Promise<DemographicAnalytics> {
  try {
    // Try the RPC function first
    const { data, error } = await supabase.rpc('get_demographic_analytics');

    if (!error && data && data.length > 0) {
      return data[0];
    }

    // Fallback: Calculate analytics manually
    console.log('Using fallback analytics calculation');
    return await calculateAnalyticsManually();
  } catch (error) {
    console.error('Error fetching demographic analytics:', error);
    // Return fallback analytics
    return await calculateAnalyticsManually();
  }
}

/**
 * Fallback function to calculate analytics manually
 */
async function calculateAnalyticsManually(): Promise<DemographicAnalytics> {
  try {
    // Get all responses
    const { data: responses, error: responsesError } = await supabase
      .from('user_demographic_responses')
      .select('responses');

    if (responsesError) {
      throw responsesError;
    }

    // Get total users count
    const { count: totalUsers, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (usersError) {
      console.warn('Could not get total users count:', usersError);
    }

    const totalResponses = responses?.length || 0;
    const completionRate = totalUsers && totalUsers > 0 ?
      Math.round((totalResponses / totalUsers) * 100 * 100) / 100 : 0;

    // Calculate breakdowns
    const byCountry: Record<string, number> = {};
    const byGender: Record<string, number> = {};
    const byRole: Record<string, number> = {};

    responses?.forEach(response => {
      const data = response.responses;

      // Country breakdown
      const country = data.country || 'Unknown';
      byCountry[country] = (byCountry[country] || 0) + 1;

      // Gender breakdown
      const gender = data.gender || 'Unknown';
      byGender[gender] = (byGender[gender] || 0) + 1;

      // Role breakdown
      const role = data.role_type || 'Unknown';
      byRole[role] = (byRole[role] || 0) + 1;
    });

    return {
      total_responses: totalResponses,
      response_breakdown: {
        by_country: byCountry,
        by_gender: byGender,
        by_role: byRole
      },
      completion_rate: completionRate
    };
  } catch (error) {
    console.error('Error in manual analytics calculation:', error);
    return {
      total_responses: 0,
      response_breakdown: {
        by_country: {},
        by_gender: {},
        by_role: {}
      },
      completion_rate: 0
    };
  }
}

/**
 * Get all demographic responses (admin only)
 */
export async function getAllDemographicResponses(): Promise<UserDemographicResponse[]> {
  // Get all responses first
  const { data: responses, error } = await supabase
    .from('user_demographic_responses')
    .select('*')
    .order('completed_at', { ascending: false });

  if (error) {
    console.error('Error fetching all demographic responses:', error);
    throw error;
  }

  if (!responses || responses.length === 0) {
    return [];
  }

  // Get user profiles separately
  const userIds = responses.map(r => r.user_id);
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('id, first_name, last_name')
    .in('id', userIds);

  if (profilesError) {
    console.warn('Error fetching profiles:', profilesError);
  }

  // Create a map of profiles for easy lookup
  const profilesMap = new Map();
  profiles?.forEach(profile => {
    profilesMap.set(profile.id, profile);
  });

  // Attach profiles to responses
  const responsesWithProfiles = responses.map(response => ({
    ...response,
    profiles: profilesMap.get(response.user_id) || null
  }));

  return responsesWithProfiles;
}

/**
 * Delete all student demographic data (admin only)
 * This is a destructive operation that removes all demographic responses
 */
export async function deleteAllDemographicData(): Promise<{ deletedCount: number }> {
  try {
    // First, get the count of records to be deleted for logging
    const { count, error: countError } = await supabase
      .from('user_demographic_responses')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting demographic responses:', countError);
      throw new Error('Failed to count demographic responses');
    }

    const recordCount = count || 0;

    if (recordCount === 0) {
      console.log('No demographic responses to delete');
      return { deletedCount: 0 };
    }

    // Delete all demographic responses
    // Note: This requires the RLS policy "Teachers can delete all responses" to be in place
    const { data: deletedData, error: deleteError } = await supabase
      .from('user_demographic_responses')
      .delete()
      .gte('created_at', '1900-01-01') // This should match all records
      .select('id');

    if (deleteError) {
      console.error('Error deleting demographic data:', deleteError);

      // Check if it's a permission error
      if (deleteError.code === '42501' || deleteError.message.includes('permission') || deleteError.message.includes('policy')) {
        throw new Error('Permission denied: You need admin/teacher privileges to delete demographic data. Please ensure the RLS policy is in place.');
      }

      throw new Error('Failed to delete demographic data: ' + deleteError.message);
    }

    const actualDeletedCount = deletedData?.length || 0;

    // Log the action for audit purposes
    console.log(`Successfully deleted ${actualDeletedCount} demographic response records`);

    return { deletedCount: actualDeletedCount };
  } catch (error) {
    console.error('Error in deleteAllDemographicData:', error);
    throw error;
  }
}

/**
 * Get demographic response breakdown for specific field
 */
export async function getDemographicBreakdown(field: string): Promise<Record<string, number>> {
  const { data, error } = await supabase
    .from('user_demographic_responses')
    .select('responses');

  if (error) {
    console.error('Error fetching demographic breakdown:', error);
    throw error;
  }

  const breakdown: Record<string, number> = {};

  data?.forEach(response => {
    const value = response.responses[field] || 'Unknown';
    breakdown[value] = (breakdown[value] || 0) + 1;
  });

  return breakdown;
}

/**
 * Get demographic responses grouped by school/university (admin only)
 */
export async function getDemographicResponsesBySchool(): Promise<SchoolGroupedAnalytics> {
  try {
    // Get all responses first
    const { data: responses, error } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .order('completed_at', { ascending: false });

    if (error) {
      console.error('Error fetching demographic responses by school:', error);
      throw error;
    }

    if (!responses || responses.length === 0) {
      return {
        total_responses: 0,
        schools: [],
        completion_rate: 0
      };
    }

    // Get user profiles separately
    const userIds = responses.map(r => r.user_id);
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name')
      .in('id', userIds);

    if (profilesError) {
      console.warn('Error fetching profiles:', profilesError);
    }

    // Create a map of profiles for easy lookup
    const profilesMap = new Map();
    profiles?.forEach(profile => {
      profilesMap.set(profile.id, profile);
    });

    // Attach profiles to responses
    const responsesWithProfiles = responses.map(response => ({
      ...response,
      profiles: profilesMap.get(response.user_id) || null
    }));

    // Group responses by university/school
    const schoolGroups: Record<string, any[]> = {};

    responsesWithProfiles.forEach(response => {
      const university = response.responses.university || 'Unknown/Not Specified';
      if (!schoolGroups[university]) {
        schoolGroups[university] = [];
      }
      schoolGroups[university].push(response);
    });

    // Calculate analytics for each school
    const schools: SchoolDemographicData[] = Object.entries(schoolGroups).map(([schoolName, schoolResponses]) => {
      // Calculate breakdowns for this school
      const byCountry: Record<string, number> = {};
      const byGender: Record<string, number> = {};
      const byRole: Record<string, number> = {};
      const byAge: Record<string, number> = {};

      schoolResponses.forEach(response => {
        const data = response.responses;

        // Country breakdown
        const country = data.country || 'Unknown';
        byCountry[country] = (byCountry[country] || 0) + 1;

        // Gender breakdown
        const gender = data.gender || 'Unknown';
        byGender[gender] = (byGender[gender] || 0) + 1;

        // Role breakdown
        const role = data.role_type || 'Unknown';
        byRole[role] = (byRole[role] || 0) + 1;

        // Age breakdown (group ages into ranges)
        const age = data.age;
        let ageGroup = 'Unknown';
        if (age) {
          const ageNum = typeof age === 'number' ? age : parseInt(String(age));
          if (!isNaN(ageNum)) {
            if (ageNum < 20) ageGroup = 'Under 20';
            else if (ageNum < 25) ageGroup = '20-24';
            else if (ageNum < 30) ageGroup = '25-29';
            else if (ageNum < 35) ageGroup = '30-34';
            else if (ageNum < 40) ageGroup = '35-39';
            else ageGroup = '40+';
          }
        }
        byAge[ageGroup] = (byAge[ageGroup] || 0) + 1;
      });

      return {
        school_name: schoolName,
        total_responses: schoolResponses.length,
        responses: schoolResponses,
        breakdown: {
          by_country: byCountry,
          by_gender: byGender,
          by_role: byRole,
          by_age: byAge
        }
      };
    });

    // Sort schools by response count (descending)
    schools.sort((a, b) => b.total_responses - a.total_responses);

    // Calculate overall completion rate
    const { count: totalUsers, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    const completionRate = totalUsers && totalUsers > 0 ?
      Math.round((responsesWithProfiles.length / totalUsers) * 100 * 100) / 100 : 0;

    return {
      total_responses: responsesWithProfiles.length,
      schools,
      completion_rate: completionRate
    };

  } catch (error) {
    console.error('Error in getDemographicResponsesBySchool:', error);
    return {
      total_responses: 0,
      schools: [],
      completion_rate: 0
    };
  }
}
