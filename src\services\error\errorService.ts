/**
 * Error Service
 * 
 * This service provides centralized error handling for the application.
 * It includes functions for handling different types of errors, logging errors,
 * and displaying error messages to the user.
 */

import { toast } from 'sonner';
import { PostgrestError } from '@supabase/postgrest-js';
import { logEvent } from '@/services/analytics/analyticsService';
import { isServiceUnavailableError } from '@/lib/connection-manager';

// Error types
export enum ErrorType {
  DATABASE = 'database',
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown'
}

// Error severity levels
export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// Error context interface
export interface ErrorContext {
  userId?: string;
  action?: string;
  component?: string;
  additionalData?: Record<string, any>;
}

/**
 * Determine the type of error
 * @param error The error to analyze
 * @returns The type of error
 */
export function getErrorType(error: any): ErrorType {
  if (error instanceof PostgrestError) {
    return ErrorType.DATABASE;
  }
  
  if (error?.message?.includes('auth/') || error?.message?.includes('authentication')) {
    return ErrorType.AUTHENTICATION;
  }
  
  if (isServiceUnavailableError(error) || 
      error?.message?.includes('network') || 
      error?.message?.includes('connection') ||
      error?.message?.includes('offline')) {
    return ErrorType.NETWORK;
  }
  
  if (error?.message?.includes('permission') || 
      error?.message?.includes('access denied') ||
      error?.message?.includes('not authorized')) {
    return ErrorType.PERMISSION;
  }
  
  if (error?.message?.includes('validation') || 
      error?.message?.includes('invalid')) {
    return ErrorType.VALIDATION;
  }
  
  return ErrorType.UNKNOWN;
}

/**
 * Determine the severity of an error
 * @param error The error to analyze
 * @param errorType The type of error
 * @returns The severity level
 */
export function getErrorSeverity(error: any, errorType: ErrorType): ErrorSeverity {
  switch (errorType) {
    case ErrorType.AUTHENTICATION:
    case ErrorType.PERMISSION:
      return ErrorSeverity.ERROR;
    
    case ErrorType.DATABASE:
      // Database errors are usually critical
      return ErrorSeverity.CRITICAL;
    
    case ErrorType.NETWORK:
      // Network errors might be temporary
      return ErrorSeverity.WARNING;
    
    case ErrorType.VALIDATION:
      // Validation errors are usually just warnings
      return ErrorSeverity.WARNING;
    
    case ErrorType.UNKNOWN:
    default:
      // Default to ERROR for unknown errors
      return ErrorSeverity.ERROR;
  }
}

/**
 * Get a user-friendly error message
 * @param error The error object
 * @param errorType The type of error
 * @returns A user-friendly error message
 */
export function getUserFriendlyMessage(error: any, errorType: ErrorType): string {
  // Default messages for each error type
  const defaultMessages: Record<ErrorType, string> = {
    [ErrorType.DATABASE]: 'There was a problem with the database. Please try again later.',
    [ErrorType.NETWORK]: 'Network connection issue. Please check your internet connection and try again.',
    [ErrorType.AUTHENTICATION]: 'Authentication error. Please sign in again.',
    [ErrorType.VALIDATION]: 'Please check your input and try again.',
    [ErrorType.PERMISSION]: 'You do not have permission to perform this action.',
    [ErrorType.UNKNOWN]: 'An unexpected error occurred. Please try again later.'
  };
  
  // Try to extract a more specific message from the error
  let specificMessage = '';
  
  if (error instanceof PostgrestError) {
    specificMessage = error.message;
  } else if (error instanceof Error) {
    specificMessage = error.message;
  } else if (typeof error === 'string') {
    specificMessage = error;
  } else if (error?.message) {
    specificMessage = error.message;
  }
  
  // Clean up the message
  if (specificMessage) {
    // Remove technical details that wouldn't be helpful to users
    specificMessage = specificMessage
      .replace(/Error: /g, '')
      .replace(/PostgrestError: /g, '')
      .replace(/AuthError: /g, '')
      .replace(/at line \d+/g, '')
      .replace(/\(.*\)/g, '')
      .trim();
    
    // If it's still a reasonable length, use it
    if (specificMessage.length > 0 && specificMessage.length < 100) {
      return specificMessage;
    }
  }
  
  // Fall back to the default message
  return defaultMessages[errorType];
}

/**
 * Handle an error in a standardized way
 * @param error The error to handle
 * @param context Additional context about the error
 */
export function handleError(error: any, context: ErrorContext = {}): void {
  // Determine error type and severity
  const errorType = getErrorType(error);
  const severity = getErrorSeverity(error, errorType);
  
  // Log the error
  console.error(`[${severity}] ${errorType} error:`, error);
  
  // Log to analytics
  logEvent('error_occurred', {
    errorType,
    severity,
    userId: context.userId,
    action: context.action,
    component: context.component,
    message: error?.message || 'Unknown error',
    ...context.additionalData
  });
  
  // Get user-friendly message
  const userMessage = getUserFriendlyMessage(error, errorType);
  
  // Show toast notification based on severity
  switch (severity) {
    case ErrorSeverity.INFO:
      toast.info(userMessage);
      break;
    case ErrorSeverity.WARNING:
      toast.warning(userMessage);
      break;
    case ErrorSeverity.ERROR:
    case ErrorSeverity.CRITICAL:
      toast.error(userMessage);
      break;
  }
}

/**
 * Create an error handler function with pre-defined context
 * @param defaultContext Default context to include with all errors
 * @returns An error handler function
 */
export function createErrorHandler(defaultContext: ErrorContext) {
  return (error: any, additionalContext: ErrorContext = {}) => {
    handleError(error, {
      ...defaultContext,
      ...additionalContext,
      additionalData: {
        ...(defaultContext.additionalData || {}),
        ...(additionalContext.additionalData || {})
      }
    });
  };
}

/**
 * Execute a function with error handling
 * @param fn The function to execute
 * @param context Error context
 * @returns The result of the function or undefined if an error occurred
 */
export async function executeWithErrorHandling<T>(
  fn: () => Promise<T>,
  context: ErrorContext = {}
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    handleError(error, context);
    return undefined;
  }
}

/**
 * Execute a function with error handling and return a default value on error
 * @param fn The function to execute
 * @param defaultValue The default value to return on error
 * @param context Error context
 * @returns The result of the function or the default value if an error occurred
 */
export async function executeWithDefault<T>(
  fn: () => Promise<T>,
  defaultValue: T,
  context: ErrorContext = {}
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    handleError(error, context);
    return defaultValue;
  }
}
