import { supabase } from '@/integrations/supabase/client';
import { ModuleTest, ModuleTestResponse, TestResponse } from '@/types/module-test';
import { Database } from '@/integrations/supabase/types';

// Type for the table row
type ModuleTestRow = Database['public']['Tables']['module_tests']['Row'];
type ModuleTestResponseRow = Database['public']['Tables']['module_test_responses']['Row'];

// Fetch module tests by module ID
export const getModuleTestsByModuleId = async (moduleId: string): Promise<ModuleTest[]> => {
  const { data, error } = await supabase
    .from('module_tests')
    .select('*')
    .eq('module_id', moduleId)
    .order('created_at');

  if (error) {
    console.error('Error fetching module tests:', error);
    throw error;
  }

  return (data || []).map((test: ModuleTestRow) => ({
    id: test.id,
    moduleId: test.module_id,
    title: test.title,
    type: test.type,
    description: test.description || undefined,
    questions: JSON.parse(test.questions || '[]'),
    createdAt: test.created_at,
    updatedAt: test.updated_at
  }));
};

// Fetch a specific module test by ID
export const getModuleTestById = async (testId: string): Promise<ModuleTest> => {
  const { data, error } = await supabase
    .from('module_tests')
    .select('*')
    .eq('id', testId)
    .single();

  if (error) {
    console.error('Error fetching module test:', error);
    throw error;
  }

  return {
    id: data.id,
    moduleId: data.module_id,
    title: data.title,
    type: data.type,
    description: data.description || undefined,
    questions: JSON.parse(data.questions || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
};

// Fetch a specific module test by module ID and type
export const getModuleTestByType = async (
  moduleId: string,
  type: 'pre_test' | 'post_test'
): Promise<ModuleTest | null> => {
  const { data, error } = await supabase
    .from('module_tests')
    .select('*')
    .eq('module_id', moduleId)
    .eq('type', type)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No data found - this is normal for modules that don't have a test
      return null;
    }
    console.error('Error fetching module test by type:', error);
    throw error;
  }

  return {
    id: data.id,
    moduleId: data.module_id,
    title: data.title,
    type: data.type,
    description: data.description || undefined,
    questions: JSON.parse(data.questions || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
};

// Create a new module test
export const createModuleTest = async (test: Partial<ModuleTest>): Promise<ModuleTest> => {
  const { data, error } = await supabase
    .from('module_tests')
    .insert({
      module_id: test.moduleId,
      title: test.title,
      type: test.type,
      description: test.description,
      questions: JSON.stringify(test.questions || [])
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating module test:', error);
    throw error;
  }

  return {
    id: data.id,
    moduleId: data.module_id,
    title: data.title,
    type: data.type,
    description: data.description || undefined,
    questions: JSON.parse(data.questions || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
};

// Update an existing module test
export const updateModuleTest = async (testId: string, test: Partial<ModuleTest>): Promise<ModuleTest> => {
  const { data, error } = await supabase
    .from('module_tests')
    .update({
      title: test.title,
      description: test.description,
      questions: test.questions ? JSON.stringify(test.questions) : undefined,
      updated_at: new Date().toISOString()
    })
    .eq('id', testId)
    .select()
    .single();

  if (error) {
    console.error('Error updating module test:', error);
    throw error;
  }

  return {
    id: data.id,
    moduleId: data.module_id,
    title: data.title,
    type: data.type,
    description: data.description || undefined,
    questions: JSON.parse(data.questions || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
};

// Delete a module test
export const deleteModuleTest = async (testId: string): Promise<void> => {
  const { error } = await supabase
    .from('module_tests')
    .delete()
    .eq('id', testId);

  if (error) {
    console.error('Error deleting module test:', error);
    throw error;
  }
};

// Submit a test response
export const submitTestResponse = async (
  testId: string,
  userId: string,
  responses: TestResponse[]
): Promise<ModuleTestResponse> => {
  const { data, error } = await supabase
    .from('module_test_responses')
    .upsert({
      test_id: testId,
      user_id: userId,
      responses: JSON.stringify(responses),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error submitting test response:', error);
    throw error;
  }

  return {
    id: data.id,
    testId: data.test_id,
    userId: data.user_id,
    responses: JSON.parse(data.responses || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
};

// Check if a user has completed a specific test
export const hasUserCompletedTest = async (testId: string, userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('module_test_responses')
      .select('id')
      .eq('test_id', testId)
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error checking test completion:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Unexpected error checking test completion:', error);
    return false;
  }
};

// Get all completed tests for a user in a specific course
export const getUserCompletedTestsInCourse = async (courseId: string, userId: string): Promise<string[]> => {
  try {
    const { data, error } = await supabase
      .from('module_test_responses')
      .select(`
        test_id,
        module_tests!inner (
          id,
          modules!inner (
            course_id
          )
        )
      `)
      .eq('user_id', userId)
      .eq('module_tests.modules.course_id', courseId);

    if (error) {
      console.error('Error fetching user completed tests:', error);
      return [];
    }

    return data?.map(item => item.test_id) || [];
  } catch (error) {
    console.error('Unexpected error fetching user completed tests:', error);
    return [];
  }
};

// Get a user's response to a test
export const getUserTestResponse = async (
  testId: string,
  userId: string
): Promise<ModuleTestResponse | null> => {
  const { data, error } = await supabase
    .from('module_test_responses')
    .select('*')
    .eq('test_id', testId)
    .eq('user_id', userId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No data found - user hasn't taken the test yet
      return null;
    }
    console.error('Error fetching user test response:', error);
    throw error;
  }

  return {
    id: data.id,
    testId: data.test_id,
    userId: data.user_id,
    responses: JSON.parse(data.responses || '[]'),
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
}; 