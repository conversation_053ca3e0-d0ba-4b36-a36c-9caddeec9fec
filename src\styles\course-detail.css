/* Course Detail Page Styles */

.course-detail-container {
  font-family: system-ui, sans-serif;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
}

.course-detail-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.03);
}

/* Dark mode styling for the container */
.dark .course-detail-container {
  background-color: #111827;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.03);
  color: #e5e7eb;
}

.dark .course-detail-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.12);
}

.course-image-container {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 350px;
  overflow: hidden;
  background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05));
}

.dark .course-image-container {
  background-color: #1f2937;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.course-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}

.course-image-container:hover .course-image {
  transform: scale(1.02);
}

/* Image overlay gradient - not needed with the new ResponsiveImage component */
.course-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
  color: white;
}

@media (max-width: 768px) {
  .course-image-container {
    min-height: 200px;
    border-radius: 0;
  }

  .course-image-overlay {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .course-image-container {
    min-height: 180px;
  }

  .course-image-overlay {
    padding: 1rem;
  }
}

@media (max-width: 380px) {
  .course-image-container {
    min-height: 140px;
  }
}

/* Course title on image - not needed with the new ResponsiveImage component */
.course-image-title {
  font-family: 'Poppins', system-ui, sans-serif;
  position: absolute;
  bottom: 30px;
  left: 30px;
  color: white;
  font-size: 2.25rem;
  font-weight: 800;
  z-index: 2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  max-width: 80%;
}

.course-image-container:hover .course-image-title {
  transform: translateY(-5px);
}

/* Fallback image container */
.course-image-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.1), rgba(230, 57, 70, 0.2));
  padding: 3rem 1rem;
}

.dark .course-image-fallback {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.2), rgba(230, 57, 70, 0.3));
}

/* Content section styles */
.course-content-section {
  padding: 1.5rem;
}

.course-stats-container {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-content {
  padding: 1.5rem;
}

/* Typography styles */
.course-header-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #111;
  line-height: 1.2;
}

.dark .course-header-title {
  color: #fff;
}

.course-header-description {
  color: #555;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-size: 1rem;
}

.dark .course-header-description {
  color: #bbb;
}

/* Course header content */
.course-header-content {
  padding: 2rem;
  color: white;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.2));
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
}

/* Button styles */
.view-modules-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  background-color: #e63946;
  color: white;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(230, 57, 70, 0.3);
}

.view-modules-button:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(230, 57, 70, 0.4);
}

.view-modules-button:active {
  transform: translateY(0);
}

.dark .view-modules-button {
  background-color: #e63946;
  box-shadow: 0 2px 5px rgba(230, 57, 70, 0.4);
}

.dark .view-modules-button:hover {
  background-color: #d32f2f;
  box-shadow: 0 4px 8px rgba(230, 57, 70, 0.5);
}

/* Media queries for responsiveness */
@media (max-width: 768px) {
  .course-image-container {
    min-height: 200px;
    border-radius: 0;
  }

  .course-header-content {
    padding: 1.5rem 1rem;
  }

  .course-header-title {
    font-size: 1.5rem;
  }

  .course-header-description {
    font-size: 0.875rem;
  }

  .course-content-section {
    padding: 1rem;
  }

  .view-modules-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .course-image-container {
    min-height: 180px;
  }

  .course-header-content {
    padding: 1rem;
  }

  .course-header-title {
    font-size: 1.25rem;
  }

  .course-header-description {
    font-size: 0.75rem;
    margin-bottom: 1rem;
  }

  .view-modules-button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    width: 100%;
  }
}

@media (max-width: 380px) {
  .course-image-container {
    min-height: 140px;
  }
}

/* Course stats */
.course-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.course-stat {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.course-stat:hover {
  background-color: #f1f3f5;
  transform: translateY(-2px);
}

.dark .course-stat {
  background-color: #1f2937;
  border-color: rgba(255, 255, 255, 0.05);
}

.dark .course-stat:hover {
  background-color: #374151;
}



@keyframes shine {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
  }
  80% {
    transform: scale(10) rotate(45deg);
    opacity: 0.5;
  }
  100% {
    transform: scale(20) rotate(45deg);
    opacity: 0;
  }
}
