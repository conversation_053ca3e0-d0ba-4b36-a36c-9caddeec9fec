/* Desktop Enhancements for Modern UI */

/* Apply only to desktop screens */
@media (min-width: 1024px) {
  /* General UI improvements */
  :root {
    --content-max-width: 1200px;
    --content-padding-x: 1.75rem;
    --section-spacing: 1.5rem;
    --card-border-radius: 0.75rem;
    --transition-slow: 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    --shadow-subtle: 0 2px 12px rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  /* Enhanced container width */
  .container {
    max-width: var(--content-max-width);
    padding-left: var(--content-padding-x);
    padding-right: var(--content-padding-x);
    margin-left: auto;
    margin-right: auto;
  }

  /* Enhanced Sidebar */
  .sidebar-container {
    width: 256px;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 40;
    height: 100vh;
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    transform: translateX(0) !important;
    border-right: 1px solid var(--border-color);
    background-color: var(--background);
  }

  /* Sidebar scroll container */
  .sidebar-container > div {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  }

  .dark .sidebar-container > div {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }

  /* Main content with proper sidebar spacing */
  .content-with-sidebar {
    margin-left: 256px;
    width: calc(100% - 256px);
    min-height: 100vh;
  }

  /* Card and grid improvements */
  .card, 
  .content-card {
    border-radius: var(--card-border-radius);
    transition: var(--transition-slow);
    box-shadow: var(--shadow-subtle);
  }

  .card:hover,
  .content-card:hover {
    box-shadow: var(--shadow-hover);
  }

  /* Grid layout improvements */
  .grid-layout {
    display: grid;
    gap: var(--section-spacing);
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  /* Section spacing */
  .section + .section {
    margin-top: calc(var(--section-spacing) * 2);
  }

  /* Typography improvements */
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 2rem;
    line-height: 1.3;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
    margin-bottom: 1rem;
  }

  /* Form elements */
  input, select, textarea {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }

  /* Button improvements */
  button, .button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    transition: var(--transition-slow);
  }

  /* Course stats cards - smaller and more professional */
  .course-stats-container {
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
  }
  
  .course-stats-card {
    padding: 0.75rem 1rem !important;
    border-radius: 0.5rem !important;
    box-shadow: var(--shadow-subtle) !important;
  }
  
  .course-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
  }
  
  /* Module container - more compact and professional */
  .module-container {
    margin-bottom: 0.75rem !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
  }
  
  .dark .module-container {
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }
  
  .module-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06) !important;
    transform: translateY(-2px);
  }
  
  /* Module header - more compact and professional */
  .module-header {
    padding: 0.875rem 1rem !important;
    min-height: auto !important;
  }
  
  /* Module title - smaller text */
  .module-title {
    font-family: 'Poppins', system-ui, sans-serif !important;
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
    font-weight: 600 !important;
    margin-bottom: 0.25rem !important;
    letter-spacing: -0.01em !important;
    color: #222 !important;
  }
  
  .dark .module-title {
    color: #e0e0e0 !important;
  }

  /* Module info (lessons count and time) - smaller text */
  .module-info, 
  .text-gray-500,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.65rem !important;
    line-height: 1.3 !important;
    color: #666 !important;
  }
  
  .dark .module-info, 
  .dark .text-gray-500,
  .dark .flex.items-center.gap-4.text-gray-500 {
    color: #999 !important;
  }
  
  /* Smaller icons in module info */
  .module-info svg,
  .text-gray-500 svg,
  .flex.items-center.gap-4.text-gray-500 svg {
    width: 0.7rem !important;
    height: 0.7rem !important;
    margin-right: 0.1rem !important;
    color: #888 !important;
  }
  
  .dark .module-info svg,
  .dark .text-gray-500 svg,
  .dark .flex.items-center.gap-4.text-gray-500 svg {
    color: #aaa !important;
  }
  
  /* Module content padding - more compact */
  .module-content {
    padding: 0.5rem 1rem 1rem !important;
  }
  
  /* Lesson item - more compact and professional */
  .lesson-item {
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
  }
  
  .dark .lesson-item {
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }
  
  .lesson-item:hover {
    transform: translateX(3px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(230, 57, 70, 0.1) !important;
  }
  
  /* Lesson title - more professional font size */
  .lesson-title {
    font-size: 0.8125rem !important;
    line-height: 1.4 !important;
    font-weight: 500 !important;
    color: #333 !important;
  }
  
  .dark .lesson-title {
    color: #d1d5db !important;
  }
  
  /* Lesson meta info - smaller */
  .lesson-meta,
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.65rem !important;
    line-height: 1.3 !important;
    color: #777 !important;
  }
  
  .dark .lesson-meta,
  .dark .flex.items-center.gap-3.mt-1,
  .dark .flex.items-center.gap-3.text-gray-500 {
    color: #9ca3af !important;
  }
  
  /* Lesson meta icons - smaller */
  .lesson-meta svg,
  .flex.items-center.gap-3.mt-1 svg,
  .flex.items-center.gap-3.text-gray-500 svg {
    width: 0.7rem !important;
    height: 0.7rem !important;
    margin-right: 0.1rem !important;
  }
  
  /* Lesson icon - smaller */
  .lesson-icon {
    width: 1.125rem !important;
    height: 1.125rem !important;
    margin-right: 0.75rem !important;
  }
  
  /* Course title on course content page */
  .course-title {
    font-size: 1.75rem !important;
    line-height: 1.3 !important;
    letter-spacing: -0.02em !important;
    margin-bottom: 1rem !important;
  }
  
  /* Card hover effects - more subtle and professional */
  .card-hover-effect {
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1), 
                box-shadow 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .card-hover-effect:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
  }
  
  /* Modern scrollbar - slightly thinner */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 2px solid rgba(0, 0, 0, 0.03);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.15);
  }
  
  .dark ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .dark ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.05);
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  /* Enhanced typography - more professional */
  h1, h2, h3, h4, h5, h6 {
    letter-spacing: -0.02em;
  }
  
  /* Enhanced card shadows - more subtle */
  .enhanced-card {
    box-shadow: 
      0 1px 2px rgba(0, 0, 0, 0.03),
      0 2px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
  }
  
  .enhanced-card:hover {
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.04),
      0 4px 10px rgba(0, 0, 0, 0.06);
    border-color: rgba(0, 0, 0, 0.08);
  }
  
  .dark .enhanced-card {
    box-shadow: 
      0 1px 2px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.04);
  }
  
  .dark .enhanced-card:hover {
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.1),
      0 4px 10px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.06);
  }
  
  /* Glass morphism effects - more subtle */
  .glass-effect {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .dark .glass-effect {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  /* Enhanced image containers - more subtle zoom */
  .image-hover-zoom {
    overflow: hidden;
  }
  
  .image-hover-zoom img {
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .image-hover-zoom:hover img {
    transform: scale(1.03);
  }
  
  /* Enhanced button styles - more subtle */
  .enhanced-button {
    transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
  }
  
  .enhanced-button::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    z-index: -1;
  }
  
  .enhanced-button:hover::after {
    transform: translateX(100%);
  }
  
  /* Enhanced module container transition */
  .module-container {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .module-container:hover {
    transform: translateY(-2px);
  }
  
  /* Enhanced lesson items transition */
  .lesson-item {
    transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .lesson-item:hover {
    transform: translateX(4px);
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --shadow-subtle: 0 2px 12px rgba(0, 0, 0, 0.2);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

/* Extra large screens (1440px and up) */
@media (min-width: 1440px) {
  .course-detail-container {
    --content-max-width: 1400px;
    --content-padding-x: 2rem;
    --section-spacing: 2rem;
  }
  
  .course-title {
    font-size: 2rem;
  }
} 