/* Floating Sidebar Layout Utilities */

/* CSS Custom Properties for consistent spacing */
:root {
  /* Floating sidebar dimensions */
  --floating-sidebar-width: 18rem; /* 72 in Tailwind (w-72) */
  --floating-sidebar-margin: 0.75rem; /* 3 in Tailwind (m-3) */

  /* Content spacing - REDUCED for better design consistency */
  --content-offset-desktop: 1rem; /* Reduced from 2rem - Extra space from floating sidebar */
  --content-padding-mobile: 1rem;
  --content-padding-desktop: 1rem; /* Reduced from 1.5rem for tighter spacing */
  
  /* Page-specific max widths */
  --page-width-default: 56rem; /* max-w-4xl */
  --page-width-dashboard: 80rem; /* max-w-6xl */
  --page-width-lesson: 100%; /* full width */
  --page-width-certificate: 64rem; /* max-w-5xl */
  
  /* Responsive breakpoints */
  --mobile-breakpoint: 768px;
  --desktop-breakpoint: 1024px;
}

/* Base layout classes for floating sidebar */
.floating-sidebar-layout {
  display: flex;
  min-height: 100vh;
  background: hsl(var(--background));
}

/* Main content area adjustments */
.floating-sidebar-content {
  flex: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Mobile: No sidebar offset */
@media (max-width: 767px) {
  .floating-sidebar-content {
    margin-left: 0;
    width: 100%;
  }
  
  .page-container {
    padding: var(--content-padding-mobile);
  }
  
  .content-section {
    padding: 0;
  }
}

/* Desktop: Account for floating sidebar */
@media (min-width: 768px) {
  .floating-sidebar-content {
    margin-left: var(--floating-sidebar-width);
  }

  .page-container {
    padding: var(--content-padding-desktop);
    padding-left: var(--content-offset-desktop);
  }

  .content-section {
    padding: 0 0.25rem; /* Reduced from 0.5rem for tighter spacing */
  }
}

/* Page-specific container classes */
.page-container-default {
  max-width: var(--page-width-default);
  margin: 0 auto;
}

.page-container-dashboard {
  max-width: var(--page-width-dashboard);
  margin: 0 auto;
}

.page-container-lesson {
  max-width: var(--page-width-lesson);
  padding: 0;
}

.page-container-certificate {
  max-width: var(--page-width-certificate);
  margin: 0 auto;
}

/* Content spacing utilities */
.content-spacing-sm {
  --content-gap: 0.75rem;
}

.content-spacing-md {
  --content-gap: 1rem;
}

.content-spacing-lg {
  --content-gap: 1.5rem;
}

.content-spacing-xl {
  --content-gap: 2rem;
}

/* Responsive content grids */
.content-grid {
  display: grid;
  gap: var(--content-gap, 1rem);
  width: 100%;
}

.content-grid-1 {
  grid-template-columns: 1fr;
}

.content-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.content-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.content-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
}

/* Mobile adjustments for grids */
@media (max-width: 767px) {
  .content-grid-2,
  .content-grid-3,
  .content-grid-4 {
    grid-template-columns: 1fr;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-grid-3,
  .content-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Floating sidebar specific adjustments */
.floating-sidebar-aware {
  /* Ensures content doesn't overlap with floating sidebar */
  position: relative;
  z-index: 1;
}

/* Lesson content specific styles */
.lesson-content-container {
  width: 100%;
  max-width: none;
}

@media (min-width: 768px) {
  .lesson-content-container {
    /* Account for floating sidebar on lesson pages */
    padding-left: 0;
  }
  
  .lesson-content-inner {
    max-width: 56rem; /* 4xl */
    margin: 0 auto;
    padding: 0 2rem;
  }
}

/* Dashboard specific styles */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 768px) {
  .dashboard-container {
    gap: 2.5rem;
  }
}

/* Module list specific styles */
.module-list-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .module-list-container {
    gap: 1.5rem;
  }
}

/* Certificate page specific styles */
.certificate-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .certificate-container {
    gap: 2rem;
  }
}

/* Utility classes for consistent spacing */
.floating-sidebar-safe-area {
  /* Ensures content has proper spacing from floating sidebar */
  margin-left: 0;
}

@media (min-width: 768px) {
  .floating-sidebar-safe-area {
    margin-left: 1rem;
  }
}

/* Animation classes for smooth transitions */
.content-fade-in {
  animation: contentFadeIn 0.3s ease-out forwards;
}

@keyframes contentFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus and accessibility improvements */
.floating-sidebar-content:focus-within {
  /* Ensure focus is visible when content has focus */
  outline: none;
}

/* Print styles */
@media print {
  .floating-sidebar-content {
    margin-left: 0 !important;
    padding: 1rem !important;
  }
  
  .page-container {
    max-width: none !important;
    padding: 0 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .floating-sidebar-content {
    border-left: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-sidebar-content,
  .content-fade-in {
    transition: none !important;
    animation: none !important;
  }
}
