/* Mobile Utility Classes */

/* Hide elements on mobile */
.hide-on-mobile {
  @apply hidden sm:block;
}

/* Hide elements on desktop */
.hide-on-desktop {
  @apply block sm:hidden;
}

/* Improved touch targets */
.touch-target {
  @apply min-h-[44px] min-w-[44px];
  @apply touch-manipulation;
  min-height: 44px;
  min-width: 44px;
}

/* Mobile-friendly spacing */
.mobile-spacing {
  @apply p-4 sm:p-6;
}

.mobile-spacing-x {
  @apply px-4 sm:px-6;
}

.mobile-spacing-y {
  @apply py-4 sm:py-6;
}

/* Mobile-friendly text */
.mobile-text {
  @apply text-base sm:text-sm;
}

.mobile-heading {
  @apply text-xl sm:text-2xl font-bold;
}

.mobile-subheading {
  @apply text-lg sm:text-xl font-semibold;
}

/* Mobile-friendly grid */
.mobile-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6;
}

/* Mobile-friendly flex */
.mobile-flex {
  @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
}

/* Mobile-friendly card */
.mobile-card {
  @apply rounded-xl bg-white dark:bg-gray-900 shadow-sm border border-gray-100 dark:border-gray-800;
  @apply p-4 sm:p-6;
  @apply w-full;
}

/* Mobile-friendly button */
.mobile-button {
  @apply rounded-full bg-red-500 text-white font-medium;
  @apply px-4 py-2 sm:py-2.5;
  @apply min-h-[44px];
  @apply flex items-center justify-center gap-2;
  @apply touch-manipulation;
  @apply transition-all duration-200;
  @apply active:bg-red-600;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
}

/* Mobile-friendly input */
.mobile-input {
  @apply rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900;
  @apply px-4 py-3;
  @apply w-full;
  @apply min-h-[44px];
  @apply text-base sm:text-sm;
  @apply appearance-none;
  @apply touch-manipulation;
}

/* Mobile-friendly scrolling */
.mobile-scroll-x {
  @apply overflow-x-auto;
  @apply -mx-4 px-4 sm:mx-0 sm:px-0;
  @apply pb-1;
  scrollbar-width: none; /* Firefox */
}
.mobile-scroll-x::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.mobile-scroll-y {
  @apply overflow-y-auto;
  @apply max-h-[80vh];
  scrollbar-width: none; /* Firefox */
}
.mobile-scroll-y::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

/* Mobile-friendly list */
.mobile-list {
  @apply space-y-3 sm:space-y-2;
}

.mobile-list-item {
  @apply flex items-center gap-3;
  @apply min-h-[44px] sm:min-h-[40px];
  @apply py-2 px-3 sm:px-2;
  @apply rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800;
  @apply transition-colors duration-200;
}

/* Mobile-friendly divider */
.mobile-divider {
  @apply h-px w-full bg-gray-100 dark:bg-gray-800;
  @apply my-4 sm:my-6;
}

/* Mobile-friendly container */
.mobile-container {
  @apply w-full mx-auto px-4 sm:px-6 py-4 sm:py-6;
  @apply max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl;
}

/* Mobile-friendly section */
.mobile-section {
  @apply py-6 sm:py-8 md:py-10;
  @apply space-y-4 sm:space-y-6;
}

/* Mobile-friendly form */
.mobile-form {
  @apply space-y-4 sm:space-y-6;
}

.mobile-form-group {
  @apply flex flex-col gap-1.5;
}

.mobile-form-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.mobile-form-helper {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
}

/* Mobile-friendly animations */
.mobile-transition {
  @apply transition-all duration-200 ease-out;
}

/* Mobile-friendly focus states */
.mobile-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:ring-offset-2;
}

/* Mobile reading optimizations */
.mobile-readable {
  /* Comfortable reading width */
  max-width: 100ch;
  margin: 0 auto;
  /* Improve text readability */
  font-size: 16px;
  line-height: 1.6;
  letter-spacing: -0.011em;
  /* Better touch scrolling */
  -webkit-overflow-scrolling: touch;
  /* Prevent horizontal overflow */
  overflow-wrap: break-word;
  word-wrap: break-word;
  /* Smooth font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Content spacing for mobile */
.mobile-content-spacing {
  padding: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .mobile-content-spacing {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

/* Image optimizations for mobile */
.mobile-image {
  max-width: 100%;
  height: auto;
  /* Prevent layout shift */
  aspect-ratio: attr(width) / attr(height);
  /* Smooth loading */
  loading: lazy;
}

/* Tables for mobile */
.mobile-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin: 1rem 0;
  /* Indicate scrollable content */
  background-image: linear-gradient(to right, white 30%, rgba(255, 255, 255, 0)),
                    linear-gradient(to right, rgba(255, 255, 255, 0), white 70%);
  background-position: 0 0, 100% 0;
  background-repeat: no-repeat;
  background-size: 20px 100%;
  background-attachment: local, local;
}

/* Touch-friendly buttons */
.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
  touch-action: manipulation;
}

/* Readable font sizes */
.mobile-text-base {
  font-size: 16px;
  line-height: 1.6;
}

.mobile-text-sm {
  font-size: 14px;
  line-height: 1.5;
}

.mobile-text-lg {
  font-size: 18px;
  line-height: 1.7;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .mobile-table-container {
    background-image: linear-gradient(to right, #1a1a1a 30%, rgba(26, 26, 26, 0)),
                      linear-gradient(to right, rgba(26, 26, 26, 0), #1a1a1a 70%);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .mobile-readable {
    scroll-behavior: auto;
  }
}

/* Mobile Utilities and Overrides */

/* Better tap targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Improved scrolling */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior-y: contain;
}

/* Remove tap highlight on mobile */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Mobile-specific paddings */
.mobile-safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-safe-top {
  padding-top: env(safe-area-inset-top);
}

/* Mobile menu adjustments */
.mobile-menu {
  height: calc(100vh - env(safe-area-inset-bottom));
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile-friendly buttons */
.mobile-button {
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
}

/* Improved form inputs on mobile */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
textarea {
  font-size: 16px !important;
  min-height: 44px;
  border-radius: 9999px;
}

/* Mobile scrolling */
.mobile-scroll {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.mobile-scroll::-webkit-scrollbar {
  display: none;
}

/* Mobile active states */
.mobile-active {
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-active:active {
  transform: scale(0.98);
}

/* Mobile-specific breakpoints */
@media (max-width: 768px) {
  .text-mobile {
    font-size: 0.9375rem;
  }

  .card-mobile {
    margin: 12px 8px;
    padding: 16px;
    border-radius: 16px;
  }

  .heading-mobile {
    font-size: 1.125rem;
    line-height: 1.3;
  }

  .content-mobile {
    font-size: 0.9375rem;
    line-height: 1.5;
  }

  .section-mobile {
    padding: 24px 16px;
  }

  .container-mobile {
    padding-left: 16px;
    padding-right: 16px;
  }

  .gap-mobile {
    gap: 16px;
  }

  .grid-mobile-1 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .grid-mobile-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

/* iOS-specific fixes */
@supports (-webkit-touch-callout: none) {
  .ios-momentum-scroll {
    -webkit-overflow-scrolling: touch;
  }

  .ios-button {
    cursor: pointer;
    touch-action: manipulation;
  }
}