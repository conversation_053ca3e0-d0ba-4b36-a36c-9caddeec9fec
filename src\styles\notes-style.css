/* Notes app styling for mobile */
.notes-style-container {
  isolation: isolate;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@media (max-width: 768px) {
  .notes-style-container {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .notes-style-content {
    font-size: 0.8125rem !important; /* 13px */
    line-height: 1.5 !important;
    padding: 8px 12px !important;
  }

  .notes-style-content h1 {
    font-size: 1.125rem !important; /* 18px */
    line-height: 1.3 !important;
  }

  .notes-style-content h2 {
    font-size: 1rem !important; /* 16px */
    line-height: 1.35 !important;
  }

  .notes-style-content h3 {
    font-size: 0.9375rem !important; /* 15px */
    line-height: 1.4 !important;
  }

  .notes-style-content h1,
  .notes-style-content h2,
  .notes-style-content h3 {
    padding-top: 8px !important;
    margin-top: 16px !important;
    margin-bottom: 8px !important;
  }

  /* Notes app style spacing */
  .notes-style-content > * + * {
    margin-top: 12px !important;
  }

  /* Better touch feedback */
  .notes-style-content a {
    -webkit-tap-highlight-color: rgba(230, 57, 70, 0.1);
    padding: 2px 0;
  }

  /* Smooth scrolling */
  .notes-style-container {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}