/* Professional Lesson Content Styling - Uses Unified Typography System */

/* Import unified typography system */
@import './unified-typography.css';

/* Professional lesson content container - Clean and Simple */
.professional-lesson-container {
  width: 100%;
  max-width: 75ch; /* Optimal reading width */
  margin: 0 auto;
  font-family: var(--font-primary);
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  padding: 2rem 1.5rem;
  line-height: 1.7;
  transition: all 0.3s ease;
}

/* Full-width variant for immersive content */
.professional-lesson-container.full-width {
  max-width: none;
  width: 100%;
  padding: 3rem 2rem;
  background-color: hsl(var(--background));
}

/* Responsive padding adjustments - Simplified */
@media (min-width: 640px) {
  .professional-lesson-container {
    padding: 2.5rem 2rem;
  }

  .professional-lesson-container.full-width {
    padding: 3.5rem 3rem;
  }
}

@media (min-width: 768px) {
  .professional-lesson-container {
    padding: 3rem 2.5rem;
  }

  .professional-lesson-container.full-width {
    padding: 4rem 4rem;
  }
}

@media (min-width: 1024px) {
  .professional-lesson-container.full-width {
    padding: 4rem 6rem;
  }
}

@media (min-width: 1280px) {
  .professional-lesson-container.full-width {
    padding: 4rem 8rem;
  }
}

/* Professional prose styling - Clean and readable */
.professional-prose {
  max-width: none;
  color: hsl(var(--foreground));
  font-size: 1rem;
  line-height: 1.7;
  letter-spacing: -0.01em;
  font-weight: 400;
}

/* Professional heading styles - Clean hierarchy */
.professional-prose h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: hsl(var(--foreground));
  margin: 2.5rem 0 1rem;
}

.professional-prose h1:first-child {
  margin-top: 0;
}

.professional-prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.02em;
  color: hsl(var(--foreground));
  margin: 2rem 0 0.75rem;
}

.professional-prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: -0.015em;
  color: hsl(var(--foreground));
  margin: 1.5rem 0 0.5rem;
}

.professional-prose h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  line-height: var(--leading-snug);
  color: hsl(var(--foreground));
  margin: var(--space-lg) 0 var(--space-sm);
}

.professional-prose h5 {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  color: hsl(var(--foreground));
  margin: var(--space-lg) 0 var(--space-sm);
}

.professional-prose h6 {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
  color: hsl(var(--muted-foreground));
  margin: var(--space-lg) 0 var(--space-sm);
}

/* Professional paragraph styling - Clean and readable */
.professional-prose p {
  font-size: 1rem;
  line-height: 1.7;
  color: hsl(var(--foreground));
  margin: 1rem 0;
}

.professional-prose p:first-child {
  margin-top: 0;
}

.professional-prose p:last-child {
  margin-bottom: 0;
}

/* Professional list styling - Clean spacing */
.professional-prose ul,
.professional-prose ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.professional-prose li {
  font-size: 1rem;
  line-height: 1.7;
  margin: 0.5rem 0;
  color: hsl(var(--foreground));
}

.professional-prose li p {
  margin: 0.5rem 0;
}

/* Professional emphasis styling */
.professional-prose strong {
  font-weight: var(--font-semibold);
  color: hsl(var(--foreground));
}

.professional-prose em {
  font-style: italic;
  color: hsl(var(--foreground));
}

/* Professional link styling */
.professional-prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-color: hsl(var(--primary) / 0.3);
  text-underline-offset: 0.125rem;
  transition: all 0.2s ease;
}

.professional-prose a:hover {
  color: hsl(var(--primary));
  text-decoration-color: hsl(var(--primary));
}

/* Professional code styling */
.professional-prose code {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: var(--space-xs) var(--space-sm);
  border-radius: 0.25rem;
  font-weight: var(--font-medium);
}

.professional-prose pre {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: var(--space-xl);
  border-radius: 0.5rem;
  margin: var(--space-xl) 0;
  overflow-x: auto;
  border: 1px solid hsl(var(--border));
}

.professional-prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* Professional blockquote styling */
.professional-prose blockquote {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  font-style: italic;
  color: hsl(var(--muted-foreground));
  background-color: hsl(var(--muted) / 0.5);
  border-left: 4px solid hsl(var(--primary));
  padding: var(--space-lg) var(--space-xl);
  margin: var(--space-xl) 0;
  border-radius: 0 0.5rem 0.5rem 0;
}

/* Professional table styling - Enhanced for clean, modern look */
.professional-prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background-color: hsl(var(--card));
}

.professional-prose th,
.professional-prose td {
  padding: 0.875rem 1rem;
  text-align: left;
  border-bottom: 1px solid hsl(var(--border));
  vertical-align: top;
  line-height: 1.5;
}

.professional-prose th {
  background-color: hsl(var(--muted) / 0.8);
  font-weight: 600;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  border-bottom: 2px solid hsl(var(--border));
}

.professional-prose td {
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
  font-size: 0.875rem;
}

.professional-prose tbody tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.2);
}

.professional-prose tbody tr:hover td {
  background-color: hsl(var(--muted) / 0.4);
  transition: background-color 0.15s ease;
}

.professional-prose tbody tr:last-child td {
  border-bottom: none;
}

/* Table wrapper for responsive scrolling - Enhanced */
.professional-prose .table-wrapper {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid hsl(var(--border));
  margin: 1.5rem 0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background-color: hsl(var(--card));
  -webkit-overflow-scrolling: touch;
}

.professional-prose .table-wrapper table {
  margin: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
  background-color: transparent;
}

/* Ensure tables are visible and properly styled */
.professional-prose table,
.professional-prose .table-wrapper table {
  display: table !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Global table fixes for markdown content - Enhanced */
.table-wrapper {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid hsl(var(--border));
  margin: 1.5rem 0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background-color: hsl(var(--card));
  -webkit-overflow-scrolling: touch;
}

.table-wrapper table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
  font-size: 0.875rem;
  background-color: transparent;
}

.table-wrapper th,
.table-wrapper td {
  padding: 0.875rem 1rem;
  text-align: left;
  border-bottom: 1px solid hsl(var(--border));
  vertical-align: top;
  line-height: 1.5;
}

.table-wrapper th {
  background-color: hsl(var(--muted) / 0.8);
  font-weight: 600;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  border-bottom: 2px solid hsl(var(--border));
}

.table-wrapper td {
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
  font-size: 0.875rem;
}

.table-wrapper tbody tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.2);
}

.table-wrapper tbody tr:hover td {
  background-color: hsl(var(--muted) / 0.4);
  transition: background-color 0.15s ease;
}

.table-wrapper tbody tr:last-child td {
  border-bottom: none;
}

/* Professional image styling */
.professional-prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: var(--space-xl) auto;
  display: block;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.professional-prose img:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Professional horizontal rule */
.professional-prose hr {
  border: none;
  height: 1px;
  background-color: hsl(var(--border));
  margin: var(--space-3xl) 0;
}

/* Responsive typography adjustments - Clean scaling */
@media (min-width: 768px) {
  .professional-prose {
    font-size: 1.125rem;
  }

  .professional-prose h1 {
    font-size: 2.5rem;
  }

  .professional-prose h2 {
    font-size: 1.875rem;
  }

  .professional-prose h3 {
    font-size: 1.5rem;
  }

  .professional-prose p,
  .professional-prose li {
    font-size: 1.125rem;
  }
}

@media (min-width: 1024px) {
  .professional-prose h1 {
    font-size: 3rem;
  }

  .professional-prose h2 {
    font-size: 2.25rem;
  }
}

/* Professional spacing utilities */
.professional-prose > * + * {
  margin-top: var(--space-lg);
}

.professional-prose h1 + *,
.professional-prose h2 + *,
.professional-prose h3 + * {
  margin-top: var(--space-md);
}

.professional-prose * + h1 {
  margin-top: var(--space-3xl);
}

.professional-prose * + h2 {
  margin-top: var(--space-2xl);
}

.professional-prose * + h3 {
  margin-top: var(--space-xl);
}

/* Professional figure styling */
.professional-prose figure {
  margin: var(--space-2xl) 0;
  text-align: center;
}

.professional-prose figcaption {
  font-size: var(--text-sm);
  color: hsl(var(--muted-foreground));
  margin-top: var(--space-sm);
  font-style: italic;
}

/* Professional video styling */
.professional-prose .video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin: var(--space-xl) 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.professional-prose .video-container iframe,
.professional-prose .video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Professional accordion styling for references */
.professional-prose .modern-accordion {
  margin: var(--space-2xl) 0;
}

.professional-prose .modern-accordion-item {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.professional-prose .modern-accordion-trigger {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: hsl(var(--foreground));
  padding: var(--space-lg);
  background: hsl(var(--muted) / 0.3);
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.professional-prose .modern-accordion-trigger:hover {
  background: hsl(var(--muted) / 0.5);
}

.professional-prose .modern-accordion-content {
  padding: var(--space-lg);
  background: hsl(var(--card));
}

/* Professional mobile optimizations - Clean and readable */
@media (max-width: 640px) {
  .professional-lesson-container {
    padding: 1.5rem 1rem;
    border-radius: 0;
    margin: 0;
  }

  .professional-prose {
    font-size: 0.9rem;
  }

  .professional-prose h1 {
    font-size: 1.75rem;
    margin: 2rem 0 0.75rem;
  }

  .professional-prose h2 {
    font-size: 1.375rem;
    margin: 1.5rem 0 0.5rem;
  }

  .professional-prose h3 {
    font-size: 1.125rem;
    margin: 1.25rem 0 0.5rem;
  }

  .professional-prose p,
  .professional-prose li {
    font-size: 0.9rem;
    margin: 0.75rem 0;
  }

  .professional-prose > * + * {
    margin-top: 0.75rem;
  }

  .professional-prose ul,
  .professional-prose ol {
    padding-left: 1.25rem;
  }

  .professional-prose pre {
    padding: 1rem;
    margin: 1rem 0;
    font-size: 0.8rem;
  }

  .professional-prose table {
    font-size: 0.8rem;
  }

  .professional-prose th,
  .professional-prose td {
    padding: 0.5rem 0.75rem;
  }
}

/* Dark mode support */
.dark .professional-lesson-container {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
}

.dark .professional-lesson-container.full-width {
  background-color: hsl(var(--background));
}

.dark .professional-prose .modern-accordion-item {
  background: hsl(var(--card));
  border-color: hsl(var(--border));
}

.dark .professional-prose .modern-accordion-trigger {
  background: hsl(var(--muted) / 0.3);
  color: hsl(var(--foreground));
}

.dark .professional-prose .modern-accordion-trigger:hover {
  background: hsl(var(--muted) / 0.5);
}

.dark .professional-prose .modern-accordion-content {
  background: hsl(var(--card));
}
