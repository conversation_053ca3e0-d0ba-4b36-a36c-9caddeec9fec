/* TipTap Editor Styles */

.ProseMirror {
  outline: none;
}

.ProseMirror p {
  margin-bottom: 1em;
}

.ProseMirror h1 {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin-bottom: 0.5em;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2em 0;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: monospace;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.375rem;
  font-family: monospace;
  overflow-x: auto;
  margin-bottom: 1em;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

.ProseMirror mark {
  background-color: #fef3c7;
  padding: 0.1em 0.2em;
  border-radius: 0.25em;
}

.ProseMirror [data-youtube-video] {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  margin-bottom: 1em;
}

.ProseMirror [data-youtube-video] iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0.375rem;
}

/* Text alignment */
.ProseMirror .text-left {
  text-align: left;
}

.ProseMirror .text-center {
  text-align: center;
}

.ProseMirror .text-right {
  text-align: right;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Editor focus styles */
.ProseMirror:focus {
  outline: none;
}
