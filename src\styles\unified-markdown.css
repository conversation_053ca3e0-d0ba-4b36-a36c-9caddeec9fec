/* Unified Markdown Editor and Preview Styles */
/* Consolidates all markdown-related CSS into a single, consistent system */

/* Base Editor Styles */
.unified-markdown-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

/* GitHub Theme */
.github-markdown-editor .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 400px;
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

.github-markdown-editor .ProseMirror h1,
.github-markdown-editor .ProseMirror h2,
.github-markdown-editor .ProseMirror h3,
.github-markdown-editor .ProseMirror h4,
.github-markdown-editor .ProseMirror h5,
.github-markdown-editor .ProseMirror h6 {
  font-weight: 600;
  line-height: 1.25;
  margin: 1.5em 0 0.5em 0;
  color: var(--foreground);
}

.github-markdown-editor .ProseMirror h1 {
  font-size: 2em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.github-markdown-editor .ProseMirror h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.github-markdown-editor .ProseMirror h3 {
  font-size: 1.25em;
}

/* Obsidian Theme */
.obsidian-editor .ProseMirror {
  outline: none;
  padding: 2rem;
  min-height: 500px;
  max-width: none;
  background-color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.obsidian-editor .ProseMirror h1,
.obsidian-editor .ProseMirror h2,
.obsidian-editor .ProseMirror h3,
.obsidian-editor .ProseMirror h4,
.obsidian-editor .ProseMirror h5,
.obsidian-editor .ProseMirror h6 {
  font-weight: 600;
  line-height: 1.3;
  margin: 1.5em 0 0.5em 0;
  color: var(--foreground);
}

/* Minimal Theme */
.minimal-editor .ProseMirror {
  outline: none;
  padding: 1.5rem;
  min-height: 300px;
  font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  font-size: 16px;
  line-height: 1.7;
}

/* Common Text Formatting */
.ProseMirror p {
  margin: 0.75em 0;
}

.ProseMirror strong {
  font-weight: 600;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror u {
  text-decoration: underline;
}

.ProseMirror s,
.ProseMirror del {
  text-decoration: line-through;
}

.ProseMirror mark {
  background-color: hsl(var(--warning) / 0.3);
  padding: 0.1em 0.2em;
  border-radius: 0.2em;
}

/* Code Styling */
.ProseMirror code {
  background-color: var(--muted);
  border-radius: 3px;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
}

.ProseMirror pre {
  background-color: var(--muted);
  border-radius: 6px;
  color: var(--foreground);
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  line-height: 1.45;
  margin: 1em 0;
  overflow: auto;
  padding: 16px;
  border: 1px solid var(--border);
}

.ProseMirror pre code {
  background: none;
  border-radius: 0;
  color: inherit;
  font-size: inherit;
  padding: 0;
}

/* Lists */
.ProseMirror ul,
.ProseMirror ol {
  margin: 1em 0;
  padding-left: 2em;
}

.ProseMirror li {
  margin: 0.25em 0;
}

.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.ProseMirror li[data-type="taskItem"] {
  display: flex;
  align-items: flex-start;
  gap: 0.5em;
}

.ProseMirror li[data-type="taskItem"] > label {
  flex-shrink: 0;
  margin-top: 0.1em;
}

.ProseMirror li[data-type="taskItem"] > div {
  flex: 1;
}

/* Blockquotes */
.ProseMirror blockquote {
  border-left: 4px solid hsl(var(--primary));
  color: var(--muted-foreground);
  font-style: italic;
  margin: 1.5em 0;
  padding: 0.5em 0 0.5em 1.5em;
  background-color: var(--muted);
  border-radius: 0 8px 8px 0;
}

/* Tables - Editor and Preview */
.ProseMirror table,
.markdown-preview table,
.professional-prose table {
  border-collapse: collapse !important;
  border: 1px solid hsl(var(--border)) !important;
  width: 100% !important;
  margin: 0.75rem 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* Table wrapper spacing fix */
.table-wrapper {
  margin: 0.75rem 0 !important;
  overflow-x: auto !important;
  border-radius: 8px !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.table-wrapper table {
  margin: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.ProseMirror table th,
.ProseMirror table td,
.markdown-preview table th,
.markdown-preview table td,
.professional-prose table th,
.professional-prose table td {
  border: 1px solid hsl(var(--border)) !important;
  padding: 0.75rem 1rem !important;
  text-align: left !important;
  vertical-align: top !important;
}

.ProseMirror table th,
.markdown-preview table th,
.professional-prose table th {
  background-color: hsl(var(--muted)) !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
}

.ProseMirror table td,
.markdown-preview table td,
.professional-prose table td {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

.ProseMirror table tr:nth-child(even) td,
.markdown-preview table tr:nth-child(even) td,
.professional-prose table tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.3) !important;
}

.ProseMirror table tr:hover td,
.markdown-preview table tr:hover td,
.professional-prose table tr:hover td {
  background-color: hsl(var(--muted) / 0.5) !important;
  transition: background-color 0.2s ease !important;
}

/* Links */
.ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 2px;
}

.ProseMirror a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1em 0;
}

/* Horizontal Rules */
.ProseMirror hr {
  border: none;
  border-top: 2px solid var(--border);
  margin: 2em 0;
}

/* Custom Extensions */

/* Details/Summary */
.ProseMirror details {
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  background-color: var(--muted) / 0.3;
}

.ProseMirror summary {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ProseMirror summary:hover {
  color: hsl(var(--primary));
}

.ProseMirror details[open] summary {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
}

/* Callouts */
.ProseMirror div[data-callout] {
  border-left: 4px solid;
  border-radius: 0 8px 8px 0;
  padding: 1rem;
  margin: 1rem 0;
}

.ProseMirror div[data-callout][data-type="info"] {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.1);
}

.ProseMirror div[data-callout][data-type="warning"] {
  border-color: hsl(var(--warning));
  background-color: hsl(var(--warning) / 0.1);
}

.ProseMirror div[data-callout][data-type="success"] {
  border-color: hsl(var(--success));
  background-color: hsl(var(--success) / 0.1);
}

.ProseMirror div[data-callout][data-type="error"] {
  border-color: hsl(var(--destructive));
  background-color: hsl(var(--destructive) / 0.1);
}

/* Preview Styles */
.github-markdown-preview,
.obsidian-markdown-preview,
.minimal-markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  color: var(--foreground);
}

.github-markdown-preview h1,
.github-markdown-preview h2 {
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.obsidian-markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.minimal-markdown-preview {
  font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  line-height: 1.7;
}

/* Dark mode adjustments */
.dark .ProseMirror mark {
  background-color: hsl(var(--warning) / 0.4);
}

.dark .ProseMirror img {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .ProseMirror img:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ProseMirror {
    padding: 1rem;
    font-size: 15px;
  }

  .obsidian-editor .ProseMirror {
    padding: 1rem;
  }

  .ProseMirror h1 {
    font-size: 1.8em;
  }

  .ProseMirror h2 {
    font-size: 1.5em;
  }

  .ProseMirror h3 {
    font-size: 1.3em;
  }

  .ProseMirror table,
  .markdown-preview table,
  .professional-prose table {
    font-size: 14px;
  }

  .ProseMirror table th,
  .ProseMirror table td,
  .markdown-preview table th,
  .markdown-preview table td,
  .professional-prose table th,
  .professional-prose table td {
    padding: 8px 12px !important;
  }
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

.ProseMirror.ProseMirror-focused {
  outline: none;
}

/* Selection styles */
.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  color: var(--muted-foreground);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
