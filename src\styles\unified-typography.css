/* Unified Typography System for i-can-iv e-learning platform */

/* Professional Typography System - Single source of truth */
:root {
  /* Font families */
  --font-primary: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Professional font size scale - optimized for readability */
  --text-xs: 0.75rem;     /* 12px - captions, labels */
  --text-sm: 0.875rem;    /* 14px - small text, metadata */
  --text-base: 1rem;      /* 16px - body text baseline */
  --text-lg: 1.125rem;    /* 18px - large body text */
  --text-xl: 1.25rem;     /* 20px - small headings */
  --text-2xl: 1.5rem;     /* 24px - medium headings */
  --text-3xl: 1.875rem;   /* 30px - large headings */
  --text-4xl: 2.25rem;    /* 36px - extra large headings */
  --text-5xl: 3rem;       /* 48px - display headings */

  /* Font weights - semantic naming */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line heights - optimized for readability */
  --leading-tight: 1.25;    /* Headings */
  --leading-snug: 1.375;    /* Subheadings */
  --leading-normal: 1.5;    /* UI text */
  --leading-relaxed: 1.625; /* Body text */
  --leading-loose: 1.75;    /* Long-form content */

  /* Letter spacing - professional typography */
  --tracking-tighter: -0.05em;  /* Large headings */
  --tracking-tight: -0.025em;   /* Headings */
  --tracking-normal: 0em;       /* Body text */
  --tracking-wide: 0.025em;     /* Uppercase text */
  --tracking-wider: 0.05em;     /* Spaced text */

  /* Content spacing system */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 0.75rem;    /* 12px */
  --space-lg: 1rem;       /* 16px */
  --space-xl: 1.5rem;     /* 24px */
  --space-2xl: 2rem;      /* 32px */
  --space-3xl: 3rem;      /* 48px */
  --space-4xl: 4rem;      /* 64px */

  /* Reading-optimized content widths */
  --content-width-narrow: 45ch;   /* Narrow reading column */
  --content-width-optimal: 65ch;  /* Optimal reading width */
  --content-width-wide: 75ch;     /* Wide reading column */
  --content-width-full: 100%;     /* Full width */
}

/* Professional base typography */
html {
  font-family: var(--font-primary);
  font-size: 16px;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  color: hsl(var(--foreground));
}

/* Professional heading hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0;
  color: hsl(var(--foreground));
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  letter-spacing: var(--tracking-tighter);
  line-height: var(--leading-tight);
}

h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-semibold);
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-tight);
}

h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-snug);
}

h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-snug);
}

h5 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
}

h6 {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

/* Professional paragraph styling */
p {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  margin: 0;
  color: hsl(var(--foreground));
}

/* Professional text utilities */
.text-xs { font-size: var(--text-xs); line-height: var(--leading-normal); }
.text-sm { font-size: var(--text-sm); line-height: var(--leading-normal); }
.text-base { font-size: var(--text-base); line-height: var(--leading-relaxed); }
.text-lg { font-size: var(--text-lg); line-height: var(--leading-relaxed); }
.text-xl { font-size: var(--text-xl); line-height: var(--leading-snug); }
.text-2xl { font-size: var(--text-2xl); line-height: var(--leading-tight); }
.text-3xl { font-size: var(--text-3xl); line-height: var(--leading-tight); }
.text-4xl { font-size: var(--text-4xl); line-height: var(--leading-tight); }
.text-5xl { font-size: var(--text-5xl); line-height: var(--leading-tight); }

/* Headings - Consistent hierarchy */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0;
}

h1, .h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
}

h2, .h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
}

h3, .h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

h4, .h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
}

h5, .h5 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

h6, .h6 {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

/* Responsive headings for mobile */
@media (max-width: 640px) {
  h1, .h1 {
    font-size: var(--text-3xl);
  }
  
  h2, .h2 {
    font-size: var(--text-2xl);
  }
  
  h3, .h3 {
    font-size: var(--text-xl);
  }
}

/* Body text */
p, .text-body {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  margin: 0;
}

/* Text size utilities */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

/* Font weight utilities */
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

/* Line height utilities */
.leading-tight { line-height: var(--leading-tight); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }

/* Letter spacing utilities */
.tracking-tight { letter-spacing: var(--tracking-tight); }
.tracking-normal { letter-spacing: var(--tracking-normal); }
.tracking-wide { letter-spacing: var(--tracking-wide); }

/* Navigation typography */
.nav-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Button typography */
.button-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Card title typography */
.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}

/* Card description typography */
.card-description {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Small text */
.text-small, small {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
}

/* Code and monospace */
code, pre, .code, .pre {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
}

/* Mobile-optimized text sizes */
@media (max-width: 640px) {
  .nav-text {
    font-size: var(--text-base);
  }
  
  .button-text {
    font-size: var(--text-base);
  }
  
  .card-title {
    font-size: var(--text-xl);
  }
  
  .card-description {
    font-size: var(--text-base);
  }
}

/* Professional text colors */
.text-primary-content {
  color: hsl(var(--primary-foreground));
}

.text-muted {
  color: hsl(var(--muted-foreground));
}

.text-subtle {
  opacity: 0.7;
}

/* Text alignment utilities */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Text decoration utilities */
.no-underline { text-decoration: none; }
.underline { text-decoration: underline; }

/* Text transform utilities */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

/* Text overflow utilities */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
