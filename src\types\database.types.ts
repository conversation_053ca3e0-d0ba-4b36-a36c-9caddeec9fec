export interface Database {
  public: {
    Tables: {
      courses: {
        Row: {
          id: string;
          title: string;
          slug: string;
          description: string;
          instructor: string;
          total_modules: number;
          completed_modules: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          description: string;
          instructor: string;
          total_modules?: number;
          completed_modules?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          description?: string;
          instructor?: string;
          total_modules?: number;
          completed_modules?: number;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      modules: {
        Row: {
          id: string;
          course_id: string;
          title: string;
          slug: string;
          module_number: number;
          is_locked: boolean;
          is_completed: boolean;
          created_at: string;
          updated_at: string;
          image_url?: string;
          image_width?: number;
          image_height?: number;
          image_alt_text?: string;
          image_size_bytes?: number;
        };
        Insert: {
          id?: string;
          course_id: string;
          title: string;
          slug: string;
          module_number: number;
          is_locked?: boolean;
          is_completed?: boolean;
          created_at?: string;
          updated_at?: string;
          image_url?: string;
          image_width?: number;
          image_height?: number;
          image_alt_text?: string;
          image_size_bytes?: number;
        };
        Update: {
          id?: string;
          course_id?: string;
          title?: string;
          slug?: string;
          module_number?: number;
          is_locked?: boolean;
          is_completed?: boolean;
          created_at?: string;
          updated_at?: string;
          image_url?: string;
          image_width?: number;
          image_height?: number;
          image_alt_text?: string;
          image_size_bytes?: number;
        };
        Relationships: [
          {
            foreignKeyName: "modules_course_id_fkey";
            columns: ["course_id"];
            referencedRelation: "courses";
            referencedColumns: ["id"];
          }
        ];
      };
      lessons: {
        Row: {
          id: string;
          module_id: string;
          title: string;
          slug: string;
          duration: string;
          type: string;
          requirement?: string;
          content?: string;
          completed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          module_id: string;
          title: string;
          slug: string;
          duration: string;
          type?: string;
          requirement?: string;
          content?: string;
          completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          module_id?: string;
          title?: string;
          slug?: string;
          duration?: string;
          type?: string;
          requirement?: string;
          content?: string;
          completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "lessons_module_id_fkey";
            columns: ["module_id"];
            referencedRelation: "modules";
            referencedColumns: ["id"];
          }
        ];
      };
      user_course_enrollment: {
        Row: {
          id: string;
          user_id: string;
          course_id: string;
          status: string;
          enrolled_at: string;
          completed_at?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          course_id: string;
          status?: string;
          enrolled_at?: string;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          course_id?: string;
          status?: string;
          enrolled_at?: string;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_course_enrollment_course_id_fkey";
            columns: ["course_id"];
            referencedRelation: "courses";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_course_enrollment_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      user_course_progress: {
        Row: {
          id: string;
          user_id: string;
          course_id: string;
          hours_spent: number;
          last_accessed_at: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          course_id: string;
          hours_spent?: number;
          last_accessed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          course_id?: string;
          hours_spent?: number;
          last_accessed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_course_progress_course_id_fkey";
            columns: ["course_id"];
            referencedRelation: "courses";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_course_progress_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      user_module_progress: {
        Row: {
          id: string;
          user_id: string;
          module_id: string;
          is_completed: boolean;
          completed_at?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          module_id: string;
          is_completed?: boolean;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          module_id?: string;
          is_completed?: boolean;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_module_progress_module_id_fkey";
            columns: ["module_id"];
            referencedRelation: "modules";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_module_progress_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      user_lesson_progress: {
        Row: {
          id: string;
          user_id: string;
          lesson_id: string;
          is_completed: boolean;
          progress_percent: number;
          time_spent: number;
          last_position?: string;
          completed_at?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          lesson_id: string;
          is_completed?: boolean;
          progress_percent?: number;
          time_spent?: number;
          last_position?: string;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          lesson_id?: string;
          is_completed?: boolean;
          progress_percent?: number;
          time_spent?: number;
          last_position?: string;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_lesson_progress_lesson_id_fkey";
            columns: ["lesson_id"];
            referencedRelation: "lessons";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_lesson_progress_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      user_roles: {
        Row: {
          id: string;
          user_id: string;
          role: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          role: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          role?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      completion_audit_log: {
        Row: {
          id: string;
          user_id: string;
          lesson_id: string;
          completed_at: string;
          success: boolean;
          error_message: string | null;
          client_info: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          lesson_id: string;
          completed_at?: string;
          success: boolean;
          error_message?: string | null;
          client_info?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          lesson_id?: string;
          completed_at?: string;
          success?: boolean;
          error_message?: string | null;
          client_info?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "completion_audit_log_lesson_id_fkey";
            columns: ["lesson_id"];
            referencedRelation: "lessons";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "completion_audit_log_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      // Add views here if needed
    };
    Functions: {
      complete_course: {
        Args: {
          p_user_id: string;
          p_course_id: string;
        };
        Returns: boolean;
      };
      assign_role: {
        Args: {
          p_user_id: string;
          p_role: string;
        };
        Returns: boolean;
      };
      has_role: {
        Args: {
          p_user_id: string;
          p_role: string;
        };
        Returns: boolean;
      };
      get_column_info: {
        Args: {
          table_name: string;
        };
        Returns: unknown;
      };
      count_completion_records: {
        Args: {
          user_id_param?: string;
        };
        Returns: unknown;
      };
      get_trigger_info: {
        Args: {
          table_name: string;
        };
        Returns: unknown;
      };
    };
    Enums: {
      // Add enums here if needed
    };
  };
} 