import { Database } from './database.types';

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T];

// Table types
export type Course = Tables<'courses'>;
export type Module = Tables<'modules'>;
export type Lesson = Tables<'lessons'>;
export type UserCourseEnrollment = Tables<'user_course_enrollment'> & {
  status: 'not_started' | 'in_progress' | 'completed';
  completed_at?: string;
};
export type UserCourseProgress = Tables<'user_course_progress'>;
export type UserModuleProgress = Tables<'user_module_progress'>;
export type UserLessonProgress = Tables<'user_lesson_progress'>;

// Function types
export type Functions = Database['public']['Functions'];
export type CompleteCourseFunction = Functions['complete_course'];

// RPC function types
export interface RPCFunctions {
  assign_role: (args: { p_user_id: string; p_role: string }) => Promise<boolean>;
  has_role: (args: { p_user_id: string; p_role: string }) => Promise<boolean>;
  complete_course: (args: { p_user_id: string; p_course_id: string }) => Promise<boolean>;
} 