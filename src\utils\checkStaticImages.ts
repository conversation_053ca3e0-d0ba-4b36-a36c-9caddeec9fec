/**
 * Utility to check if the static module images exist in the public folder
 */

/**
 * Checks if the module images (m1.jpg through m7.jpg) exist in the public/images folder
 * @returns True if all images exist, false otherwise
 */
export async function checkModuleImagesExist(): Promise<{exists: boolean; missing: string[]}> {
  const missingImages: string[] = [];
  
  // Check each module image
  for (let i = 1; i <= 7; i++) {
    const imagePath = `/images/m${i}.jpg`;
    
    try {
      // Try to fetch the image
      const response = await fetch(imagePath, { method: 'HEAD' });
      
      if (!response.ok) {
        console.warn(`Module image ${imagePath} does not exist`);
        missingImages.push(imagePath);
      }
    } catch (error) {
      console.error(`Error checking module image ${imagePath}:`, error);
      missingImages.push(imagePath);
    }
  }
  
  // Also check for the fallback image
  try {
    const fallbackPath = '/images/module-placeholder.jpg';
    const response = await fetch(fallbackPath, { method: 'HEAD' });
    
    if (!response.ok) {
      console.warn(`Fallback image ${fallbackPath} does not exist`);
      missingImages.push(fallbackPath);
    }
  } catch (error) {
    console.error('Error checking fallback image:', error);
    missingImages.push('/images/module-placeholder.jpg');
  }
  
  return {
    exists: missingImages.length === 0,
    missing: missingImages
  };
}

/**
 * Shows a toast notification if any module images are missing
 * @param toast The toast function to show notifications
 */
export async function checkAndNotifyMissingImages(toast: any): Promise<void> {
  const { exists, missing } = await checkModuleImagesExist();
  
  if (!exists) {
    toast({
      title: 'Missing module images',
      description: `Please add the following images to the public/images folder: ${missing.join(', ')}`,
      duration: 10000, // 10 seconds
      variant: 'destructive',
    });
    
    console.error('Missing module images. Add the following files to public/images/:', missing);
  }
} 