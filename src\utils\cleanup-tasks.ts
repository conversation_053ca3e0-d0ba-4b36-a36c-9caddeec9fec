import { supabase } from '@/lib/supabase';
import { cleanupOrphanedModuleImages } from '@/lib/image-utils';

/**
 * Utility class for running application cleanup tasks
 */
export class CleanupTasks {
  private static isRunning = false;
  private static runInterval: number | null = null;
  
  /**
   * Start the scheduled cleanup tasks
   * @param intervalMs Interval between cleanup runs in milliseconds (default: 24 hours)
   */
  public static startScheduledCleanup(intervalMs: number = 24 * 60 * 60 * 1000): void {
    if (this.isRunning) {
      console.log('Cleanup tasks are already running');
      return;
    }
    
    // Run cleanup immediately
    this.runAllCleanupTasks();
    
    // Schedule periodic cleanup
    this.runInterval = window.setInterval(() => {
      this.runAllCleanupTasks();
    }, intervalMs);
    
    this.isRunning = true;
    console.log(`Scheduled cleanup tasks started, running every ${intervalMs / (60 * 60 * 1000)} hours`);
  }
  
  /**
   * Stop the scheduled cleanup tasks
   */
  public static stopScheduledCleanup(): void {
    if (!this.isRunning || this.runInterval === null) {
      console.log('No cleanup tasks are running');
      return;
    }
    
    clearInterval(this.runInterval);
    this.runInterval = null;
    this.isRunning = false;
    console.log('Scheduled cleanup tasks stopped');
  }
  
  /**
   * Run all cleanup tasks immediately
   */
  public static async runAllCleanupTasks(): Promise<void> {
    console.log('Running all cleanup tasks...');
    
    try {
      // Cleanup orphaned module images
      await this.cleanupModuleImages();
      
      // Add more cleanup tasks as needed
      
      console.log('All cleanup tasks completed successfully');
    } catch (error) {
      console.error('Error running cleanup tasks:', error);
    }
  }
  
  /**
   * Cleanup orphaned module images
   * @returns Promise that resolves when complete
   */
  private static async cleanupModuleImages(): Promise<void> {
    console.log('Cleaning up orphaned module images...');
    
    try {
      // Check if user has admin rights before proceeding
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.log('User not authenticated, skipping module image cleanup');
        return;
      }
      
      // Get user role
      const { data: userRoles } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user.id)
        .single();
      
      // Only proceed if user is an admin
      if (!userRoles || userRoles.role !== 'admin') {
        console.log('User does not have admin rights, skipping module image cleanup');
        return;
      }
      
      // Run the cleanup
      await cleanupOrphanedModuleImages();
      console.log('Module image cleanup completed');
    } catch (error) {
      console.error('Error cleaning up module images:', error);
    }
  }
} 