/**
 * Utility functions for handling module images from the public folder
 */

/**
 * Gets the appropriate image URL for a module based on its number
 * @param moduleNumber The module number (1-based)
 * @returns The URL to the module image
 */
export function getModuleImageByNumber(moduleNumber: number): string {
  // Ensure a valid module number
  const safeModuleNumber = Math.max(1, Math.min(7, moduleNumber));
  
  // Return the path to the corresponding image
  return `/images/m${safeModuleNumber}.jpg`;
}

/**
 * Gets a fallback image if the module image doesn't exist
 * @returns URL to the fallback image
 */
export function getModuleFallbackImage(): string {
  return '/images/module-placeholder.jpg';
}

/**
 * Checks if a static image exists for the given module number
 * This performs a client-side check that can be used to validate
 * if images are accessible before attempting to load them
 * 
 * @param moduleNumber The module number to check
 * @returns Promise that resolves to boolean indicating if image exists
 */
export async function checkModuleImageExists(moduleNumber: number): Promise<boolean> {
  const imageUrl = getModuleImageByNumber(moduleNumber);
  
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error checking if module image exists:', error);
    return false;
  }
} 