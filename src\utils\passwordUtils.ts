/**
 * Password Utilities
 *
 * Utilities for password validation and strength checking.
 */

/**
 * Password strength levels
 */
export enum PasswordStrength {
  WEAK = 'weak',
  MEDIUM = 'medium',
  STRONG = 'strong'
}

/**
 * Password validation result
 */
export interface PasswordValidationResult {
  isValid: boolean;
  strength: PasswordStrength;
  message: string;
}

/**
 * Check if a password meets the minimum requirements
 * @param password The password to validate
 * @returns True if the password meets the minimum requirements, false otherwise
 */
export function isPasswordValid(password: string): boolean {
  return password.length >= 8;
}

/**
 * Get the strength of a password
 * @param password The password to check
 * @returns The password strength
 */
export function getPasswordStrength(password: string): PasswordStrength {
  if (!password || password.length < 8) {
    return PasswordStrength.WEAK;
  }

  let score = 0;

  // Length check
  if (password.length >= 12) {
    score += 2;
  } else if (password.length >= 8) {
    score += 1;
  }

  // Complexity checks
  if (/[A-Z]/.test(password)) score += 1; // Has uppercase
  if (/[a-z]/.test(password)) score += 1; // Has lowercase
  if (/[0-9]/.test(password)) score += 1; // Has number
  if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

  // Determine strength based on score
  if (score >= 4) {
    return PasswordStrength.STRONG;
  } else if (score >= 2) {
    return PasswordStrength.MEDIUM;
  } else {
    return PasswordStrength.WEAK;
  }
}

/**
 * Validate a password and return detailed information
 * @param password The password to validate
 * @returns A validation result object
 */
export function validatePassword(password: string): PasswordValidationResult {
  const strength = getPasswordStrength(password);

  if (!password || password.length < 8) {
    return {
      isValid: false,
      strength,
      message: 'Password must be at least 8 characters long'
    };
  }

  // Check for common patterns
  if (/^12345678/.test(password) || /^password/i.test(password) || /^qwerty/i.test(password)) {
    return {
      isValid: false,
      strength,
      message: 'Password is too common and easily guessable'
    };
  }

  // Provide feedback based on strength
  switch (strength) {
    case PasswordStrength.STRONG:
      return {
        isValid: true,
        strength,
        message: 'Strong password'
      };
    case PasswordStrength.MEDIUM:
      return {
        isValid: true,
        strength,
        message: 'Medium strength password. Consider adding numbers or special characters.'
      };
    case PasswordStrength.WEAK:
      return {
        isValid: false,
        strength,
        message: 'Weak password. Add uppercase letters, numbers, or special characters.'
      };
    default:
      return {
        isValid: false,
        strength,
        message: 'Invalid password'
      };
  }
}

/**
 * Get a color for the password strength
 * @param strength The password strength
 * @returns A CSS color value
 */
export function getPasswordStrengthColor(strength: PasswordStrength): string {
  switch (strength) {
    case PasswordStrength.STRONG:
      return '#E63946'; // Red for strong (matching our theme)
    case PasswordStrength.MEDIUM:
      return 'orange';
    case PasswordStrength.WEAK:
      return '#9D0208'; // Darker red for weak
    default:
      return 'gray';
  }
}
