# Supabase Configuration and Migrations

This directory contains the Supabase configuration and SQL migrations for the database.

## Directory Structure

- `config.toml`: Supabase project configuration
- `migrations/`: SQL migration files

## Running Migrations

We recommend using our standardized migration script instead of running migrations directly:

```bash
# Apply migrations to local database
npm run migrate

# Apply migrations to remote database
npm run migrate:remote

# Create a new migration
npm run migrate:create -- --name=your_migration_name
```

For more details, see the [Database Migrations Guide](../docs/DATABASE_MIGRATIONS.md).

## Migration Naming Convention

Migration files follow this naming convention:

```
YYYYMMDDHHMMSS_descriptive_name.sql
```

For example:
```
20250410001_fix_user_roles_rls.sql
```

The timestamp prefix ensures migrations are applied in the correct order.

Or you can manually run the SQL commands in the Supabase SQL editor.

## Troubleshooting

If you're having issues with RLS policies, you can try the following:

1. Make sure the user has the correct role assigned in the user_roles table
2. Check the RLS policies for the table you're trying to access
3. Use the admin API functions in `src/services/course/adminApi.ts` to bypass RLS

## Direct Database Access

Teachers can directly access the Supabase database using the Supabase dashboard. They can:

1. Go to the Supabase dashboard
2. Navigate to the "Table Editor" section
3. View and edit the data in the tables

This provides a direct way to manage course content without going through the application UI.
