-- Function to complete a course for a user
CREATE OR REPLACE FUNCTION public.complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS(
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  IF v_enrollment_exists THEN
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = NOW(),
      updated_at = NOW()
    WHERE 
      user_id = p_user_id AND 
      course_id = p_course_id;
  ELSE
    -- Create new enrollment
    INSERT INTO public.user_course_enrollment (
      user_id,
      course_id,
      status,
      enrolled_at,
      completed_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_course_id,
      'completed',
      NOW(),
      NOW(),
      NOW()
    );
  END IF;
  
  -- Also update user_course_progress
  INSERT INTO public.user_course_progress (
    user_id,
    course_id,
    completed_modules,
    updated_at
  ) VALUES (
    p_user_id,
    p_course_id,
    100,
    NOW()
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    completed_modules = 100,
    updated_at = NOW();
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course function: %', SQLERRM;
    RETURN FALSE;
END;
$$;
