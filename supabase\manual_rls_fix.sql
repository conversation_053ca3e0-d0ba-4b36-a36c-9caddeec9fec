-- Manual RLS and Storage Bucket Fix
-- Run this script in the Supabase SQL Editor to fix storage buckets and RLS policies
-- This combines bucket creation and RLS policy setup in one script

-- =====================================================
-- STEP 1: Create Storage Buckets
-- =====================================================

-- Insert buckets directly into storage.buckets table
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('course-images', 'course-images', true, 52428800, NULL),
  ('avatars', 'avatars', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('app-uploads', 'app-uploads', true, 52428800, NULL),
  ('uploads', 'uploads', true, 52428800, NULL),
  ('default-bucket', 'default-bucket', true, 52428800, NULL)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- STEP 2: Enable RLS and Clean Up Existing Policies
-- =====================================================

-- Enable RLS for storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Allow public access to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to app-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to default-bucket" ON storage.objects;

DROP POLICY IF EXISTS "Allow authenticated uploads to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to app-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to default-bucket" ON storage.objects;

DROP POLICY IF EXISTS "Allow authenticated users to update their uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete their uploads" ON storage.objects;
DROP POLICY IF EXISTS "Service role bypass for storage" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous uploads to course-images" ON storage.objects;

-- =====================================================
-- STEP 3: Create Public Read Access Policies
-- =====================================================

-- Create policies for public read access to all buckets
CREATE POLICY "Allow public access to course-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'course-images');

CREATE POLICY "Allow public access to avatars"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

CREATE POLICY "Allow public access to app-uploads"
ON storage.objects FOR SELECT
USING (bucket_id = 'app-uploads');

CREATE POLICY "Allow public access to uploads"
ON storage.objects FOR SELECT
USING (bucket_id = 'uploads');

CREATE POLICY "Allow public access to default-bucket"
ON storage.objects FOR SELECT
USING (bucket_id = 'default-bucket');

-- =====================================================
-- STEP 4: Create Authenticated User Upload Policies
-- =====================================================

-- Create policies for authenticated users to upload to all buckets
CREATE POLICY "Allow authenticated uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to avatars"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'avatars' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to app-uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'app-uploads' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'uploads' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to default-bucket"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'default-bucket' AND
  auth.role() = 'authenticated'
);

-- =====================================================
-- STEP 5: Create Update and Delete Policies
-- =====================================================

-- Create policies for authenticated users to update their own uploads
CREATE POLICY "Allow authenticated users to update their uploads"
ON storage.objects FOR UPDATE
USING (auth.uid() = owner)
WITH CHECK (auth.uid() = owner);

-- Create policies for authenticated users to delete their own uploads
CREATE POLICY "Allow authenticated users to delete their uploads"
ON storage.objects FOR DELETE
USING (auth.uid() = owner);

-- =====================================================
-- STEP 6: Create Service Role Bypass Policy
-- =====================================================

-- Create a policy for service role to bypass RLS
CREATE POLICY "Service role bypass for storage"
ON storage.objects
USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================================================
-- STEP 7: Create Anonymous Upload Policy (Optional)
-- =====================================================

-- Create a policy for anon users to upload to course-images (if needed)
CREATE POLICY "Allow anonymous uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'anon'
);

-- =====================================================
-- STEP 8: Add Comments for Documentation
-- =====================================================

COMMENT ON TABLE storage.buckets IS 'Storage buckets for the i-can-iv e-learning application';
COMMENT ON TABLE storage.objects IS 'Storage objects with proper RLS policies for authenticated and anonymous users';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Run these queries to verify the setup worked correctly:

-- Check if buckets were created
-- SELECT name, public, file_size_limit FROM storage.buckets ORDER BY name;

-- Check if RLS is enabled
-- SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE tablename = 'objects' AND schemaname = 'storage';

-- Check policies
-- SELECT policyname, tablename FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage' ORDER BY policyname;

-- Test bucket access (should return bucket info)
-- SELECT * FROM storage.buckets WHERE name IN ('course-images', 'avatars', 'app-uploads', 'uploads', 'default-bucket');
