-- Migration: create_rls_helper_functions
-- Created at: 2024-06-01T00:00:00.000Z
-- Description: Adds helper functions for RLS management and health checks

-- Function to get tables with RLS information
CREATE OR REPLACE FUNCTION public.get_tables_with_rls()
RETURNS TABLE (
  table_schema text,
  table_name text,
  rls_enabled boolean
) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT 
    n.nspname AS table_schema,
    c.relname AS table_name,
    c.relrowsecurity AS rls_enabled
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE c.relkind = 'r'  -- Only tables
    AND n.nspname = 'public'  -- Only public schema
  ORDER BY table_schema, table_name;
$$;

-- Grant execution permissions to service_role
GRANT EXECUTE ON FUNCTION public.get_tables_with_rls() TO service_role;

-- Add a health check function to verify database connection
CREATE OR REPLACE FUNCTION public.health_check()
R<PERSON><PERSON>NS jsonb
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT jsonb_build_object(
    'status', 'ok',
    'timestamp', now(),
    'version', current_setting('server_version')
  );
$$;

-- Grant execution permissions to anonymous users for basic health checks
GRANT EXECUTE ON FUNCTION public.health_check() TO anon; 