-- Add image_url column to courses table
ALTER TABLE public.courses 
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Update RLS policies to allow access to the new column
DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
CREATE POLICY "Anyone can view courses" 
ON public.courses FOR SELECT 
USING (true);

-- Allow teachers to update course images
DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
CREATE POLICY "Teachers can update courses" 
ON public.courses FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'teacher'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'teacher'
  )
);

-- Allow teachers to insert courses with images
DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
CREATE POLICY "Teachers can insert courses" 
ON public.courses FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'teacher'
  )
);

-- Set up replica identity for realtime
ALTER TABLE public.courses REPLICA IDENTITY FULL;
