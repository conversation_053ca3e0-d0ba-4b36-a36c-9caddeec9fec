-- Create functions to check and add columns

-- Function to check if a column exists
CREATE OR REPLACE FUNCTION public.check_column_exists(
  table_name text,
  column_name text
) RETURNS boolean
LANGUAGE plpgsql
AS $$
DECLARE
  column_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = check_column_exists.table_name
    AND column_name = check_column_exists.column_name
  ) INTO column_exists;
  
  RETURN column_exists;
END;
$$;

-- Function to add the image_url column to courses table
CREATE OR REPLACE FUNCTION public.add_image_url_column()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the column exists
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'courses'
    AND column_name = 'image_url'
  ) THEN
    -- Add the column if it doesn't exist
    EXECUTE 'ALTER TABLE public.courses ADD COLUMN image_url TEXT';
    RAISE NOTICE 'Added image_url column to courses table';
  ELSE
    -- Log that the column already exists
    RAISE NOTICE 'image_url column already exists in courses table';
  END IF;
END;
$$;
