-- Migration: Fix Certificate Generation Issues
-- Created: 2025-01-02
-- Description: Comprehensive fix for certificate generation and display issues

-- =============================================
-- ENSURE PROPER TABLE STRUCTURE
-- =============================================

-- Make sure user_course_enrollment table has all required columns
ALTER TABLE IF EXISTS public.user_course_enrollment
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update existing completed enrollments that don't have a completed_at date
UPDATE public.user_course_enrollment
SET completed_at = COALESCE(updated_at, enrolled_at, NOW())
WHERE status = 'completed' AND completed_at IS NULL;

-- Ensure courses table has image_url column for certificate display
ALTER TABLE IF EXISTS public.courses
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- =============================================
-- IMPROVED COMPLETE COURSE FUNCTION
-- =============================================

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS public.complete_course(UUID, UUID);

-- Create improved complete_course function with better error handling
CREATE OR REPLACE FUNCTION public.complete_course(
  p_user_id UUID,
  p_course_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
  v_now TIMESTAMP WITH TIME ZONE;
BEGIN
  v_now := NOW();

  -- Check if enrollment exists
  SELECT EXISTS(
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;

  IF v_enrollment_exists THEN
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET
      status = 'completed',
      completed_at = COALESCE(completed_at, v_now),
      updated_at = v_now
    WHERE
      user_id = p_user_id AND
      course_id = p_course_id;
  ELSE
    -- Create new enrollment
    INSERT INTO public.user_course_enrollment (
      user_id,
      course_id,
      status,
      enrolled_at,
      completed_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_course_id,
      'completed',
      v_now,
      v_now,
      v_now
    );
  END IF;

  -- Ensure user_course_progress entry exists
  INSERT INTO public.user_course_progress (
    user_id,
    course_id,
    hours_spent,
    last_accessed_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_course_id,
    0,
    v_now,
    v_now
  )
  ON CONFLICT (user_id, course_id)
  DO UPDATE SET
    last_accessed_at = v_now,
    updated_at = v_now;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- =============================================
-- CERTIFICATE HELPER FUNCTIONS
-- =============================================

-- Function to get user certificates
CREATE OR REPLACE FUNCTION public.get_user_certificates(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  course_id UUID,
  user_id UUID,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  status TEXT,
  course_title TEXT,
  course_description TEXT,
  course_image_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT
    uce.id,
    uce.course_id,
    uce.user_id,
    uce.completed_at,
    uce.created_at,
    uce.updated_at,
    uce.status,
    c.title as course_title,
    c.description as course_description,
    c.image_url as course_image_url
  FROM public.user_course_enrollment uce
  JOIN public.courses c ON c.id = uce.course_id
  WHERE uce.user_id = p_user_id
    AND uce.status = 'completed'
  ORDER BY uce.completed_at DESC;
END;
$$;

-- Function to check if user has completed a course
CREATE OR REPLACE FUNCTION public.is_course_completed(
  p_user_id UUID,
  p_course_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_course_enrollment
    WHERE user_id = p_user_id
      AND course_id = p_course_id
      AND status = 'completed'
      AND completed_at IS NOT NULL
  );
END;
$$;

-- =============================================
-- FIX EXISTING DATA
-- =============================================

-- Fix any enrollments that have status 'completed' but no completed_at
UPDATE public.user_course_enrollment
SET completed_at = COALESCE(updated_at, enrolled_at, NOW())
WHERE status = 'completed' AND completed_at IS NULL;

-- Ensure all completed courses have progress entries
INSERT INTO public.user_course_progress (
  user_id,
  course_id,
  hours_spent,
  last_accessed_at,
  updated_at
)
SELECT
  uce.user_id,
  uce.course_id,
  0,
  NOW(),
  NOW()
FROM public.user_course_enrollment uce
WHERE uce.status = 'completed'
  AND NOT EXISTS (
    SELECT 1
    FROM public.user_course_progress ucp
    WHERE ucp.user_id = uce.user_id
      AND ucp.course_id = uce.course_id
  );

-- =============================================
-- IMPROVED RLS POLICIES
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can insert their own enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can update their own enrollments" ON public.user_course_enrollment;

-- Create improved RLS policies for user_course_enrollment
CREATE POLICY "Users can view their own enrollments" ON public.user_course_enrollment
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own enrollments" ON public.user_course_enrollment
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own enrollments" ON public.user_course_enrollment
  FOR UPDATE USING (auth.uid() = user_id);

-- Allow service role to manage all enrollments
CREATE POLICY "Service role can manage all enrollments" ON public.user_course_enrollment
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions for certificate functions
GRANT EXECUTE ON FUNCTION public.complete_course(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_certificates(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_course_completed(UUID, UUID) TO authenticated;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for better certificate query performance
CREATE INDEX IF NOT EXISTS idx_user_course_enrollment_user_status
ON public.user_course_enrollment(user_id, status);

CREATE INDEX IF NOT EXISTS idx_user_course_enrollment_completed
ON public.user_course_enrollment(user_id, completed_at)
WHERE status = 'completed';

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION public.complete_course(UUID, UUID) IS 'Complete a course for a user and ensure proper certificate data';
COMMENT ON FUNCTION public.get_user_certificates(UUID) IS 'Get all certificates for a user with course details';
COMMENT ON FUNCTION public.is_course_completed(UUID, UUID) IS 'Check if a user has completed a specific course';

COMMENT ON TABLE public.user_course_enrollment IS 'Tracks user enrollment and completion status for courses';
COMMENT ON COLUMN public.user_course_enrollment.completed_at IS 'Timestamp when the course was completed (required for certificates)';
COMMENT ON COLUMN public.user_course_enrollment.status IS 'Enrollment status: not_started, in_progress, completed';
