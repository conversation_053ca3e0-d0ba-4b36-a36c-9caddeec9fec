-- Migration: Enhanced Lesson Content Validation and Security
-- Created at: 2025-01-02
-- Description: Adds content validation, size limits, and security constraints to lesson content

-- Add content validation constraints to lessons table
ALTER TABLE public.lessons 
ADD CONSTRAINT lesson_content_size_limit 
CHECK (length(content) <= 1048576); -- 1MB limit

-- Add content type validation
ALTER TABLE public.lessons 
ADD COLUMN content_type TEXT DEFAULT 'markdown' 
CHECK (content_type IN ('markdown', 'html', 'json'));

-- Add content encoding validation
ALTER TABLE public.lessons 
ADD COLUMN content_encoding TEXT DEFAULT 'utf8' 
CHECK (content_encoding IN ('utf8', 'base64'));

-- Add content validation function
CREATE OR REPLACE FUNCTION validate_lesson_content()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate content is not empty for published lessons
  IF NEW.is_published = true AND (NEW.content IS NULL OR trim(NEW.content) = '') THEN
    RAISE EXCEPTION 'Published lessons must have content';
  END IF;
  
  -- Validate content size
  IF NEW.content IS NOT NULL AND length(NEW.content) > 1048576 THEN
    RAISE EXCEPTION 'Content size exceeds 1MB limit';
  END IF;
  
  -- Validate UTF-8 encoding
  IF NEW.content IS NOT NULL AND NOT convert_from(convert_to(NEW.content, 'UTF8'), 'UTF8') = NEW.content THEN
    RAISE EXCEPTION 'Content must be valid UTF-8';
  END IF;
  
  -- Set content type based on content structure
  IF NEW.content IS NOT NULL THEN
    IF NEW.content ~ '^\s*<[^>]+>' THEN
      NEW.content_type = 'html';
    ELSIF NEW.content ~ '^\s*[\{\[]' THEN
      NEW.content_type = 'json';
    ELSE
      NEW.content_type = 'markdown';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for content validation
DROP TRIGGER IF EXISTS validate_lesson_content_trigger ON public.lessons;
CREATE TRIGGER validate_lesson_content_trigger
  BEFORE INSERT OR UPDATE ON public.lessons
  FOR EACH ROW EXECUTE FUNCTION validate_lesson_content();

-- Add content versioning table for history tracking
CREATE TABLE IF NOT EXISTS public.lesson_content_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  content_type TEXT NOT NULL DEFAULT 'markdown',
  version_number INTEGER NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  change_summary TEXT,
  UNIQUE(lesson_id, version_number)
);

-- Enable RLS on content versions
ALTER TABLE public.lesson_content_versions ENABLE ROW LEVEL SECURITY;

-- Content versions policies
CREATE POLICY "Teachers can view content versions"
ON public.lesson_content_versions FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can insert content versions"
ON public.lesson_content_versions FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Function to create content version on lesson update
CREATE OR REPLACE FUNCTION create_lesson_content_version()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create version if content actually changed
  IF OLD.content IS DISTINCT FROM NEW.content THEN
    INSERT INTO public.lesson_content_versions (
      lesson_id,
      content,
      content_type,
      version_number,
      created_by,
      change_summary
    )
    SELECT 
      NEW.id,
      OLD.content,
      COALESCE(OLD.content_type, 'markdown'),
      COALESCE(
        (SELECT MAX(version_number) + 1 FROM public.lesson_content_versions WHERE lesson_id = NEW.id),
        1
      ),
      auth.uid(),
      'Content updated';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for content versioning
DROP TRIGGER IF EXISTS create_lesson_content_version_trigger ON public.lessons;
CREATE TRIGGER create_lesson_content_version_trigger
  AFTER UPDATE ON public.lessons
  FOR EACH ROW EXECUTE FUNCTION create_lesson_content_version();

-- Grant permissions
GRANT SELECT, INSERT ON public.lesson_content_versions TO authenticated;
GRANT EXECUTE ON FUNCTION validate_lesson_content TO authenticated;
GRANT EXECUTE ON FUNCTION create_lesson_content_version TO authenticated;
