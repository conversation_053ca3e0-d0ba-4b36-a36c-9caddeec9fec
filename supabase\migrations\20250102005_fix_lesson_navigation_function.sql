-- Fix lesson navigation function to use lesson_number instead of non-existent order_index
-- This addresses the random navigation issue by ensuring proper lesson ordering

-- =============================================
-- FIX GET_LESSON_NAVIGATION FUNCTION
-- =============================================

-- Drop the old function that uses order_index (which doesn't exist)
DROP FUNCTION IF EXISTS public.get_lesson_navigation(UUID);

-- Create the corrected function that uses lesson_number for proper ordering
CREATE OR REPLACE FUNCTION public.get_lesson_navigation(
  p_current_lesson_id UUID,
  p_module_id UUID DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSON;
  current_module_id UUID;
  current_lesson_number INTEGER;
BEGIN
  -- Get current lesson details
  SELECT module_id, lesson_number 
  INTO current_module_id, current_lesson_number
  FROM public.lessons 
  WHERE id = p_current_lesson_id;
  
  -- Use provided module_id or the one from current lesson
  IF p_module_id IS NOT NULL THEN
    current_module_id := p_module_id;
  END IF;
  
  -- Build navigation result
  SELECT json_build_object(
    'previous_slug', (
      SELECT slug
      FROM public.lessons
      WHERE module_id = current_module_id
        AND lesson_number < current_lesson_number
      ORDER BY lesson_number DESC
      LIMIT 1
    ),
    'next_slug', (
      SELECT slug
      FROM public.lessons
      WHERE module_id = current_module_id
        AND lesson_number > current_lesson_number
      ORDER BY lesson_number ASC
      LIMIT 1
    ),
    'current_lesson_number', current_lesson_number,
    'module_id', current_module_id
  ) INTO result;
  
  RETURN result;
END;
$$;

-- =============================================
-- CREATE ENHANCED NAVIGATION FUNCTION
-- =============================================

-- Create a more comprehensive navigation function that handles cross-module navigation
CREATE OR REPLACE FUNCTION public.get_lesson_navigation_enhanced(
  p_current_lesson_slug TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSON;
  current_lesson RECORD;
  current_module RECORD;
  prev_lesson_slug TEXT;
  next_lesson_slug TEXT;
  is_last_lesson BOOLEAN := FALSE;
BEGIN
  -- Get current lesson details
  SELECT id, module_id, lesson_number, slug
  INTO current_lesson
  FROM public.lessons 
  WHERE slug = p_current_lesson_slug;
  
  IF NOT FOUND THEN
    RETURN json_build_object('error', 'Lesson not found');
  END IF;
  
  -- Get current module details
  SELECT course_id, module_number, title
  INTO current_module
  FROM public.modules
  WHERE id = current_lesson.module_id;
  
  -- Find previous lesson in same module
  SELECT slug INTO prev_lesson_slug
  FROM public.lessons
  WHERE module_id = current_lesson.module_id
    AND lesson_number < current_lesson.lesson_number
  ORDER BY lesson_number DESC
  LIMIT 1;
  
  -- Find next lesson in same module
  SELECT slug INTO next_lesson_slug
  FROM public.lessons
  WHERE module_id = current_lesson.module_id
    AND lesson_number > current_lesson.lesson_number
  ORDER BY lesson_number ASC
  LIMIT 1;
  
  -- If no next lesson in current module, check next module
  IF next_lesson_slug IS NULL THEN
    -- Look for next module
    DECLARE
      next_module_id UUID;
    BEGIN
      SELECT id INTO next_module_id
      FROM public.modules
      WHERE course_id = current_module.course_id
        AND module_number > current_module.module_number
      ORDER BY module_number ASC
      LIMIT 1;
      
      IF next_module_id IS NOT NULL THEN
        -- Get first lesson of next module
        SELECT slug INTO next_lesson_slug
        FROM public.lessons
        WHERE module_id = next_module_id
        ORDER BY lesson_number ASC
        LIMIT 1;
      ELSE
        -- This is the last lesson in the course
        is_last_lesson := TRUE;
      END IF;
    END;
  END IF;
  
  -- Build comprehensive result
  SELECT json_build_object(
    'previous_slug', prev_lesson_slug,
    'next_slug', next_lesson_slug,
    'is_last_lesson', is_last_lesson,
    'current_lesson', json_build_object(
      'id', current_lesson.id,
      'slug', current_lesson.slug,
      'lesson_number', current_lesson.lesson_number,
      'module_id', current_lesson.module_id
    ),
    'current_module', json_build_object(
      'course_id', current_module.course_id,
      'module_number', current_module.module_number,
      'title', current_module.title
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.get_lesson_navigation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_lesson_navigation_enhanced(TEXT) TO authenticated;

-- =============================================
-- ADD COMMENTS
-- =============================================

COMMENT ON FUNCTION public.get_lesson_navigation(UUID, UUID) IS 'Returns navigation information for a lesson using lesson_number ordering';
COMMENT ON FUNCTION public.get_lesson_navigation_enhanced(TEXT) IS 'Enhanced navigation function with cross-module support and detailed information';

-- =============================================
-- VALIDATION
-- =============================================

-- Test the function to ensure it works correctly
DO $$
DECLARE
  test_result JSON;
  test_lesson_slug TEXT;
BEGIN
  -- Get a test lesson slug
  SELECT slug INTO test_lesson_slug
  FROM public.lessons
  LIMIT 1;
  
  IF test_lesson_slug IS NOT NULL THEN
    -- Test the enhanced navigation function
    SELECT public.get_lesson_navigation_enhanced(test_lesson_slug) INTO test_result;
    
    IF test_result IS NOT NULL THEN
      RAISE NOTICE 'Navigation function test successful for lesson: %', test_lesson_slug;
    ELSE
      RAISE WARNING 'Navigation function test returned NULL for lesson: %', test_lesson_slug;
    END IF;
  ELSE
    RAISE NOTICE 'No lessons found for testing navigation function';
  END IF;
END $$;
