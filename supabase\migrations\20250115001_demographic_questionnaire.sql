-- Migration: Demographic Questionnaire System
-- Created at: 2025-01-15
-- Description: Creates tables for storing demographic questionnaire data

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- DEMOGRAPHIC QUESTIONNAIRES TABLE
-- =============================================

-- Create demographic_questionnaires table to store questionnaire structure
CREATE TABLE IF NOT EXISTS public.demographic_questionnaires (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  questions JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- USER DEMOGRAPHIC RESPONSES TABLE
-- =============================================

-- Create user_demographic_responses table to store user responses
CREATE TABLE IF NOT EXISTS public.user_demographic_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  questionnaire_id UUID REFERENCES public.demographic_questionnaires(id) ON DELETE CASCADE,
  responses JSONB NOT NULL DEFAULT '{}',
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, questionnaire_id)
);

-- =============================================
-- INDEXES
-- =============================================

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_demographic_questionnaires_active ON public.demographic_questionnaires(is_active);
CREATE INDEX IF NOT EXISTS idx_user_demographic_responses_user_id ON public.user_demographic_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_demographic_responses_questionnaire_id ON public.user_demographic_responses(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_user_demographic_responses_completed_at ON public.user_demographic_responses(completed_at);

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on tables
ALTER TABLE public.demographic_questionnaires ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_demographic_responses ENABLE ROW LEVEL SECURITY;

-- Policies for demographic_questionnaires
CREATE POLICY "Anyone can view active questionnaires"
  ON public.demographic_questionnaires
  FOR SELECT
  USING (is_active = true);

CREATE POLICY "Teachers can manage questionnaires"
  ON public.demographic_questionnaires
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  );

-- Policies for user_demographic_responses
CREATE POLICY "Users can view their own responses"
  ON public.user_demographic_responses
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own responses"
  ON public.user_demographic_responses
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own responses"
  ON public.user_demographic_responses
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all responses"
  ON public.user_demographic_responses
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  );

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to get demographic analytics
CREATE OR REPLACE FUNCTION public.get_demographic_analytics()
RETURNS TABLE (
  total_responses BIGINT,
  response_breakdown JSONB,
  completion_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::BIGINT as total_responses,
    jsonb_build_object(
      'by_country', (
        SELECT jsonb_object_agg(
          COALESCE(responses->>'country', 'Unknown'),
          count
        )
        FROM (
          SELECT 
            responses->>'country' as country,
            COUNT(*) as count
          FROM public.user_demographic_responses
          GROUP BY responses->>'country'
        ) country_stats
      ),
      'by_gender', (
        SELECT jsonb_object_agg(
          COALESCE(responses->>'gender', 'Unknown'),
          count
        )
        FROM (
          SELECT 
            responses->>'gender' as gender,
            COUNT(*) as count
          FROM public.user_demographic_responses
          GROUP BY responses->>'gender'
        ) gender_stats
      ),
      'by_role', (
        SELECT jsonb_object_agg(
          COALESCE(responses->>'role_type', 'Unknown'),
          count
        )
        FROM (
          SELECT 
            responses->>'role_type' as role_type,
            COUNT(*) as count
          FROM public.user_demographic_responses
          GROUP BY responses->>'role_type'
        ) role_stats
      )
    ) as response_breakdown,
    ROUND(
      (COUNT(*)::NUMERIC / NULLIF((SELECT COUNT(*) FROM auth.users), 0)) * 100,
      2
    ) as completion_rate
  FROM public.user_demographic_responses;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_demographic_analytics() TO authenticated;

-- =============================================
-- INSERT DEFAULT QUESTIONNAIRE
-- =============================================

-- Insert the default demographic questionnaire
INSERT INTO public.demographic_questionnaires (title, description, questions, is_active)
VALUES (
  'Student Demographic Questionnaire',
  'Please complete this questionnaire to help us better understand our student demographics.',
  '[
    {
      "id": "consent",
      "question": "Would you like to proceed with the questionnaire?",
      "type": "single_choice",
      "required": true,
      "options": ["Agree", "Disagree"]
    },
    {
      "id": "country",
      "question": "What country are you from?",
      "type": "text",
      "required": true
    },
    {
      "id": "gender",
      "question": "Gender",
      "type": "single_choice",
      "required": true,
      "options": ["Male", "Female"]
    },
    {
      "id": "age",
      "question": "Please state your age",
      "type": "number",
      "required": true
    },
    {
      "id": "formal_training",
      "question": "Have you received any formal training on IV cannulation and contrast administration?",
      "type": "single_choice",
      "required": true,
      "options": ["Yes", "No"]
    },
    {
      "id": "role_type",
      "question": "Are you a medical imaging student or a practitioner?",
      "type": "single_choice",
      "required": true,
      "options": ["Student", "Practitioner", "Other"]
    },
    {
      "id": "student_level",
      "question": "Are you an undergraduate or a postgraduate student?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Student"},
      "options": ["Undergraduate", "Postgraduate"]
    },
    {
      "id": "university",
      "question": "Which university do you attend?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Student"},
      "options": [
        "University of Ghana (UG)",
        "Kwame Nkrumah University of Science and Technology (KNUST)",
        "University of Cape Coast (UCC)",
        "University of Development Studies (UDS)",
        "University of Health and Allied Sciences (UHAS)",
        "Accra Technical University (ATU)",
        "College of Health and Well-Being, Kintampo (KRHTS)",
        "Other"
      ]
    },
    {
      "id": "undergraduate_program",
      "question": "What program are you reading?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "student_level", "value": "Undergraduate"},
      "options": [
        "BSc Diagnostic Radiography",
        "BSc Diagnostic Sonography",
        "BSc Radiation Therapy",
        "BSc Nuclear Medicine Technology",
        "Other"
      ]
    },
    {
      "id": "undergraduate_year",
      "question": "What year are you in?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "student_level", "value": "Undergraduate"},
      "options": ["1st Year", "2nd Year", "3rd Year", "4th Year", "5th Year", "6th Year"]
    },
    {
      "id": "postgraduate_program",
      "question": "What program are you reading?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "student_level", "value": "Postgraduate"},
      "options": ["MSc", "MPhil", "PhD", "Other"]
    },
    {
      "id": "practitioner_work",
      "question": "What do you do for work?",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Practitioner"},
      "options": [
        "Diagnostic Radiographer",
        "Diagnostic Sonography",
        "Radiation Therapy",
        "Nuclear Medicine Technology",
        "Other"
      ]
    },
    {
      "id": "workplace",
      "question": "Place of work",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Practitioner"},
      "options": [
        "Tertiary Hospital",
        "Regional Hospital",
        "District Hospital",
        "Diagnostic Center",
        "Other"
      ]
    },
    {
      "id": "location",
      "question": "Location",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Practitioner"},
      "options": [
        "Ashanti Region",
        "Ahafo Region",
        "Bono Region",
        "Bono East Region",
        "Central Region",
        "Eastern Region",
        "Greater Accra Region",
        "North East Region",
        "Northern Region",
        "Oti Region",
        "Savannah Region",
        "Upper East Region",
        "Upper West Region",
        "Volta Region",
        "Western Region",
        "Western North Region"
      ]
    },
    {
      "id": "experience_years",
      "question": "Years of Experience",
      "type": "single_choice",
      "required": false,
      "conditional": {"field": "role_type", "value": "Practitioner"},
      "options": ["1 Year", "2 Years", "3 Years", "4 Years", "5 Years", "Other"]
    }
  ]'::jsonb,
  true
)
ON CONFLICT DO NOTHING;
