
-- Create a function to log role changes
CREATE OR REPLACE FUNCTION public.log_role_change(
  user_id_param UUID,
  changed_by_param UUID,
  old_role_param TEXT,
  new_role_param TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.user_role_changes (
    user_id, 
    changed_by, 
    old_role, 
    new_role
  ) VALUES (
    user_id_param, 
    changed_by_param, 
    old_role_param, 
    new_role_param
  );
END;
$$;

-- Grant permissions to use this function
GRANT EXECUTE ON FUNCTION public.log_role_change TO authenticated;
