-- Fix RLS policies for all tables

-- =====================
-- COURSES TABLE
-- =====================

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can delete courses" ON public.courses;
DROP POLICY IF EXISTS "Service role bypass" ON public.courses;

-- Create new policies
CREATE POLICY "Anyone can view courses" 
ON public.courses FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert courses" 
ON public.courses FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update courses" 
ON public.courses FOR UPDATE 
USING (true);

CREATE POLICY "Anyone can delete courses" 
ON public.courses FOR DELETE 
USING (true);

-- =====================
-- MODULES TABLE
-- =====================

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can insert modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can update modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can delete modules" ON public.modules;
DROP POLICY IF EXISTS "Service role bypass" ON public.modules;

-- Create new policies
CREATE POLICY "Anyone can view modules" 
ON public.modules FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert modules" 
ON public.modules FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update modules" 
ON public.modules FOR UPDATE 
USING (true);

CREATE POLICY "Anyone can delete modules" 
ON public.modules FOR DELETE 
USING (true);

-- =====================
-- LESSONS TABLE
-- =====================

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can insert lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can update lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can delete lessons" ON public.lessons;
DROP POLICY IF EXISTS "Service role bypass" ON public.lessons;

-- Create new policies
CREATE POLICY "Anyone can view lessons" 
ON public.lessons FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert lessons" 
ON public.lessons FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update lessons" 
ON public.lessons FOR UPDATE 
USING (true);

CREATE POLICY "Anyone can delete lessons" 
ON public.lessons FOR DELETE 
USING (true);

-- =====================
-- USER_ROLES TABLE
-- =====================

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can insert user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can update user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can delete user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Service role bypass" ON public.user_roles;

-- Create new policies
CREATE POLICY "Anyone can view user_roles" 
ON public.user_roles FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert user_roles" 
ON public.user_roles FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update user_roles" 
ON public.user_roles FOR UPDATE 
USING (true);

CREATE POLICY "Anyone can delete user_roles" 
ON public.user_roles FOR DELETE 
USING (true);
