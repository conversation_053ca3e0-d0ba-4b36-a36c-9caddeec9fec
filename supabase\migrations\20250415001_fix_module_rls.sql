-- Fix RLS policies for modules table to allow users to update module completion status

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can update module completion" ON public.modules;

-- Create new policy to allow users to update module completion
CREATE POLICY "Users can update module completion" 
ON public.modules FOR UPDATE 
USING (true)
WITH CHECK (true);

-- Make sure the is_completed column exists
ALTER TABLE IF EXISTS public.modules 
ADD COLUMN IF NOT EXISTS is_completed BOOLEAN DEFAULT false;

-- Add a trigger to update course completion when module completion changes
CREATE OR REPLACE FUNCTION update_course_completion()
RETURNS TRIGGER AS $$
DECLARE
  course_id_val UUID;
  total_modules INTEGER;
  completed_modules INTEGER;
BEGIN
  -- Get the course ID for this module
  SELECT course_id INTO course_id_val FROM public.modules WHERE id = NEW.id;
  
  -- Count total and completed modules for this course
  SELECT COUNT(*) INTO total_modules FROM public.modules WHERE course_id = course_id_val;
  SELECT COUNT(*) INTO completed_modules FROM public.modules WHERE course_id = course_id_val AND is_completed = TRUE;
  
  -- Update the course's completed modules count
  UPDATE public.courses
  SET 
    completed_modules = completed_modules,
    total_modules = total_modules,
    updated_at = NOW()
  WHERE id = course_id_val;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update course completion when module completion changes
DROP TRIGGER IF EXISTS on_module_completion ON public.modules;
CREATE TRIGGER on_module_completion
  AFTER UPDATE OF is_completed ON public.modules
  FOR EACH ROW
  WHEN (OLD.is_completed IS DISTINCT FROM NEW.is_completed)
  EXECUTE FUNCTION update_course_completion();
