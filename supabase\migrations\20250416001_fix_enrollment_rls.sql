-- Fix RLS policies for user_course_enrollment table to allow users to mark courses as completed

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can insert their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can update their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Teachers can view all enrollments" ON public.user_course_enrollment;

-- Create new policies with more permissive rules
CREATE POLICY "Anyone can view course enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert course enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update course enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (true);

-- Make sure the completed_at column exists
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
