-- Create a function to complete a course for a user
-- This function uses SECURITY DEFINER to bypass RLS policies
CREATE OR REPLACE FUNCTION public.complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS(
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  IF v_enrollment_exists THEN
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = NOW(),
      updated_at = NOW()
    WHERE 
      user_id = p_user_id AND 
      course_id = p_course_id;
  ELSE
    -- Create new enrollment
    INSERT INTO public.user_course_enrollment (
      user_id,
      course_id,
      status,
      enrolled_at,
      completed_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_course_id,
      'completed',
      NOW(),
      NOW(),
      NOW()
    );
  END IF;
  
  -- Also update user_course_progress
  INSERT INTO public.user_course_progress (
    user_id,
    course_id,
    completed_modules,
    updated_at
  ) VALUES (
    p_user_id,
    p_course_id,
    100,
    NOW()
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    completed_modules = 100,
    updated_at = NOW();
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course function: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.complete_course(UUID, UUID) TO authenticated;

-- Create a policy to allow all users to execute the function
DROP POLICY IF EXISTS "Anyone can execute complete_course function" ON public.user_course_enrollment;
CREATE POLICY "Anyone can execute complete_course function" 
ON public.user_course_enrollment 
FOR ALL 
TO authenticated
USING (true);

-- Make sure the user_course_enrollment table has the necessary columns
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
