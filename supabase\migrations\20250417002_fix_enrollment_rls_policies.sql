-- Fix RLS policies for user_course_enrollment table
-- This migration completely removes and recreates the RLS policies for the user_course_enrollment table

-- Disable <PERSON><PERSON> temporarily to ensure we can make changes
ALTER TABLE public.user_course_enrollment DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can insert their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can update their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Teachers can view all enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can view course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can insert course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can update course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can execute complete_course function" ON public.user_course_enrollment;

-- Create new, more permissive policies
CREATE POLICY "Anyone can view enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can update enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (true);

CREATE POLICY "Anyone can delete enrollments" 
ON public.user_course_enrollment FOR DELETE 
USING (true);

-- Re-enable RLS
ALTER TABLE public.user_course_enrollment ENABLE ROW LEVEL SECURITY;

-- Make sure the completed_at column exists
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
