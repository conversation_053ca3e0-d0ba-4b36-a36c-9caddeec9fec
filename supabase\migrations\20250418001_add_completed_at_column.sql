-- Add completed_at column to user_course_enrollment table if it doesn't exist
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update the schema cache
COMMENT ON TABLE public.user_course_enrollment IS 'Table to track user enrollments in courses';
COMMENT ON COLUMN public.user_course_enrollment.completed_at IS 'Timestamp when the course was completed';

-- Make sure the status column has the correct type
ALTER TABLE IF EXISTS public.user_course_enrollment 
ALTER COLUMN status TYPE TEXT;
