-- Create storage buckets for the application
-- This migration creates the necessary buckets for file storage

-- Insert buckets directly into storage.buckets table
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
  ('course-images', 'course-images', true, 52428800, NULL),
  ('avatars', 'avatars', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('app-uploads', 'app-uploads', true, 52428800, NULL),
  ('uploads', 'uploads', true, 52428800, NULL),
  ('default-bucket', 'default-bucket', true, 52428800, NULL)
ON CONFLICT (id) DO NOTHING;

-- Add a comment to explain the migration
COMMENT ON TABLE storage.buckets IS 'Storage buckets for the application';
