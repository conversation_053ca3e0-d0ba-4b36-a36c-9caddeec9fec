-- Fix RLS policies for storage buckets
-- This migration adds proper RLS policies to allow authenticated users to upload files

-- Enable RLS for storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow public access to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to app-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to default-bucket" ON storage.objects;

DROP POLICY IF EXISTS "Allow authenticated uploads to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to app-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to default-bucket" ON storage.objects;

-- Create policies for public read access to all buckets
CREATE POLICY "Allow public access to course-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'course-images');

CREATE POLICY "Allow public access to avatars"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

CREATE POLICY "Allow public access to app-uploads"
ON storage.objects FOR SELECT
USING (bucket_id = 'app-uploads');

CREATE POLICY "Allow public access to uploads"
ON storage.objects FOR SELECT
USING (bucket_id = 'uploads');

CREATE POLICY "Allow public access to default-bucket"
ON storage.objects FOR SELECT
USING (bucket_id = 'default-bucket');

-- Create policies for authenticated users to upload to all buckets
CREATE POLICY "Allow authenticated uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to avatars"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'avatars' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to app-uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'app-uploads' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'uploads' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to default-bucket"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'default-bucket' AND
  auth.role() = 'authenticated'
);

-- Create policies for authenticated users to update their own uploads
CREATE POLICY "Allow authenticated users to update their uploads"
ON storage.objects FOR UPDATE
USING (auth.uid() = owner)
WITH CHECK (auth.uid() = owner);

-- Create policies for authenticated users to delete their own uploads
CREATE POLICY "Allow authenticated users to delete their uploads"
ON storage.objects FOR DELETE
USING (auth.uid() = owner);

-- Create a policy for service role to bypass RLS
CREATE POLICY "Service role bypass for storage"
ON storage.objects
USING (auth.jwt() ->> 'role' = 'service_role');

-- Create a policy for anon users to upload to specific buckets (if needed)
CREATE POLICY "Allow anonymous uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'anon'
);

-- Add a comment to explain the migration
COMMENT ON TABLE storage.objects IS 'Storage objects with proper RLS policies for authenticated and anonymous users';
