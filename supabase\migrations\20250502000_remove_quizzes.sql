-- Migration: Remove Quizzes
-- Created at: 2025-05-02
-- Description: Removes all quiz-related tables and data

-- First drop tables with foreign key dependencies
DROP TABLE IF EXISTS public.user_quiz_answers;
DROP TABLE IF EXISTS public.user_quiz_attempts;
DROP TABLE IF EXISTS public.quiz_answers;
DROP TABLE IF EXISTS public.quiz_questions;

-- Remove quiz type from lessons table
ALTER TABLE public.lessons DROP CONSTRAINT IF EXISTS lessons_type_check;
ALTER TABLE public.lessons ADD CONSTRAINT lessons_type_check 
  CHECK (type IN ('lesson', 'assignment'));