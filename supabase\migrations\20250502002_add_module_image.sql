-- Add image_url column to modules table
ALTER TABLE modules ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Enable Row Level Security
ALTER TABLE modules ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view modules" ON modules;
DROP POLICY IF EXISTS "Teachers can update their modules" ON modules;

-- Create new policies
CREATE POLICY "Users can view modules" ON modules
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM user_course_enrollment e
        WHERE e.course_id = modules.course_id
        AND e.user_id = auth.uid()
    ));

CREATE POLICY "Teachers can update their modules" ON modules
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM courses c
            WHERE c.id = modules.course_id
            AND (
                c.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM user_roles
                    WHERE user_id = auth.uid()
                    AND role = 'teacher'
                )
            )
        )
    );