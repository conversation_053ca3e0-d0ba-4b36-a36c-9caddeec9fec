-- Drop existing policies
DROP POLICY IF EXISTS "Users can view modules" ON modules;
DROP POLICY IF EXISTS "Teachers can update their modules" ON modules;

-- Create new policies
CREATE POLICY "Users can view modules" ON modules
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM user_course_enrollment e
        WHERE e.course_id = modules.course_id
        AND e.user_id = auth.uid()
    ));

CREATE POLICY "Teachers can update their modules" ON modules
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM user_roles
            WHERE user_id = auth.uid()
            AND role = 'teacher'
        )
    ); 