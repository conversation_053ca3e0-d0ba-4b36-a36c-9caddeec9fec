-- Create user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  auto_complete_courses BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for user_preferences
CREATE POLICY "Users can view their own preferences"
ON public.user_preferences FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences"
ON public.user_preferences FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences"
ON public.user_preferences FOR UPDATE
USING (auth.uid() = user_id);

-- Add to realtime publication if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
  ) THEN
    ALTER PUBLICATION supabase_realtime ADD TABLE public.user_preferences;
  END IF;
END
$$;

-- Set up replica identity for realtime
ALTER TABLE public.user_preferences REPLICA IDENTITY FULL;

-- Create function to reset module completion for a user
CREATE OR REPLACE FUNCTION public.reset_module_completion(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_success BOOLEAN := TRUE;
BEGIN
  -- Delete all user lesson progress
  DELETE FROM public.user_lesson_progress
  WHERE user_id = p_user_id;

  -- Reset module completion status
  -- Note: This doesn't actually change the module's is_completed status
  -- because that would affect all users. Instead, we rely on the fact that
  -- module completion is determined by lesson progress for each user.

  RETURN v_success;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in reset_module_completion function: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.reset_module_completion(UUID) TO authenticated;
