-- Create achievements table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.achievements (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  points INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Create user_achievements table for tracking user achievements
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES public.achievements(id) ON DELETE CASCADE,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  is_claimed BOOLEAN DEFAULT FALSE,
  module_id UUID REFERENCES public.modules(id) ON DELETE SET NULL,
  course_id UUID REFERENCES public.courses(id) ON DELETE SET NULL,
  UNIQUE(user_id, achievement_id, module_id, course_id)
);

-- Add RLS policies
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;

-- Allow users to view all achievements
CREATE POLICY "Anyone can view achievements"
  ON public.achievements
  FOR SELECT
  USING (true);

-- Allow users to view their own achievements
CREATE POLICY "Users can view their own achievements"
  ON public.user_achievements
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow authenticated users to update their own achievements (for claiming badges)
CREATE POLICY "Users can update their own achievements"
  ON public.user_achievements
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Insert default module badges
INSERT INTO public.achievements (name, description, icon, points)
VALUES 
  ('Bold Step', 'Completed Module 1', 'bold-step', 100),
  ('Persistence', 'Completed Module 2', 'persistence', 100),
  ('Consistency', 'Completed Module 3', 'consistency', 100),
  ('Perseverance', 'Completed Module 4', 'perseverance', 100),
  ('Discipline', 'Completed Module 5', 'discipline', 100),
  ('Last Straw', 'Completed Module 6', 'last-straw', 100),
  ('Achiever', 'Completed Module 7', 'achiever', 100),
  ('Course Master', 'Completed all modules in a course', 'course-master', 500)
ON CONFLICT (name) DO NOTHING;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON public.user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_module_id ON public.user_achievements(module_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_course_id ON public.user_achievements(course_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_claimed ON public.user_achievements(is_claimed);

-- Add comments
COMMENT ON TABLE public.achievements IS 'Defines available achievements and badges';
COMMENT ON TABLE public.user_achievements IS 'Tracks user achievements and badges';
COMMENT ON COLUMN public.user_achievements.is_claimed IS 'Whether the badge has been claimed by the user';
COMMENT ON COLUMN public.user_achievements.module_id IS 'The module this achievement is related to, if applicable';
COMMENT ON COLUMN public.user_achievements.course_id IS 'The course this achievement is related to, if applicable';
