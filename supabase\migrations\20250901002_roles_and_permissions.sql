-- Migration: Roles and Permissions
-- Created at: 2025-09-01
-- Description: Creates the role-based access control system

-- =============================================
-- USER ROLES TABLE
-- =============================================

-- Create user_roles table for role-based access control
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('student', 'teacher')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- =============================================
-- ROLE REQUESTS TABLE
-- =============================================

-- Create role_requests table to track teacher role requests
CREATE TABLE IF NOT EXISTS public.role_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  requested_role TEXT NOT NULL CHECK (requested_role IN ('teacher')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  processed_at TIMESTAMP WITH TIME ZONE,
  processed_by UUID REFERENCES auth.users(id),
  notes TEXT,
  UNIQUE(user_id, status) WHERE status = 'pending'
);

-- =============================================
-- ROLE AUDIT TABLE
-- =============================================

-- Create role_audit table to track role changes
CREATE TABLE IF NOT EXISTS public.role_audit (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  changed_by UUID REFERENCES auth.users(id),
  old_role TEXT,
  new_role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- SECURITY POLICIES
-- =============================================

-- Enable Row Level Security
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_audit ENABLE ROW LEVEL SECURITY;

-- User roles policies
CREATE POLICY "Users can view their own role"
ON public.user_roles FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all roles"
ON public.user_roles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update roles"
ON public.user_roles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can insert roles"
ON public.user_roles FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Role requests policies
CREATE POLICY "Users can view their own role requests"
ON public.role_requests FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all role requests"
ON public.role_requests FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own role requests"
ON public.role_requests FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Teachers can update role requests"
ON public.role_requests FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Role audit policies
CREATE POLICY "Teachers can view role audit logs"
ON public.role_audit FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate inputs to prevent SQL injection
  IF _user_id IS NULL OR _role IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- We use a parameterized query for better security
  RETURN EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id AND role = _role
  );
END;
$$;

-- Function to assign a role to a user
CREATE OR REPLACE FUNCTION public.assign_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _old_role TEXT;
BEGIN
  -- Validate inputs
  IF _user_id IS NULL OR _role IS NULL THEN
    RAISE EXCEPTION 'User ID and role are required';
  END IF;
  
  -- Check if role is valid
  IF _role NOT IN ('student', 'teacher') THEN
    RAISE EXCEPTION 'Invalid role: %', _role;
  END IF;
  
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = _user_id) THEN
    RAISE EXCEPTION 'User does not exist';
  END IF;
  
  -- Get the current role if it exists
  SELECT role INTO _old_role
  FROM public.user_roles
  WHERE user_id = _user_id;
  
  -- Insert or update the role
  IF _old_role IS NULL THEN
    -- Insert new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (_user_id, _role);
  ELSE
    -- Update existing role
    UPDATE public.user_roles
    SET role = _role, updated_at = NOW()
    WHERE user_id = _user_id;
  END IF;
  
  -- Record the change in the audit log
  INSERT INTO public.role_audit (user_id, changed_by, old_role, new_role)
  VALUES (_user_id, auth.uid(), _old_role, _role);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Function to approve a role request
CREATE OR REPLACE FUNCTION public.approve_role_request(
  _request_id UUID,
  _notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _user_id UUID;
  _requested_role TEXT;
BEGIN
  -- Check if the request exists and is pending
  SELECT user_id, requested_role INTO _user_id, _requested_role
  FROM public.role_requests
  WHERE id = _request_id AND status = 'pending';
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Role request not found or not pending';
  END IF;
  
  -- Update the request status
  UPDATE public.role_requests
  SET 
    status = 'approved',
    processed_at = NOW(),
    processed_by = auth.uid(),
    notes = _notes
  WHERE id = _request_id;
  
  -- Assign the requested role to the user
  PERFORM public.assign_role(_user_id, _requested_role);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Function to reject a role request
CREATE OR REPLACE FUNCTION public.reject_role_request(
  _request_id UUID,
  _notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the request exists and is pending
  IF NOT EXISTS (
    SELECT 1 FROM public.role_requests
    WHERE id = _request_id AND status = 'pending'
  ) THEN
    RAISE EXCEPTION 'Role request not found or not pending';
  END IF;
  
  -- Update the request status
  UPDATE public.role_requests
  SET 
    status = 'rejected',
    processed_at = NOW(),
    processed_by = auth.uid(),
    notes = _notes
  WHERE id = _request_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.has_role TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_role TO authenticated;
GRANT EXECUTE ON FUNCTION public.approve_role_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.reject_role_request TO authenticated;
