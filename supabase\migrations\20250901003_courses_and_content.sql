-- Migration: Courses and Content
-- Created at: 2025-09-01
-- Description: Creates the course-related tables and content structure

-- =============================================
-- COURSES TABLE
-- =============================================

-- Create courses table
CREATE TABLE IF NOT EXISTS public.courses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  instructor TEXT NOT NULL,
  image_url TEXT,
  total_modules INTEGER DEFAULT 0,
  completed_modules INTEGER DEFAULT 0,
  is_published BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- MODULES TABLE
-- =============================================

-- Create modules table
CREATE TABLE IF NOT EXISTS public.modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  module_number INTEGER NOT NULL,
  is_locked BOOLEAN DEFAULT false,
  is_published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(course_id, slug)
);

-- =============================================
-- LESSONS TABLE
-- =============================================

-- Create lessons table
CREATE TABLE IF NOT EXISTS public.lessons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  content TEXT,
  duration TEXT,
  type TEXT NOT NULL CHECK (type IN ('lesson', 'quiz', 'assignment')),
  lesson_number INTEGER NOT NULL,
  requirement TEXT,
  video_url TEXT,
  image_url TEXT,
  is_published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(module_id, slug)
);

-- =============================================
-- QUIZ QUESTIONS TABLE
-- =============================================

-- Create quiz_questions table
CREATE TABLE IF NOT EXISTS public.quiz_questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  question_type TEXT NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer')),
  points INTEGER DEFAULT 1,
  question_order INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- QUIZ ANSWERS TABLE
-- =============================================

-- Create quiz_answers table
CREATE TABLE IF NOT EXISTS public.quiz_answers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID REFERENCES public.quiz_questions(id) ON DELETE CASCADE,
  answer_text TEXT NOT NULL,
  is_correct BOOLEAN DEFAULT false,
  answer_order INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- COURSE TAGS TABLE
-- =============================================

-- Create course_tags table
CREATE TABLE IF NOT EXISTS public.course_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- COURSE TAG RELATIONS TABLE
-- =============================================

-- Create course_tag_relations table
CREATE TABLE IF NOT EXISTS public.course_tag_relations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.course_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(course_id, tag_id)
);

-- =============================================
-- SECURITY POLICIES
-- =============================================

-- Enable Row Level Security
ALTER TABLE public.courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.course_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.course_tag_relations ENABLE ROW LEVEL SECURITY;

-- Courses policies
CREATE POLICY "Anyone can view published courses"
ON public.courses FOR SELECT
USING (is_published = true OR created_by = auth.uid());

CREATE POLICY "Teachers can insert courses"
ON public.courses FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update their own courses"
ON public.courses FOR UPDATE
USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete their own courses"
ON public.courses FOR DELETE
USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Modules policies
CREATE POLICY "Anyone can view published modules"
ON public.modules FOR SELECT
USING (
  is_published = true OR
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  )
);

CREATE POLICY "Teachers can insert modules"
ON public.modules FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update modules"
ON public.modules FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete modules"
ON public.modules FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Lessons policies
CREATE POLICY "Anyone can view published lessons"
ON public.lessons FOR SELECT
USING (
  is_published = true OR
  EXISTS (
    SELECT 1 FROM public.modules m
    JOIN public.courses c ON m.course_id = c.id
    WHERE m.id = module_id AND c.created_by = auth.uid()
  )
);

CREATE POLICY "Teachers can insert lessons"
ON public.lessons FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.modules m
    JOIN public.courses c ON m.course_id = c.id
    WHERE m.id = module_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update lessons"
ON public.lessons FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.modules m
    JOIN public.courses c ON m.course_id = c.id
    WHERE m.id = module_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete lessons"
ON public.lessons FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.modules m
    JOIN public.courses c ON m.course_id = c.id
    WHERE m.id = module_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Quiz questions policies
CREATE POLICY "Anyone can view quiz questions"
ON public.quiz_questions FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.lessons l
    WHERE l.id = lesson_id AND l.is_published = true
  ) OR
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  )
);

CREATE POLICY "Teachers can insert quiz questions"
ON public.quiz_questions FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update quiz questions"
ON public.quiz_questions FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete quiz questions"
ON public.quiz_questions FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.lessons l
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE l.id = lesson_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Quiz answers policies
CREATE POLICY "Anyone can view quiz answers"
ON public.quiz_answers FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.quiz_questions q
    JOIN public.lessons l ON q.lesson_id = l.id
    WHERE q.id = question_id AND l.is_published = true
  ) OR
  EXISTS (
    SELECT 1 FROM public.quiz_questions q
    JOIN public.lessons l ON q.lesson_id = l.id
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE q.id = question_id AND c.created_by = auth.uid()
  )
);

CREATE POLICY "Teachers can insert quiz answers"
ON public.quiz_answers FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.quiz_questions q
    JOIN public.lessons l ON q.lesson_id = l.id
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE q.id = question_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update quiz answers"
ON public.quiz_answers FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.quiz_questions q
    JOIN public.lessons l ON q.lesson_id = l.id
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE q.id = question_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete quiz answers"
ON public.quiz_answers FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.quiz_questions q
    JOIN public.lessons l ON q.lesson_id = l.id
    JOIN public.modules m ON l.module_id = m.id
    JOIN public.courses c ON m.course_id = c.id
    WHERE q.id = question_id AND c.created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Course tags policies
CREATE POLICY "Anyone can view course tags"
ON public.course_tags FOR SELECT
USING (true);

CREATE POLICY "Teachers can insert course tags"
ON public.course_tags FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update course tags"
ON public.course_tags FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Course tag relations policies
CREATE POLICY "Anyone can view course tag relations"
ON public.course_tag_relations FOR SELECT
USING (true);

CREATE POLICY "Teachers can insert course tag relations"
ON public.course_tag_relations FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete course tag relations"
ON public.course_tag_relations FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.courses
    WHERE id = course_id AND created_by = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update course module counts
CREATE OR REPLACE FUNCTION public.update_course_module_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment total_modules count
    UPDATE public.courses
    SET total_modules = total_modules + 1
    WHERE id = NEW.course_id;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement total_modules count
    UPDATE public.courses
    SET total_modules = GREATEST(0, total_modules - 1)
    WHERE id = OLD.course_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for module count updates
DROP TRIGGER IF EXISTS update_course_module_count ON public.modules;
CREATE TRIGGER update_course_module_count
AFTER INSERT OR DELETE ON public.modules
FOR EACH ROW EXECUTE FUNCTION public.update_course_module_count();

-- Function to get course details with module and lesson counts
CREATE OR REPLACE FUNCTION public.get_course_details(course_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _result JSON;
BEGIN
  SELECT 
    json_build_object(
      'id', c.id,
      'title', c.title,
      'slug', c.slug,
      'description', c.description,
      'instructor', c.instructor,
      'image_url', c.image_url,
      'total_modules', c.total_modules,
      'completed_modules', c.completed_modules,
      'is_published', c.is_published,
      'created_at', c.created_at,
      'updated_at', c.updated_at,
      'module_count', (SELECT COUNT(*) FROM public.modules WHERE course_id = c.id),
      'lesson_count', (
        SELECT COUNT(*) 
        FROM public.lessons l
        JOIN public.modules m ON l.module_id = m.id
        WHERE m.course_id = c.id
      ),
      'tags', (
        SELECT json_agg(t.name)
        FROM public.course_tag_relations r
        JOIN public.course_tags t ON r.tag_id = t.id
        WHERE r.course_id = c.id
      )
    ) INTO _result
  FROM public.courses c
  WHERE c.id = course_id;
  
  RETURN _result;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_course_details TO authenticated;
