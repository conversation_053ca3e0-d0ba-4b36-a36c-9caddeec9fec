-- Migration: User Progress and Enrollment
-- Created at: 2025-09-01
-- Description: Creates tables for tracking user progress and course enrollment

-- =============================================
-- USER COURSE ENROLLMENT TABLE
-- =============================================

-- Create user_course_enrollment table
CREATE TABLE IF NOT EXISTS public.user_course_enrollment (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, course_id)
);

-- =============================================
-- USER COURSE PROGRESS TABLE
-- =============================================

-- Create user_course_progress table
CREATE TABLE IF NOT EXISTS public.user_course_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  hours_spent NUMERIC(10, 2) DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, course_id)
);

-- =============================================
-- USER MODULE PROGRESS TABLE
-- =============================================

-- Create user_module_progress table
CREATE TABLE IF NOT EXISTS public.user_module_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, module_id)
);

-- =============================================
-- USER LESSON PROGRESS TABLE
-- =============================================

-- Create user_lesson_progress table
CREATE TABLE IF NOT EXISTS public.user_lesson_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  is_completed BOOLEAN DEFAULT false,
  progress_percent INTEGER DEFAULT 0,
  time_spent NUMERIC(10, 2) DEFAULT 0,
  last_position TEXT,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, lesson_id)
);

-- =============================================
-- USER QUIZ ATTEMPTS TABLE
-- =============================================

-- Create user_quiz_attempts table
CREATE TABLE IF NOT EXISTS public.user_quiz_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  score INTEGER DEFAULT 0,
  max_score INTEGER DEFAULT 0,
  passed BOOLEAN DEFAULT false,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- USER QUIZ ANSWERS TABLE
-- =============================================

-- Create user_quiz_answers table
CREATE TABLE IF NOT EXISTS public.user_quiz_answers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  attempt_id UUID REFERENCES public.user_quiz_attempts(id) ON DELETE CASCADE,
  question_id UUID REFERENCES public.quiz_questions(id) ON DELETE CASCADE,
  answer_id UUID REFERENCES public.quiz_answers(id) ON DELETE SET NULL,
  text_answer TEXT,
  is_correct BOOLEAN DEFAULT false,
  points_earned INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(attempt_id, question_id)
);

-- =============================================
-- ACHIEVEMENTS TABLE
-- =============================================

-- Create achievements table
CREATE TABLE IF NOT EXISTS public.achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  points INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- USER ACHIEVEMENTS TABLE
-- =============================================

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES public.achievements(id) ON DELETE CASCADE,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, achievement_id)
);

-- =============================================
-- SECURITY POLICIES
-- =============================================

-- Enable Row Level Security
ALTER TABLE public.user_course_enrollment ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_course_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_lesson_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;

-- User course enrollment policies
CREATE POLICY "Users can view their own enrollments"
ON public.user_course_enrollment FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all enrollments"
ON public.user_course_enrollment FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own enrollments"
ON public.user_course_enrollment FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own enrollments"
ON public.user_course_enrollment FOR UPDATE
USING (auth.uid() = user_id);

-- User course progress policies
CREATE POLICY "Users can view their own course progress"
ON public.user_course_progress FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all course progress"
ON public.user_course_progress FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own course progress"
ON public.user_course_progress FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own course progress"
ON public.user_course_progress FOR UPDATE
USING (auth.uid() = user_id);

-- User module progress policies
CREATE POLICY "Users can view their own module progress"
ON public.user_module_progress FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all module progress"
ON public.user_module_progress FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own module progress"
ON public.user_module_progress FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own module progress"
ON public.user_module_progress FOR UPDATE
USING (auth.uid() = user_id);

-- User lesson progress policies
CREATE POLICY "Users can view their own lesson progress"
ON public.user_lesson_progress FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all lesson progress"
ON public.user_lesson_progress FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own lesson progress"
ON public.user_lesson_progress FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own lesson progress"
ON public.user_lesson_progress FOR UPDATE
USING (auth.uid() = user_id);

-- User quiz attempts policies
CREATE POLICY "Users can view their own quiz attempts"
ON public.user_quiz_attempts FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all quiz attempts"
ON public.user_quiz_attempts FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own quiz attempts"
ON public.user_quiz_attempts FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own quiz attempts"
ON public.user_quiz_attempts FOR UPDATE
USING (auth.uid() = user_id);

-- User quiz answers policies
CREATE POLICY "Users can view their own quiz answers"
ON public.user_quiz_answers FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_quiz_attempts
    WHERE id = attempt_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Teachers can view all quiz answers"
ON public.user_quiz_answers FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own quiz answers"
ON public.user_quiz_answers FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_quiz_attempts
    WHERE id = attempt_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own quiz answers"
ON public.user_quiz_answers FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_quiz_attempts
    WHERE id = attempt_id AND user_id = auth.uid()
  )
);

-- Achievements policies
CREATE POLICY "Anyone can view achievements"
ON public.achievements FOR SELECT
USING (true);

CREATE POLICY "Teachers can insert achievements"
ON public.achievements FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update achievements"
ON public.achievements FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- User achievements policies
CREATE POLICY "Users can view their own achievements"
ON public.user_achievements FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all user achievements"
ON public.user_achievements FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "System can insert user achievements"
ON public.user_achievements FOR INSERT
WITH CHECK (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update user points when achievement is earned
CREATE OR REPLACE FUNCTION public.update_user_points()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.profiles
  SET 
    total_points = total_points + (
      SELECT points FROM public.achievements WHERE id = NEW.achievement_id
    ),
    updated_at = NOW()
  WHERE id = NEW.user_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updating user points
DROP TRIGGER IF EXISTS update_user_points ON public.user_achievements;
CREATE TRIGGER update_user_points
AFTER INSERT ON public.user_achievements
FOR EACH ROW EXECUTE FUNCTION public.update_user_points();

-- Function to update course enrollment status when module progress changes
CREATE OR REPLACE FUNCTION public.update_course_enrollment_status()
RETURNS TRIGGER AS $$
DECLARE
  _course_id UUID;
  _total_modules INTEGER;
  _completed_modules INTEGER;
  _enrollment_id UUID;
BEGIN
  -- Get the course ID for this module
  SELECT course_id INTO _course_id
  FROM public.modules
  WHERE id = NEW.module_id;
  
  IF _course_id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Count total and completed modules
  SELECT COUNT(*) INTO _total_modules
  FROM public.modules
  WHERE course_id = _course_id;
  
  SELECT COUNT(*) INTO _completed_modules
  FROM public.user_module_progress p
  JOIN public.modules m ON p.module_id = m.id
  WHERE m.course_id = _course_id AND p.user_id = NEW.user_id AND p.is_completed = true;
  
  -- Update the course's completed_modules count
  UPDATE public.courses
  SET completed_modules = _completed_modules
  WHERE id = _course_id;
  
  -- Get the enrollment ID
  SELECT id INTO _enrollment_id
  FROM public.user_course_enrollment
  WHERE user_id = NEW.user_id AND course_id = _course_id;
  
  -- If no enrollment exists, create one
  IF _enrollment_id IS NULL THEN
    INSERT INTO public.user_course_enrollment (user_id, course_id, status)
    VALUES (NEW.user_id, _course_id, 'in_progress')
    RETURNING id INTO _enrollment_id;
  END IF;
  
  -- Update enrollment status based on module completion
  IF _completed_modules = 0 THEN
    UPDATE public.user_course_enrollment
    SET status = 'not_started', updated_at = NOW()
    WHERE id = _enrollment_id;
  ELSIF _completed_modules = _total_modules THEN
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed', 
      completed_at = NOW(),
      updated_at = NOW()
    WHERE id = _enrollment_id;
  ELSE
    UPDATE public.user_course_enrollment
    SET status = 'in_progress', updated_at = NOW()
    WHERE id = _enrollment_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updating course enrollment status
DROP TRIGGER IF EXISTS update_course_enrollment_status ON public.user_module_progress;
CREATE TRIGGER update_course_enrollment_status
AFTER INSERT OR UPDATE OF is_completed ON public.user_module_progress
FOR EACH ROW EXECUTE FUNCTION public.update_course_enrollment_status();

-- Function to update module progress when lesson progress changes
CREATE OR REPLACE FUNCTION public.update_module_progress()
RETURNS TRIGGER AS $$
DECLARE
  _module_id UUID;
  _total_lessons INTEGER;
  _completed_lessons INTEGER;
  _module_progress_id UUID;
BEGIN
  -- Get the module ID for this lesson
  SELECT module_id INTO _module_id
  FROM public.lessons
  WHERE id = NEW.lesson_id;
  
  IF _module_id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Count total and completed lessons
  SELECT COUNT(*) INTO _total_lessons
  FROM public.lessons
  WHERE module_id = _module_id;
  
  SELECT COUNT(*) INTO _completed_lessons
  FROM public.user_lesson_progress p
  JOIN public.lessons l ON p.lesson_id = l.id
  WHERE l.module_id = _module_id AND p.user_id = NEW.user_id AND p.is_completed = true;
  
  -- Get the module progress ID
  SELECT id INTO _module_progress_id
  FROM public.user_module_progress
  WHERE user_id = NEW.user_id AND module_id = _module_id;
  
  -- If no module progress exists, create one
  IF _module_progress_id IS NULL THEN
    INSERT INTO public.user_module_progress (user_id, module_id, is_completed)
    VALUES (NEW.user_id, _module_id, _completed_lessons = _total_lessons)
    RETURNING id INTO _module_progress_id;
  ELSE
    -- Update module progress based on lesson completion
    UPDATE public.user_module_progress
    SET 
      is_completed = _completed_lessons = _total_lessons,
      completed_at = CASE WHEN _completed_lessons = _total_lessons THEN NOW() ELSE completed_at END,
      updated_at = NOW()
    WHERE id = _module_progress_id;
  END IF;
  
  -- Update the user's completed_lessons count
  UPDATE public.profiles
  SET 
    completed_lessons = (
      SELECT COUNT(*) 
      FROM public.user_lesson_progress 
      WHERE user_id = NEW.user_id AND is_completed = true
    ),
    updated_at = NOW()
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updating module progress
DROP TRIGGER IF EXISTS update_module_progress ON public.user_lesson_progress;
CREATE TRIGGER update_module_progress
AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
FOR EACH ROW EXECUTE FUNCTION public.update_module_progress();

-- Function to get user course progress
CREATE OR REPLACE FUNCTION public.get_user_course_progress(
  _user_id UUID,
  _course_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _result JSON;
BEGIN
  SELECT 
    json_build_object(
      'course_id', c.id,
      'course_title', c.title,
      'total_modules', c.total_modules,
      'completed_modules', (
        SELECT COUNT(*) 
        FROM public.user_module_progress p
        JOIN public.modules m ON p.module_id = m.id
        WHERE m.course_id = c.id AND p.user_id = _user_id AND p.is_completed = true
      ),
      'total_lessons', (
        SELECT COUNT(*) 
        FROM public.lessons l
        JOIN public.modules m ON l.module_id = m.id
        WHERE m.course_id = c.id
      ),
      'completed_lessons', (
        SELECT COUNT(*) 
        FROM public.user_lesson_progress p
        JOIN public.lessons l ON p.lesson_id = l.id
        JOIN public.modules m ON l.module_id = m.id
        WHERE m.course_id = c.id AND p.user_id = _user_id AND p.is_completed = true
      ),
      'enrollment_status', (
        SELECT status
        FROM public.user_course_enrollment
        WHERE user_id = _user_id AND course_id = c.id
      ),
      'hours_spent', (
        SELECT hours_spent
        FROM public.user_course_progress
        WHERE user_id = _user_id AND course_id = c.id
      ),
      'last_accessed_at', (
        SELECT last_accessed_at
        FROM public.user_course_progress
        WHERE user_id = _user_id AND course_id = c.id
      )
    ) INTO _result
  FROM public.courses c
  WHERE c.id = _course_id;
  
  RETURN _result;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_user_course_progress TO authenticated;
