-- Migration: Notifications and Messaging
-- Created at: 2025-09-01
-- Description: Creates tables for notifications, messaging, and feedback

-- =============================================
-- NOTIFICATIONS TABLE
-- =============================================

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  read_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- NOTIFICATION SETTINGS TABLE
-- =============================================

-- Create notification_settings table
CREATE TABLE IF NOT EXISTS public.notification_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_updates BOOLEAN DEFAULT true,
  new_achievements BOOLEAN DEFAULT true,
  learning_reminders BOOLEAN DEFAULT true,
  progress_reports BOOLEAN DEFAULT true,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- =============================================
-- FEEDBACK TABLE
-- =============================================

-- Create feedback table
CREATE TABLE IF NOT EXISTS public.feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  course_id UUID REFERENCES public.courses(id) ON DELETE SET NULL,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE SET NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- =============================================
-- SECURITY POLICIES
-- =============================================

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

-- Notifications policies
CREATE POLICY "Users can view their own notifications"
ON public.notifications FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
ON public.notifications FOR UPDATE
USING (auth.uid() = user_id);

-- Notification settings policies
CREATE POLICY "Users can view their own notification settings"
ON public.notification_settings FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification settings"
ON public.notification_settings FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notification settings"
ON public.notification_settings FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Feedback policies
CREATE POLICY "Users can view their own feedback"
ON public.feedback FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all feedback"
ON public.feedback FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own feedback"
ON public.feedback FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own feedback"
ON public.feedback FOR UPDATE
USING (auth.uid() = user_id);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to mark a notification as read
CREATE OR REPLACE FUNCTION public.mark_notification_read(
  _notification_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the notification exists and belongs to the current user
  IF NOT EXISTS (
    SELECT 1 FROM public.notifications
    WHERE id = _notification_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Notification not found or does not belong to the current user';
  END IF;
  
  -- Update the notification
  UPDATE public.notifications
  SET 
    read = true,
    read_at = NOW()
  WHERE id = _notification_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION public.mark_all_notifications_read()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update all unread notifications for the current user
  UPDATE public.notifications
  SET 
    read = true,
    read_at = NOW()
  WHERE user_id = auth.uid() AND read = false;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Function to create a notification
CREATE OR REPLACE FUNCTION public.create_notification(
  _user_id UUID,
  _type TEXT,
  _title TEXT,
  _message TEXT,
  _data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _notification_id UUID;
BEGIN
  -- Validate inputs
  IF _user_id IS NULL OR _type IS NULL OR _title IS NULL OR _message IS NULL THEN
    RAISE EXCEPTION 'User ID, type, title, and message are required';
  END IF;
  
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = _user_id) THEN
    RAISE EXCEPTION 'User does not exist';
  END IF;
  
  -- Check notification settings
  IF _type = 'course_update' AND NOT (
    SELECT course_updates FROM public.notification_settings WHERE user_id = _user_id
  ) THEN
    RETURN NULL;
  ELSIF _type = 'achievement' AND NOT (
    SELECT new_achievements FROM public.notification_settings WHERE user_id = _user_id
  ) THEN
    RETURN NULL;
  ELSIF _type = 'reminder' AND NOT (
    SELECT learning_reminders FROM public.notification_settings WHERE user_id = _user_id
  ) THEN
    RETURN NULL;
  ELSIF _type = 'progress' AND NOT (
    SELECT progress_reports FROM public.notification_settings WHERE user_id = _user_id
  ) THEN
    RETURN NULL;
  END IF;
  
  -- Insert the notification
  INSERT INTO public.notifications (user_id, type, title, message, data)
  VALUES (_user_id, _type, _title, _message, _data)
  RETURNING id INTO _notification_id;
  
  RETURN _notification_id;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Function to notify teachers about a new role request
CREATE OR REPLACE FUNCTION public.notify_teachers_on_role_request()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _user_record RECORD;
  _teacher_record RECORD;
  _user_name TEXT;
  _user_email TEXT;
BEGIN
  -- Only proceed for new teacher role requests
  IF NEW.requested_role = 'teacher' AND NEW.status = 'pending' THEN
    -- Get user information
    SELECT * INTO _user_record FROM auth.users WHERE id = NEW.user_id;
    
    -- Extract user details
    _user_name := _user_record.raw_user_meta_data->>'full_name';
    IF _user_name IS NULL THEN
      _user_name := _user_record.raw_user_meta_data->>'first_name';
      IF _user_name IS NULL THEN
        _user_name := 'A new user';
      END IF;
    END IF;
    _user_email := _user_record.email;
    
    -- Create notifications for all teachers
    FOR _teacher_record IN 
      SELECT user_id FROM public.user_roles WHERE role = 'teacher'
    LOOP
      PERFORM public.create_notification(
        _teacher_record.user_id,
        'role_request',
        'New Teacher Role Request',
        _user_name || ' (' || _user_email || ') has requested teacher privileges.',
        jsonb_build_object(
          'request_id', NEW.id,
          'user_id', NEW.user_id,
          'user_name', _user_name,
          'user_email', _user_email
        )
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create a trigger on the role_requests table
DROP TRIGGER IF EXISTS role_request_notification_trigger ON public.role_requests;
CREATE TRIGGER role_request_notification_trigger
AFTER INSERT ON public.role_requests
FOR EACH ROW
EXECUTE FUNCTION public.notify_teachers_on_role_request();

-- Function to notify user when their role request is processed
CREATE OR REPLACE FUNCTION public.notify_user_on_role_request_processed()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only proceed if the status has changed from pending
  IF OLD.status = 'pending' AND NEW.status IN ('approved', 'rejected') THEN
    IF NEW.status = 'approved' THEN
      -- Create approval notification
      PERFORM public.create_notification(
        NEW.user_id,
        'role_approved',
        'Teacher Role Approved',
        'Your request for teacher privileges has been approved. You now have access to create and manage courses.',
        jsonb_build_object(
          'request_id', NEW.id,
          'notes', NEW.notes
        )
      );
    ELSE
      -- Create rejection notification
      PERFORM public.create_notification(
        NEW.user_id,
        'role_rejected',
        'Teacher Role Request Declined',
        'Your request for teacher privileges has been declined.',
        jsonb_build_object(
          'request_id', NEW.id,
          'notes', NEW.notes
        )
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create a trigger on the role_requests table
DROP TRIGGER IF EXISTS role_request_processed_notification_trigger ON public.role_requests;
CREATE TRIGGER role_request_processed_notification_trigger
AFTER UPDATE ON public.role_requests
FOR EACH ROW
EXECUTE FUNCTION public.notify_user_on_role_request_processed();

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.mark_notification_read TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_all_notifications_read TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_notification TO authenticated;
