-- Comprehensive fix for user_lesson_progress table

-- STEP 1: Check which column exists and standardize to is_completed
DO $$
DECLARE
  completed_exists BOOLEAN;
  is_completed_exists BOOLEAN;
BEGIN
  -- Check if 'completed' column exists
  SELECT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_lesson_progress' 
    AND column_name = 'completed'
  ) INTO completed_exists;
  
  -- Check if 'is_completed' column exists
  SELECT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_lesson_progress' 
    AND column_name = 'is_completed'
  ) INTO is_completed_exists;
  
  -- Log which columns exist
  RAISE NOTICE 'Column status: completed = %, is_completed = %', completed_exists, is_completed_exists;
  
  -- Case 1: Only 'completed' exists -> rename to 'is_completed'
  IF completed_exists AND NOT is_completed_exists THEN
    RAISE NOTICE 'Renaming column completed to is_completed';
    ALTER TABLE public.user_lesson_progress RENAME COLUMN completed TO is_completed;
  
  -- Case 2: Both columns exist -> consolidate data and drop 'completed'
  <PERSON><PERSON><PERSON> completed_exists AND is_completed_exists THEN
    RAISE NOTICE 'Both columns exist, consolidating data';
    -- Update is_completed with completed values where is_completed is false
    UPDATE public.user_lesson_progress 
    SET is_completed = completed 
    WHERE is_completed = false AND completed = true;
    
    -- Drop the completed column
    ALTER TABLE public.user_lesson_progress DROP COLUMN completed;
  
  -- Case 3: Only is_completed exists -> nothing to do
  ELSIF NOT completed_exists AND is_completed_exists THEN
    RAISE NOTICE 'Only is_completed exists, no action needed';
  
  -- Case 4: Neither exists -> add is_completed column
  ELSE
    RAISE NOTICE 'Neither column exists, adding is_completed';
    ALTER TABLE public.user_lesson_progress ADD COLUMN is_completed BOOLEAN DEFAULT false;
  END IF;
END $$;

-- STEP 2: Make sure all triggers use is_completed
-- Drop existing triggers related to lesson completion
DROP TRIGGER IF EXISTS on_lesson_completion ON public.user_lesson_progress;
DROP TRIGGER IF EXISTS update_module_progress ON public.user_lesson_progress;

-- Fix the update_module_completion function to use is_completed
CREATE OR REPLACE FUNCTION update_module_completion()
RETURNS TRIGGER AS $$
DECLARE
  module_id_val UUID;
  user_id_val UUID;
  total_lessons INTEGER;
  completed_lessons INTEGER;
BEGIN
  -- Get the user ID
  user_id_val := NEW.user_id;
  
  -- Get the module ID for this lesson
  SELECT module_id INTO module_id_val FROM public.lessons WHERE id = NEW.lesson_id;
  
  IF module_id_val IS NULL THEN
    RAISE NOTICE 'No module found for lesson %', NEW.lesson_id;
    RETURN NEW;
  END IF;
  
  -- Count total lessons for this module
  SELECT COUNT(*) INTO total_lessons FROM public.lessons WHERE module_id = module_id_val;
  
  -- Count completed lessons for this user and module
  SELECT COUNT(*) INTO completed_lessons 
  FROM public.user_lesson_progress ulp
  JOIN public.lessons l ON ulp.lesson_id = l.id
  WHERE l.module_id = module_id_val 
    AND ulp.user_id = user_id_val 
    AND ulp.is_completed = TRUE;
  
  RAISE NOTICE 'Module % has % completed lessons out of %', module_id_val, completed_lessons, total_lessons;
  
  -- If all lessons are completed, mark the module as completed
  IF completed_lessons = total_lessons THEN
    UPDATE public.modules
    SET 
      is_completed = TRUE,
      updated_at = NOW()
    WHERE id = module_id_val;
    
    RAISE NOTICE 'Marked module % as completed', module_id_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 3: Recreate the triggers with the correct column
CREATE TRIGGER on_lesson_completion
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  WHEN (NEW.is_completed = TRUE)
  EXECUTE FUNCTION update_module_completion();

-- Make sure the update_module_progress trigger also uses is_completed
CREATE TRIGGER update_module_progress
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  EXECUTE FUNCTION public.update_module_progress();

-- STEP 4: Add an index on the is_completed column for better performance
CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_is_completed
ON public.user_lesson_progress(is_completed);

-- STEP 5: Add audit logging for lesson completion
CREATE TABLE IF NOT EXISTS public.completion_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  success BOOLEAN NOT NULL,
  error_message TEXT,
  client_info TEXT
);

-- Create a function to log completion attempts
CREATE OR REPLACE FUNCTION log_completion_attempt()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_completed = TRUE AND (OLD IS NULL OR OLD.is_completed = FALSE) THEN
    INSERT INTO public.completion_audit_log (
      user_id, 
      lesson_id, 
      completed_at,
      success,
      error_message
    ) VALUES (
      NEW.user_id,
      NEW.lesson_id,
      NOW(),
      TRUE,
      NULL
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger for the audit log
CREATE TRIGGER log_lesson_completion_attempt
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  EXECUTE FUNCTION log_completion_attempt(); 