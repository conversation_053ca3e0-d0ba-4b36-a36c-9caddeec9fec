-- Create stored procedure for getting column information
CREATE OR REPLACE FUNCTION public.get_column_info(table_name text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_agg(
    json_build_object(
      'column_name', column_name,
      'data_type', data_type,
      'is_nullable', is_nullable,
      'column_default', column_default
    )
  ) INTO result
  FROM information_schema.columns
  WHERE table_schema = 'public'
    AND table_name = $1;
  
  RETURN result;
END;
$$;

-- Create function to get database trigger information
CREATE OR REPLACE FUNCTION public.get_trigger_info(table_name text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_agg(
    json_build_object(
      'trigger_name', trigger_name,
      'event_manipulation', event_manipulation,
      'action_statement', action_statement,
      'action_condition', action_condition,
      'action_timing', action_timing
    )
  ) INTO result
  FROM information_schema.triggers
  WHERE event_object_schema = 'public'
    AND event_object_table = $1;
  
  RETURN result;
END;
$$;

-- Create function to count completion records
CREATE OR REPLACE FUNCTION public.count_completion_records(user_id_param uuid DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  user_filter text := CASE WHEN user_id_param IS NULL THEN '' ELSE ' AND user_id = ''' || user_id_param || '''' END;
BEGIN
  EXECUTE format('
    SELECT json_build_object(
      ''total'', (SELECT COUNT(*) FROM public.user_lesson_progress),
      ''completed'', (SELECT COUNT(*) FROM public.user_lesson_progress WHERE is_completed = true%s),
      ''users'', (SELECT COUNT(DISTINCT user_id) FROM public.user_lesson_progress),
      ''lessons'', (SELECT COUNT(DISTINCT lesson_id) FROM public.user_lesson_progress)
    )', user_filter) INTO result;
  
  RETURN result;
END;
$$;

-- Grant access to these functions
GRANT EXECUTE ON FUNCTION public.get_column_info TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_trigger_info TO authenticated;
GRANT EXECUTE ON FUNCTION public.count_completion_records TO authenticated; 