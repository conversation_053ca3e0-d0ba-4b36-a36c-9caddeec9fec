-- Create a function to execute SQL directly
-- This is a last resort approach for when the schema cache is out of sync

-- Create the function
CREATE OR REPLACE FUNCTION public.execute_sql(sql_query TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Execute the SQL query
  EXECUTE sql_query;
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error executing SQL: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.execute_sql TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.execute_sql IS 'Executes a SQL query directly. Use with caution as a last resort.';
