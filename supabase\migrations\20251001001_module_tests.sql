-- Migration: Module Tests
-- Created at: 2025-10-01
-- Description: Creates the module tests table for pre and post tests

-- =============================================
-- MODULE TESTS TABLE
-- =============================================

-- Create module_tests table
CREATE TABLE IF NOT EXISTS public.module_tests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('pre_test', 'post_test')),
  description TEXT,
  questions JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(module_id, type) -- A module can have at most one pre-test and one post-test
);

-- =============================================
-- MODULE TEST RESPONSES TABLE
-- =============================================

-- Create module_test_responses table
CREATE TABLE IF NOT EXISTS public.module_test_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  test_id UUID REFERENCES public.module_tests(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  responses JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(test_id, user_id) -- A user can respond to a test only once
);

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable Row Level Security
ALTER TABLE public.module_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.module_test_responses ENABLE ROW LEVEL SECURITY;

-- =============================================
-- RLS POLICIES FOR MODULE TESTS
-- =============================================

-- Any authenticated user can view tests
CREATE POLICY "Anyone can view module tests" 
ON public.module_tests FOR SELECT 
USING (true);

-- Only teachers can create/update/delete tests
CREATE POLICY "Teachers can insert module tests" 
ON public.module_tests FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update module tests" 
ON public.module_tests FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete module tests" 
ON public.module_tests FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =============================================
-- RLS POLICIES FOR MODULE TEST RESPONSES
-- =============================================

-- Users can only see their own responses
CREATE POLICY "Users can view their own responses" 
ON public.module_test_responses FOR SELECT 
USING (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Users can insert their own responses
CREATE POLICY "Users can insert their own responses" 
ON public.module_test_responses FOR INSERT 
WITH CHECK (
  auth.uid() = user_id
);

-- Users can update their own responses
CREATE POLICY "Users can update their own responses" 
ON public.module_test_responses FOR UPDATE 
USING (
  auth.uid() = user_id
);

-- Teachers can view all responses
CREATE POLICY "Teachers can view all responses" 
ON public.module_test_responses FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Service role bypass policies
CREATE POLICY "Service role bypass for module_tests" 
ON public.module_tests USING (auth.jwt() ? 'service_role');

CREATE POLICY "Service role bypass for module_test_responses" 
ON public.module_test_responses USING (auth.jwt() ? 'service_role'); 