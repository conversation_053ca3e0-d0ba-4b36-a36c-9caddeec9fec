// vite-env-plugin.js
import fs from 'fs';
import path from 'path';

/**
 * Vite plugin to ensure environment variables are properly loaded
 * This is especially useful for Netlify deployments
 */
export default function viteEnvPlugin() {
  return {
    name: 'vite-env-plugin',
    configResolved(config) {
      // Check for Supabase environment variables
      const supabaseUrl = process.env.VITE_SUPABASE_URL || config.env?.VITE_SUPABASE_URL;
      const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || config.env?.VITE_SUPABASE_ANON_KEY;
      
      // Warn if environment variables are missing
      if (!supabaseUrl || !supabaseKey) {
        // In production mode, this is a critical error
        if (config.mode === 'production') {
          // Try to load from .env.production as a fallback
          try {
            const envPath = path.resolve(process.cwd(), '.env.production');
            if (fs.existsSync(envPath)) {
              const envContent = fs.readFileSync(envPath, 'utf-8');
              const envLines = envContent.split('\n');
              
              for (const line of envLines) {
                if (line.trim() && !line.startsWith('#')) {
                  const [key, ...valueParts] = line.split('=');
                  const value = valueParts.join('=');
                  if (key && value) {
                    process.env[key.trim()] = value.trim();
                  }
                }
              }
            }
          } catch (error) {
            console.error('Failed to load .env.production:', error);
          }
        }
      }
    }
  };
}
