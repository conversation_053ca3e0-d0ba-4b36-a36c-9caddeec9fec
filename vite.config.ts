import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { visualizer } from 'rollup-plugin-visualizer';
import viteEnvPlugin from "./vite-env-plugin";
import fs from 'fs';
import viteCompression from 'vite-plugin-compression';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env files based on mode
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  // Log environment mode
  console.log(`Building in ${mode} mode`);

  // Check for environment variables
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

  console.log('Environment variables:');
  console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Not set');
  console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? `Set (length: ${supabaseKey.length})` : 'Not set');

  // If in production mode and environment variables are missing, try to load from .env.production
  if (mode === 'production' && (!supabaseUrl || !supabaseKey)) {
    console.log('Attempting to load environment variables from .env.production');
    try {
      const envPath = path.resolve(process.cwd(), '.env.production');
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf-8');
        console.log('.env.production file found and loaded');
      }
    } catch (error) {
      console.error('Error loading .env.production:', error);
    }
  }

  return {
    base: '/',
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react(),
      mode === 'production' && viteCompression({
        algorithm: 'brotliCompress',
        ext: '.br',
        deleteOriginFile: false,
      }),
      mode === 'production' && viteCompression({
        algorithm: 'gzip',
        ext: '.gz',
        deleteOriginFile: false,
      }),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    optimizeDeps: {
      include: ['dompurify']
    },
    build: {
      commonjsOptions: {
        include: [/dompurify/, /node_modules/]
      },
      minify: 'esbuild',
      target: 'es2015',
      cssCodeSplit: true,
      chunkSizeWarningLimit: 1000,
      assetsDir: 'assets',
      manifest: true,
      rollupOptions: {
        output: {
          format: 'es',
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(assetInfo.name)) {
              return `assets/images/[name].[hash][extname]`;
            }
            if (/\.css$/.test(assetInfo.name)) {
              return `assets/css/[name].[hash][extname]`;
            }
            return `assets/[name].[hash][extname]`;
          },
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('react')) return 'vendor-react';
              if (id.includes('@supabase')) return 'vendor-supabase';
              return 'vendor';
            }
          }
        }
      },
      modulePreload: {
        polyfill: true
      }
    }
  };
});
